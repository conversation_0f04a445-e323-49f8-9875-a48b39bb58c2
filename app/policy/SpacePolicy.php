<?php

namespace app\policy;

use app\model\Space;
use app\model\SpaceMember;
use app\model\User;

class SpacePolicy
{

    public function developer(User $user, Space $space)
    {
        return $space->hasMember($user, SpaceMember::DEVELOPER);
    }

    public function master(User $user, Space $space)
    {
        return $space->hasMember($user, SpaceMember::MASTER);
    }

    public function owner(User $user, Space $space)
    {
        return $space->hasMember($user, SpaceMember::OWNER);
    }
}
