<?php

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use function Swoole\Coroutine\run;

class Test extends Command
{

    protected function configure()
    {
        $this->setName('test');
    }

    protected function execute(Input $input, Output $output)
    {
        if (defined('SWOOLE_VERSION')) {
            run(function () use ($output, $input) {
                parent::execute($input, $output);
            });
        } else {
            parent::execute($input, $output);
        }
    }

    public function handle()
    {
    }

}
