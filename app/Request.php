<?php

namespace app;

// 应用请求对象类
use app\model\Space;

class Request extends \think\Request
{
    protected $proxyServerIp       = ['127.0.0.1'];
    protected $proxyServerIpHeader = ['HTTP_X_ORIGINAL_FORWARDED_FOR', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'HTTP_X_CLIENT_IP', 'HTTP_X_CLUSTER_CLIENT_IP'];

    protected ?Space $space = null;

    public function setSpace(Space $space)
    {
        $this->space = $space;
    }

    public function getSpace()
    {
        return $this->space;
    }
}
