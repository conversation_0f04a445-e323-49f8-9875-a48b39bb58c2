<?php

namespace app\controller;

use app\BaseController;
use app\lib\License;
use app\model\Setting;
use think\helper\Arr;

class ManifestController extends BaseController
{
    public function index(License $license)
    {
        $cloud = config('cloud.enable');

        //授权检查
        if (!$cloud && !$license->isValid()) {
            abort(403, '应用尚未授权');
        }

        $socials = [
            [
                'name'  => 'qq',
                'label' => 'QQ',
            ],
            [
                'name'  => 'wechat',
                'label' => '微信',
            ],
            [
                'name'  => 'github',
                'label' => 'Github',
            ],
        ];

        $login = [
            'qrcode'   => Setting::read('login.qrcode'),
            'channels' => Arr::flatMap(function ($channel) {
                if (Setting::read("login.{$channel['name']}.enable", false)) {
                    return [$channel];
                }
                return [];
            }, $socials),
        ];

        return json([
            'website' => Setting::read('website'),
            'cloud'   => $cloud,
            'login'   => $login,
        ]);
    }

}
