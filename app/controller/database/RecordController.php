<?php

namespace app\controller\database;

use app\BaseController;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use think\db\Query;
use think\exception\ValidateException;
use think\Filesystem;
use think\helper\Arr;

class RecordController extends BaseController
{
    use WithDatabase;

    public function index()
    {
        $records = $this->database->runWithQuery(function (Query $query) {
            return $query->order('id', 'desc')->paginate();
        });
        return json($records);
    }

    public function delete($id)
    {
        $this->database->runWithQuery(function (Query $query) use ($id) {
            $query->where('id', $id)->delete();
        });
    }

    public function save()
    {
        $fields = $this->database->fields()->select();

        $validate = [];
        foreach ($fields as $field) {
            $validate["{$field->name}|{$field->label}"] = $field->required ? 'require' : '';
        }

        $data = $this->validate($validate);

        $data['user_id'] = $this->user->hash_id;

        $this->database->runWithQuery(function (Query $query) use ($data) {
            return $query->insert($data);
        });
    }

    public function update($id)
    {
        $fields = $this->database->fields()->select();

        $validate = [];
        foreach ($fields as $field) {
            $validate["{$field->name}|{$field->label}"] = $field->required ? 'require' : '';
        }

        $data = $this->validate($validate);

        $this->database->runWithQuery(function (Query $query) use ($id, $data) {
            $query->where('id', $id)->update($data);
        });
    }

    public function import(Filesystem $filesystem)
    {
        $data = $this->validate([
            'file' => 'require',
        ]);

        $path = $filesystem->disk('uploads')->path($data['file']);

        $reader      = new Xlsx();
        $spreadsheet = $reader->load($path);

        $sheet = $spreadsheet->getActiveSheet();

        $fields     = $this->database->fields()->select();
        $fieldNames = $fields->column('name');

        //获取第一行的数据
        $row = $sheet->getRowIterator()->current()->getCellIterator();
        $row->setIterateOnlyExistingCells(false);
        $columns = [];
        foreach ($row as $cell) {
            $name = $cell->getValue();
            if (!empty($name) && in_array($name, $fieldNames)) {
                $columns[$name] = $cell->getColumn();
            }
        }

        //检查是否包含所有必填字段
        foreach ($fields as $field) {
            if ($field->required && !array_key_exists($field->name, $columns)) {
                throw new \Exception("缺少必填字段{$field->name}");
            }
        }

        $dataset = [];
        foreach ($sheet->getRowIterator(2) as $row) {
            $data = [
                'user_id' => $this->user->hash_id,
            ];
            foreach ($columns as $field => $column) {
                $data[$field] = $sheet->getCell("{$column}{$row->getRowIndex()}")->getValue();
            }
            $dataset[] = $data;
        }

        if (!empty($dataset)) {
            $this->database->runWithQuery(function (Query $query) use ($dataset) {
                $query->insertAll($dataset);
            });
        }
    }

    public function export(Filesystem $filesystem)
    {
        $fields = $this->database->fields()->select();
        $data   = $this->database->runWithQuery(function (Query $query) {
            return $query->select();
        });

        if ($data->isEmpty()) {
            throw new ValidateException('没有数据可导出');
        }

        // 创建Spreadsheet对象
        $spreadsheet = new Spreadsheet();
        $sheet       = $spreadsheet->getActiveSheet();

        // 设置表头
        $names = $fields->column('name');
        $sheet->fromArray($names, null, 'A1');

        $source = [];
        foreach ($data as $datum) {
            $source[] = Arr::only($datum, $names);
        }

        $sheet->fromArray($source, null, 'A2');

        // 创建Excel写入器
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);

        $disk     = $filesystem->disk('uploads');
        $filename = $this->database->name . '数据_' . date('YmdHis') . '.xlsx';
        $path     = $disk->path("exports/{$filename}");
        $dir      = dirname($path);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }

        $writer->save($path);

        return json([
            'url' => $disk->url("exports/{$filename}"),
        ]);
    }
}
