<?php

namespace app\controller\database;

use app\controller\WithSpace;
use app\lib\Hashids;
use app\Request;

trait WithDatabase
{
    use WithSpace;

    /** @var \app\model\Database */
    protected $database;

    protected function initializeWithDatabase()
    {
        $this->middleware(fn() => $this->authorized('developer', $this->space));

        $this->middleware(function (Request $request, $next) {
            $routeName = $this->routeName ?? 'database_id';
            $id = $request->route($routeName);
            if ($id) {
                $this->database = $this->space->databases()->findOrFail(Hashids::decode($id));
            }
            return $next($request);
        });
    }
}
