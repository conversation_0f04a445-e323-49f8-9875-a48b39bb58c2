<?php

namespace app\controller\database;

use app\BaseController;
use app\lib\Hashids;

class IndexController extends BaseController
{
    use WithDatabase;

    protected $routeName = 'id';

    public function index()
    {
        $id = $this->request->param('id');

        $query = $this->space->databases()->order('id desc');

        if (!empty($id)) {
            return json($query->whereIn('id', array_map(function ($id) {
                return Hashids::decode($id);
            }, $id))->select());
        } else {
            return json($query->paginate());
        }
    }

    public function read()
    {
        return json($this->database);
    }

    public function save()
    {
        $this->space->checkQuota('database');

        $data = $this->validate([
            'name|名称'        => 'require',
            'description|描述' => '',
            'mode|权限模式'    => 'require|in:1,2,3',
        ]);

        $data['space_id'] = $this->space->id;
        $data['user_id']  = $this->user->id;
        $data['status']   = 0;

        return $this->space->databases()->save($data);
    }

    public function update()
    {
        $data = $this->validate([
            'name|名称'        => 'require',
            'description|描述' => '',
            'mode|权限模式'    => 'require|in:1,2,3',
        ]);

        $this->database->save($data);

        return $this->database;
    }

    public function delete()
    {
        $this->database->delete();
    }
}
