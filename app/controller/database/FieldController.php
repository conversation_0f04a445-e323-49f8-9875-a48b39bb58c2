<?php

namespace app\controller\database;

use app\BaseController;

class FieldController extends BaseController
{
    use WithDatabase;

    public function index()
    {
        $fields = $this->database->fields()->select();
        return json($fields);
    }

    public function save()
    {
        $data = $this->validate([
            'name|字段名'   => ['require', 'alphaDash', function ($value) {
                if (in_array($value, ['id', 'user_id', 'create_time'])) {
                    return '字段名不能为系统保留字段';
                }
                return true;
            }],
            'label|显示名'  => 'require',
            'type|类型'     => 'require|in:string,integer,float,boolean,datetime',
            'required|必填' => 'boolean',
        ]);

        $this->database->fields()->save($data);
    }

    public function update($id)
    {
        $field = $this->database->fields()->findOrFail($id);

        $data = $this->validate([
            'name|字段名'   => ['require', 'alphaDash', function ($value) {
                if (in_array($value, ['id', 'user_id', 'create_time'])) {
                    return '字段名不能为系统保留字段';
                }
                return true;
            }],
            'label|显示名'  => 'require',
            'type|类型'     => 'require|in:string,integer,float,boolean,datetime',
            'required|必填' => 'boolean',
        ]);

        $field->save($data);
    }

    public function delete($id)
    {
        $field = $this->database->fields()->findOrFail($id);

        $field->delete();
    }

    public function sync()
    {

    }

}
