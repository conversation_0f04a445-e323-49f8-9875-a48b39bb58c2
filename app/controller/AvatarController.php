<?php

namespace app\controller;

use A<PERSON>ram\Robohash\Robohash;

class AvatarController
{
    public function index($hash)
    {
        $image = Robohash::make($hash, 140, 'set1', 'any', 'any');
        $image->encode();

        return response($image->getEncoded())->header([
            'Cache-Control' => 'public, max-age=315360000',
            'Content-Type'  => 'image/png',
        ]);
    }

    public function plugin($id)
    {
        $file = app_path("lib/agent/plugin/{$id}") . 'icon.png';

        if (!file_exists($file)) {
            $file = app_path("lib/agent/plugin") . 'icon.png';
        }

        return \think\swoole\helper\file($file);
    }
}
