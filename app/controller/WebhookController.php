<?php

namespace app\controller;

use app\BaseController;
use app\job\WkfMessageJob;
use app\job\WmpMessageJob;
use app\lib\Cloud;
use app\lib\Hashids;
use app\lib\wechat\kf\Message as KfMessage;
use app\lib\wechat\mp\Message as MpMessage;
use app\model\Bot;
use app\model\BotQq;
use app\model\BotWkf;
use app\model\BotWmp;
use app\model\Order;
use Closure;

class WebhookController extends BaseController
{
    public function wkf($id)
    {
        $bot = Bot::where('id', Hashids::decode($id))->findOrFail();
        $wkf = BotWkf::where('bot_id', $bot->id)->findOrFail();

        $provider = $wkf->getProvider();
        $server   = $provider->getServer();
        $client   = $provider->getClient();

        $server->with(function (KfMessage $message, Closure $next) use ($client, $bot, $wkf) {
            $result = $client->post('cgi-bin/kf/sync_msg', [
                'json' => [
                    'token'  => $message->Token,
                    'cursor' => $wkf->cursor,
                ],
            ]);

            if ($result['errcode'] == 0) {
                $wkf->save(['cursor' => $result['next_cursor']]);

                $enabled = $wkf->bot->enabled(Bot::INTEGRATION_WKF);

                foreach ($result['msg_list'] as $msg) {
                    if ($msg['send_time'] <= $wkf->create_time->timestamp) {
                        continue;
                    }

                    if ($enabled) {
                        queue(WkfMessageJob::class, [$wkf->id, $msg]);
                    } else {
                        $client->post('cgi-bin/kf/send_msg', [
                            'json' => [
                                'touser'    => $msg['external_userid'],
                                'open_kfid' => $msg['open_kfid'],
                                'msgtype'   => 'text',
                                'text'      => [
                                    'content' => '[服务未开启]',
                                ],
                            ],
                        ]);
                    }
                }
            }

            return $next($message);
        });

        return $server->serve($this->request);
    }

    public function wmp($id)
    {
        $bot = Bot::where('id', Hashids::decode($id))->findOrFail();
        $wmp = BotWmp::where('bot_id', $bot->id)->findOrFail();

        $provider = $wmp->getProvider();
        $server   = $provider->getServer();

        $server->with(function (MpMessage $message, Closure $next) use ($bot, $wmp) {

            $from    = $message->FromUserName;
            $content = $message->Content;

            $enabled = $wmp->bot->enabled(Bot::INTEGRATION_WMP);

            if ($enabled) {
                if ($message->MsgType == 'text') {
                    queue(WmpMessageJob::class, [$wmp->id, $from, $content]);
                }
            } else {
                return '[服务未开启]';
            }

            return $next($message);
        });

        return $server->serve($this->request);
    }

    public function lark($id)
    {

    }

    public function qq($id)
    {
        $bot = Bot::where('id', Hashids::decode($id))->findOrFail();
        $qq  = BotQq::where('bot_id', $bot->id)->findOrFail();

        return $qq->getClient()->serve($this->request);
    }

    //订单回调
    public function order(Cloud $cloud)
    {
        $data = $cloud->verifyChargeNotify($this->request);

        $order = Order::findOrFail(Hashids::decode($data['order_no']));

        $order->paid();
    }
}
