<?php

namespace app\controller;

use app\lib\Hashids;
use think\Request;

trait WithSpace
{
    /** @var \app\model\Space */
    protected $space;

    protected function initializeWithSpace()
    {
        $this->middleware(function (Request $request, $next) {
            $routeName = $this->spaceId ?? 'space_id';
            $id        = $request->route($routeName);
            if ($id) {
                $id    = Hashids::decode($id);
                $space = $this->request->getSpace();
                if ($space) {
                    if ($space->id != $id) {
                        abort(404);
                    }
                    $this->space = $space;
                } else {
                    $this->space = $this->user->spaces()->where('space.id', $id)->findOrFail();
                }
            }
            return $next($request);
        });
    }
}
