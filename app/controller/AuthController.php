<?php

namespace app\controller;

use app\BaseController;
use app\model\Setting;
use app\model\User;
use TopThinkCloud\Client;
use yunwuxin\Social;

class AuthController extends BaseController
{
    public function current()
    {
        $user = $this->user->append(['is_admin', 'token']);
        return json($user);
    }

    protected function getChannel($channel)
    {
        return $this->app->make(Social::class)
            ->channel($channel, Setting::read("login.{$channel}", []));
    }

    public function login($channel)
    {
        $url = $this->getChannel($channel)->getAuthUrl();

        return json(['url' => $url]);
    }

    public function register()
    {
        $data = $this->validate([
            'channel'      => 'require',
            'code'         => 'require',
            'redirect_uri' => 'require',
        ]);

        $socialUser = $this->getChannel($data['channel'])
            ->setRedirectUrl($data['redirect_uri'])
            ->user($data['code']);

        $user = User::getBySocialUser($socialUser);

        return json([
            'token' => $user->token,
        ]);
    }

    public function qrcode(Client $client)
    {
        $res = $client->passport()->login(Setting::read('website.title'));

        return json($res);
    }

    public function check(Client $client, $token)
    {
        $user = $client->passport()->user($token);

        if (empty($user)) {
            abort(449);
        }

        $socialUser = \yunwuxin\social\User::make($user, []);
        $socialUser->setChannel('passport');

        $user = User::getBySocialUser($socialUser);

        return json([
            'token' => $user->token,
        ]);
    }
}
