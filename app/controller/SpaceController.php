<?php

namespace app\controller;

use app\BaseController;
use app\lib\Date;
use app\model\Space;
use app\model\SpaceMember;
use think\exception\ValidateException;

class SpaceController extends BaseController
{
    use WithSpace;

    protected $spaceId = 'id';

    public function index()
    {
        $spaces = $this->user->spaces;
        return json($spaces);
    }

    public function save()
    {
        $data = $this->validate([
            'name|空间名称' => 'require',
        ]);

        $cloud = config('cloud.enable');

        $space = Space::create([
            ...$data,
            'plan'        => $cloud ? 'trial' : 'enterprise',
            'expire_time' => $cloud ? Date::now()->addDays(7) : null,
        ]);

        $space->addMember($this->user, SpaceMember::OWNER);

        return json($space);
    }

    public function update()
    {
        $this->authorized('master', $this->space);

        $data = $this->validate([
            'name|空间名称' => 'require',
        ]);

        $this->space->save($data);

        return json($this->space);
    }

    public function delete()
    {

        $this->authorized('owner', $this->space);

        $data = $this->validate([
            'name|空间名称' => 'require',
        ]);

        if ($data['name'] !== $this->space->name) {
            throw new ValidateException(['name' => '请输入正确的空间名称']);
        }

        $this->space->delete();
    }

}
