<?php

namespace app\controller;

use app\BaseController;
use app\model\Space;
use app\model\User;
use think\Cache;

class InviteController extends BaseController
{
    public function read(Cache $cache, $code)
    {
        $invite = $cache->get("invite#{$code}");

        if (empty($invite) || !is_array($invite)) {
            abort(404);
        }

        return json([
            'space' => Space::findOrFail($invite['space']),
            'user'  => User::findOrFail($invite['user']),
        ]);
    }

    public function save(Cache $cache, $code)
    {
        $invite = $cache->get("invite#{$code}");

        if (empty($invite) || !is_array($invite)) {
            abort(404);
        }

        $space = Space::findOrFail($invite['space']);

        $space->addMember($this->user, $invite['access_level']);

        $cache->delete("invite#{$code}");
        return json($space);
    }
}
