<?php

namespace app\controller\dataset;

use app\BaseController;
use app\lib\Hashids;
use think\Filesystem;

class SourceController extends BaseController
{
    use WithDataset;

    public function index()
    {
        $query = $this->dataset->sources()->withoutField('content')->order('id desc');

        $this->searchField($query, 'title');

        $sources = $query->paginate();
        return json($sources);
    }

    public function read($id)
    {
        $source = $this->dataset->sources()->withoutField('content')->findOrFail(Hashids::decode($id));
        return json($source);
    }

    public function save(Filesystem $filesystem)
    {
        if (!$this->dataset->isCommon()) {
            abort(404);
        }
        $data = $this->validate([
            'type'  => 'require|in:text,file,qa,url',
            'title' => '',
            'text'  => function ($value, $data) {
                if ($data['type'] == 'text') {
                    if (empty($value)) {
                        return '文本内容不能为空';
                    }
                    if (mb_strlen($value) > 10000) {
                        return '文本内容不能超过10000个字符';
                    }
                }
                return true;
            },
            'file'  => function ($value, $data) {
                if ($data['type'] == 'file') {
                    if (empty($value)) {
                        return '文件不能为空';
                    }
                }
                return true;
            },
            'qa'    => function ($value, $data) {
                if ($data['type'] == 'qa') {
                    if (empty($value)) {
                        return '文件不能为空';
                    }
                }
                return true;
            },
            'url'   => function ($value, $data) {
                if ($data['type'] == 'url') {
                    if (empty($value)) {
                        return '网页地址不能为空';
                    }
                }
                return true;
            },
        ]);

        $disk = $filesystem->disk('uploads');

        switch ($data['type']) {
            case 'text':
                $this->dataset->sources()->save([
                    'type'    => 'text',
                    'title'   => $data['title'] ?? '手动输入',
                    'content' => $data['text'],
                    'size'    => strlen($data['text']),
                ]);
                break;
            case 'file':
                $file = $data['file'];
                $this->dataset->sources()->save([
                    'type'    => 'file',
                    'title'   => $file['name'],
                    'content' => $file['value'],
                    'size'    => $disk->fileSize($file['value']),
                ]);
                break;
            case 'qa':
                $file = $data['qa'];
                $this->dataset->sources()->save([
                    'type'    => 'qa',
                    'title'   => $file['name'],
                    'content' => $file['value'],
                    'size'    => $disk->fileSize($file['value']),
                ]);
                break;
            case 'url':
                $urls = array_filter(explode("\n", $data['url']));
                foreach (array_slice($urls, 0, 20) as $url) {
                    $this->dataset->sources()->save([
                        'type'    => 'url',
                        'title'   => $url,
                        'content' => $url,
                        'size'    => 0,
                    ]);
                }
                break;
            default:
                abort(400, '未知的数据类型');
        }
    }

    public function train($id)
    {
        $source = $this->dataset->sources()->where('status', -1)->findOrFail($id);
        $source->retrain();
    }

    public function delete($id)
    {
        $source = $this->dataset->sources()->where('status', '<>', 2)->findOrFail($id);
        $source->delete();
    }
}
