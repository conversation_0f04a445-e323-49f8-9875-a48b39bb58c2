<?php

namespace app\controller\dataset;

use app\controller\WithSpace;
use app\lib\Hashids;
use app\Request;

trait WithDataset
{
    use WithSpace;

    /** @var \app\model\Dataset */
    protected $dataset;

    protected function initializeWithDataset()
    {
        $this->middleware(fn() => $this->authorized('developer', $this->space));

        $this->middleware(function (Request $request, $next) {
            $routeName = $this->routeName ?? 'dataset_id';
            $id        = $request->route($routeName);
            if ($id) {
                $this->dataset = $this->space->datasets()->findOrFail(Hashids::decode($id));
            }
            return $next($request);
        });
    }
}
