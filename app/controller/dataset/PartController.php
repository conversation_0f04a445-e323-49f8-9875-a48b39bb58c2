<?php

namespace app\controller\dataset;

use app\BaseController;
use app\lib\Hashids;
use app\Request;
use Qdrant\Models\Filter\Condition\MatchInt;
use Qdrant\Models\Filter\Filter;
use Qdrant\Models\Request\ScrollRequest;
use think\helper\Arr;

class PartController extends BaseController
{
    use WithDataset;

    /** @var \app\model\DatasetSource */
    protected $source;

    protected function initialized()
    {
        $this->middleware(function (Request $request, $next) {
            $id = $request->route('source_id');
            if ($id) {
                $this->source = $this->dataset->sources()->findOrFail(Hashids::decode($id));
            }
            return $next($request);
        });
    }

    public function index($offset = null)
    {
        $collection = $this->dataset->collection();

        $points = $collection->points();

        $scrollRequest = new ScrollRequest();
        $scrollRequest->setLimit(12);
        $scrollRequest->setWithVector(true);
        $filter = new Filter();
        $filter->addMust(new MatchInt('source', $this->source->id));
        $scrollRequest->setFilter($filter);

        if ($offset) {
            $scrollRequest->setOffset($offset);
        }

        $response = $points->scroll($scrollRequest);

        $result = Arr::get($response, 'result');
        return json($result);
    }
}
