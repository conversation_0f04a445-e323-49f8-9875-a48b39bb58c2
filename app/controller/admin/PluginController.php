<?php

namespace app\controller\admin;

use app\model\Plugin;

class PluginController extends Controller
{
    public function index()
    {
        $plugins = Plugin::order('id desc')->whereNull('space_id')->paginate();
        return json($plugins);
    }

    public function status($id)
    {
        $plugin = Plugin::findOrFail($id);

        $data = $this->validate([
            'status' => 'require',
        ]);

        $plugin->save([
            'status' => $data['status'],
        ]);
    }

    public function sort($id)
    {
        $plugin = Plugin::findOrFail($id);

        $data = $this->validate([
            'sort' => 'require',
        ]);

        $plugin->save([
            'sort' => $data['sort'],
        ]);
    }

}
