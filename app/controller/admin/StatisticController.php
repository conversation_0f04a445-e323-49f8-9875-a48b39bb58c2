<?php

namespace app\controller\admin;

use app\lib\Date;
use app\lib\License;
use app\model\Bot;
use app\model\Conversation;
use app\model\Message;
use app\model\Space;
use app\model\User;
use think\db\Query;

class StatisticController extends Controller
{
    public function basic(License $license)
    {
        $user = [
            'total'     => User::count(),
            'yesterday' => User::whereBetween('create_time', [Date::yesterday(), Date::today()])->count(),
        ];

        $space = [
            'total'     => Space::count(),
            'yesterday' => Space::whereBetween('create_time', [Date::yesterday(), Date::today()])->count(),
        ];

        $bot = [
            'total'     => Bot::count(),
            'yesterday' => Bot::whereBetween('create_time', [Date::yesterday(), Date::today()])->count(),
        ];

        if (config('cloud.enable')) {
            $license = [
                'plan'   => 'SAAS版',
                'tokens' => null,
            ];
        } else {
            $info = $license->info();
            if (empty($info)) {
                $license = [
                    'plan'   => '未授权',
                    'tokens' => 0,
                ];
            } else {
                $license = [
                    'plan'   => $info['plan']['label'],
                    'tokens' => $info['token'],
                ];
            }
        }

        return json(compact('user', 'space', 'bot', 'license'));
    }

    public function user($period = '30days')
    {
        $data = $this->getPeriodData(
            $period,
            User::field('COUNT(*) as value')
        );
        return json($data);
    }

    public function chat($period = '30days')
    {
        $conversation = $this->getPeriodData($period, Conversation::field('COUNT(*) as value'));
        $message      = $this->getPeriodData($period, Message::field('COUNT(*) as value'));

        return json(compact('conversation', 'message'));
    }

    /**
     * @param $period
     * @param Query|\think\Model $query
     * @param $field
     * @return array
     */
    protected function getPeriodData($period, $query, $field = 'create_time')
    {
        [$start, $end, $unit, $list] = get_period($period);

        $dateField = get_date_query($field, $unit);

        $data = $query
            ->field("{$dateField} as date")
            ->whereBetween($field, [$start, $end])
            ->group('date')
            ->order('date asc')
            ->select()
            ->append([])
            ->visible([]);

        return fill_data($list, $data);
    }
}
