<?php

namespace app\controller\admin;

use app\model\Space;

class SpaceController extends Controller
{
    public function index()
    {
        $query = Space::order('id desc');

        $this->searchField($query, 'name');

        $spaces = $query->paginate()->append(['owner'], true);
        return json($spaces);
    }

    public function plan($id)
    {
        $space = Space::findOrFail($id);

        $cloud = config('cloud.enable');

        if ($cloud) {
            $validate = [
                'plan'        => 'require',
                'expire_time' => 'require',
                'token'       => 'require',
            ];
        } else {
            $validate = [
                'token' => 'require',
            ];
        }

        $data = $this->validate($validate);

        $space->save($data);
    }
}
