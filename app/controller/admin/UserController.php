<?php

namespace app\controller\admin;

use app\model\User;
use think\exception\ValidateException;

class UserController extends Controller
{
    public function index()
    {
        $users = User::order('id desc')->paginate();
        return json($users);
    }

    public function status($id)
    {
        $user = User::findOrFail($id);

        $data = $this->validate([
            'status' => 'require',
        ]);

        //至少保留一个管理员
        if (
            $this->user->id == $id &&
            $user->status & User::STATUS_ADMIN &&
            !($data['status'] & User::STATUS_ADMIN)
        ) {
            throw new ValidateException('不能取消自身管理员');
        }

        return $user->save($data);
    }
}
