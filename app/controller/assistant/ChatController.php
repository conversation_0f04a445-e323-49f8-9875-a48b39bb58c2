<?php

namespace app\controller\assistant;

use app\BaseController;
use app\controller\WithSpace;
use app\lib\bot\Assistant;
use app\model\Conversation;
use function think\swoole\helper\iterator;

class ChatController extends BaseController
{
    use WithSpace;

    public function index()
    {
        $conversation = Conversation::recent()
            ->where('space_id', $this->space->id)
            ->where('bot_id', 0)
            ->where('user_id', $this->user->hash_id)
            ->with(['messages'])
            ->find();

        return json([
            'conversation' => $conversation,
        ]);
    }

    public function save()
    {
        $params = $this->validate([
            'query'        => '',
            'files'        => '',
            'variables'    => '',
            'conversation' => '',
        ]);

        $params['user'] = $this->user->hash_id;

        $this->space->checkQuota('token', 'Token额度不足');

        $assistant = new Assistant($this->space);

        $result = $assistant->run($params);

        $result->rewind();
        $generator = function () use ($result) {
            while ($result->valid()) {
                yield 'data: ' . json_encode($result->current()) . "\n\n";
                $result->next();
            }
            yield "data: [DONE]\n\n";
        };

        $response = iterator($generator());

        return $response->header([
            'Content-Type'      => 'text/event-stream',
            'Cache-Control'     => 'no-cache, must-revalidate',
            'X-Accel-Buffering' => 'no',
        ]);
    }

}
