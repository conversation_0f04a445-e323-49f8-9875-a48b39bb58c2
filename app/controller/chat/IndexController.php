<?php

namespace app\controller\chat;

use app\BaseController;
use app\lib\Date;
use app\lib\Hashids;
use app\model\Bot;
use app\model\BotEmbed;
use app\model\BotToken;
use app\model\Conversation;
use Firebase\JWT\JWT;
use Ramsey\Uuid\Uuid;
use think\Filesystem;

class IndexController extends BaseController
{
    use WithBot;

    public function read($id)
    {
        //使用share id鉴权
        if (str_starts_with($id, 's-')) {
            $token = BotToken::getByToken(substr($id, 2), true);
            if (empty($token)) {
                abort(404, '链接不存在');
            }
            if (!$token->isAvailable()) {
                abort(403, '链接已过期');
            }

            $token->save(['last_time' => Date::now()]);

            $bot = $token->bot;

            $this->checkBot($bot, Bot::INTEGRATION_SHARE);

            $exp    = min(time() + 24 * 60 * 60, $token->expire_time ? $token->expire_time->timestamp : PHP_INT_MAX);
            $source = 'share';
        } else {
            $url = $this->request->param('url');

            if (empty($url)) {
                abort(403, '应嵌入到某个网站中使用');
            }

            /** @var Bot $bot */
            $bot = Bot::where('id', Hashids::decode($id))->find();

            $this->checkBot($bot, Bot::INTEGRATION_EMBED);

            $embed = BotEmbed::where('bot_id', $bot->id)->find();

            if ($embed) {
                if (!$embed->checkDomain(parse_url($url, PHP_URL_HOST))) {
                    abort(403, '域名不在白名单中');
                }
                $options = $embed->options;
            }

            $exp    = time() + 24 * 60 * 60;
            $source = 'embed';
        }

        $ip       = $this->request->ip();
        $clientId = $this->request->param('client_id');

        $userId = Uuid::uuid5(Uuid::NAMESPACE_DNS, "{$ip}#{$clientId}#{$bot->space_id}");

        $conversation = Conversation::recent()
            ->where('space_id', $bot->space_id)
            ->where('bot_id', $bot->id)
            ->where('user_id', $userId)
            ->with(['messages'])
            ->find();

        $token = JWT::encode([
            'exp'     => $exp,
            'iat'     => time(),
            'id'      => $bot->id,
            'user_id' => $userId,
            'source'  => $source,
        ], config('app.token'), 'HS256');

        return json([
            'bot'          => $bot->hidden(['config']),
            'config'       => $bot->feature,
            'options'      => $options ?? null,
            'conversation' => $conversation,
            'token'        => $token,
        ]);
    }

    public function save()
    {
        $this->bot->space->checkQuota('token', '服务不可用');

        $params = $this->validate([
            'query'        => '',
            'files'        => '',
            'variables'    => '',
            'conversation' => '',
            'user'         => '',
            'stream'       => '',
        ]);

        if (!empty($this->userId) && empty($params['user'])) {
            $params['user'] = $this->userId;
        }

        return $this->bot->chat($this->source, $params);
    }

    public function suggestion()
    {
        $this->bot->space->checkQuota('token', '服务不可用');

        $params = $this->validate([
            'conversation' => 'require',
        ]);

        $suggestions = $this->bot->suggestion($params['conversation']);

        return json($suggestions);
    }

    public function config()
    {
        return json($this->bot->feature);
    }

    public function upload(Filesystem $filesystem)
    {
        $data = $this->validate([
            'file' => 'require',
        ]);

        $disk = $filesystem->disk('uploads');
        /** @var \think\file\UploadedFile $file */
        $file = $data['file'];
        $path = $disk->putFile('chat', $file);

        $result = [
            'name' => $file->getOriginalName(),
            'size' => $file->getSize(),
            'path' => $path,
        ];

        return json($result);
    }

}
