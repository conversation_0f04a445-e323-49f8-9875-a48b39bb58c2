<?php

namespace app\controller\chat;

use app\BaseController;
use app\model\Conversation;

class ConversationController extends BaseController
{
    use WithBot;

    public function index($offset = null)
    {
        $userId = $this->userId ?: $this->request->param('user');

        $query = Conversation::where('bot_id', $this->bot->id)
            ->where('user_id', $userId)
            ->where('source', $this->source)
            ->with(['messages'])
            ->order('id', 'desc')
            ->limit(10);

        if ($offset) {
            $query->where('id', '<', $offset);
        }

        $conversations = $query->select();

        return json($conversations);
    }

    public function recent()
    {
        $userId = $this->userId ?: $this->request->param('user');

        $recent = Conversation::recent()
            ->where('bot_id', $this->bot->id)
            ->where('user_id', $userId)
            ->where('source', $this->source)
            ->with(['messages'])
            ->find();

        return json($recent);
    }

    public function delete($id)
    {
        $userId = $this->userId ?: $this->request->param('user');

        $conversation = Conversation::where('bot_id', $this->bot->id)
            ->where('user_id', $userId)
            ->where('source', $this->source)
            ->findOrFail($id);

        $conversation->delete();
    }
}
