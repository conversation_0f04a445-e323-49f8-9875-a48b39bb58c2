<?php

namespace app\controller\chat;

use app\lib\Date;
use app\model\Bot;
use app\model\BotToken;
use app\Request;
use Exception;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use think\helper\Str;
use yunwuxin\auth\exception\AuthenticationException;

/**
 * @property Bot $bot
 */
trait WithBot
{
    protected $source = null;
    protected $userId = null;

    private ?Bot $_bot = null;

    protected function initializeWithBot()
    {
        $this->middleware(function (Request $request, $next) {
            //鉴权
            try {
                $authorization = $this->request->header('Authorization');
                if (!empty($authorization)) {
                    if (Str::startsWith($authorization, 'Bearer ')) {
                        //api key
                        $token = BotToken::getByToken(Str::substr($authorization, 7));
                        if (empty($token)) {
                            abort(401, 'API Key 不存在');
                        }
                        $token->save(['last_time' => Date::now()]);
                        $bot = $token->bot;

                        switch ($token->type) {
                            case BotToken::TYPE_API:
                                $this->checkBot($bot, Bot::INTEGRATION_API);
                                $this->source = 'api';
                                break;
                            case BotToken::TYPE_CHAT:
                                $this->checkBot($bot, Bot::INTEGRATION_CHAT);
                                $this->source = 'chat';
                                break;
                            default:
                                abort(401, 'API Key 不存在');
                        }
                    } elseif (Str::startsWith($authorization, 'Token ')) {
                        //令牌
                        $token   = Str::substr($authorization, 6);
                        $decoded = (array) JWT::decode($token, new Key(config('app.token'), 'HS256'));
                        $bot     = Bot::where('id', $decoded['id'])->find();

                        $this->checkBot($bot);

                        $this->source = $decoded['source'] ?? null;
                        $this->userId = $decoded['user_id'] ?? null;
                    }

                    if (!empty($bot)) {
                        $this->bot = $bot;
                    }
                }
            } catch (Exception) {

            }
            return $next($request);
        });
    }

    /**
     * @param Bot $bot
     * @param $integration
     * @return void
     */
    protected function checkBot($bot, $integration = null)
    {
        if (empty($bot) || !$bot->isAvailable()) {
            abort(404, '服务未就绪');
        }
        if ($integration != null && !$bot->enabled($integration)) {
            abort(404, '服务未启用');
        }
    }

    public function __get(string $name)
    {
        if ($name == 'bot') {
            if (empty($this->_bot)) {
                throw new AuthenticationException();
            }
            return $this->_bot;
        }
        return null;
    }
}
