<?php

namespace app\controller\chat;

use app\BaseController;
use think\ai\Client;
use think\helper\Arr;

class AudioController extends BaseController
{
    use WithBot;

    public function speech(Client $client)
    {
        $this->bot->space->checkQuota('token', '服务不可用');

        $data = $this->validate([
            'model' => 'require',
            'input' => 'require',
            'voice' => 'require',
        ]);

        $result = $client->audio()->speech($data);

        $usage = Arr::get($result, 'usage.total_tokens');

        $this->bot->space->consumeToken($usage);

        return json([
            'audio' => $result['audio'],
        ]);
    }

    public function transcriptions(Client $client)
    {
        $this->bot->space->checkQuota('token', '服务不可用');

        $data = $this->validate([
            'model' => 'require',
            'file'  => 'require|file',
        ]);

        $data['language'] = 'zh';

        $result = $client->audio()->transcriptions($data);

        $usage = Arr::get($result, 'usage.total_tokens');

        $this->bot->space->consumeToken($usage);

        return json([
            'text' => $result['text'],
        ]);
    }

}
