<?php

namespace app\controller\bot\integration;

use app\BaseController;
use app\controller\bot\WithBot;

class KeyController extends BaseController
{
    use WithBot;

    public function initialized()
    {
        $this->middleware(fn() => $this->authorized('developer', $this->space));
    }

    public function index()
    {
        $tokens = $this->bot->tokens()->where('type', 1)->order('id desc')->paginate();
        return json($tokens);
    }

    public function save()
    {
        $data = $this->validate([
            'name|名称' => 'require',
        ]);

        $data['type'] = 1;

        $this->bot->tokens()->save($data);
    }

    public function delete($id)
    {
        $key = $this->bot->tokens()->where('type', 1)->findOrFail($id);

        $key->delete();
    }
}
