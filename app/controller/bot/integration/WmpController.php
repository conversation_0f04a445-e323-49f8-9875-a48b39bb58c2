<?php

namespace app\controller\bot\integration;

use app\BaseController;
use app\controller\bot\WithBot;
use app\model\BotWmp;

class WmpController extends BaseController
{
    use WithBot;

    public function initialized()
    {
        $this->middleware(fn() => $this->authorized('developer', $this->space));
    }

    public function index()
    {
        $wmp = BotWmp::where('bot_id', $this->bot->id)->find();

        return json([
            'ip'   => get_server_ip(),
            'data' => $wmp,
        ]);
    }

    public function save()
    {
        $data = $this->validate([
            'token|Token'            => '',
            'aes_key|EncodingAESKey' => '',
            'app_id|AppID'           => '',
            'app_secret|AppSecret'   => '',
        ]);

        $data['bot_id'] = $this->bot->id;

        BotWmp::create($data, replace: true);
    }
}
