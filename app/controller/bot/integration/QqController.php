<?php

namespace app\controller\bot\integration;

use app\BaseController;
use app\controller\bot\WithBot;
use app\model\BotQq;

class QqController extends BaseController
{
    use WithBot;

    public function initialized()
    {
        $this->middleware(fn() => $this->authorized('developer', $this->space));
    }

    public function index()
    {
        $qq = BotQq::where('bot_id', $this->bot->id)->find();

        return json([
            'ip'   => get_server_ip(),
            'data' => $qq,
        ]);
    }

    public function save()
    {
        $data = $this->validate([
            'app_id|AppID'         => '',
            'app_secret|AppSecret' => '',
        ]);

        $data['bot_id'] = $this->bot->id;

        BotQq::create($data, replace: true);
    }
}
