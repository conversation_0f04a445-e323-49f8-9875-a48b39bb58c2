<?php

namespace app\controller\bot\integration;

use app\BaseController;
use app\controller\bot\WithBot;
use app\lib\Date;

class ShareController extends BaseController
{
    use WithBot;

    public function initialized()
    {
        $this->middleware(fn() => $this->authorized('developer', $this->space));
    }

    public function index()
    {
        $tokens = $this->bot->tokens()->where('type', 2)->order('id desc')->paginate();
        return json($tokens);
    }

    public function save()
    {
        $data = $this->validate([
            'name|名称'            => 'require',
            'expire_time|过期时间' => 'require',
        ]);

        $data['expire_time'] = match ($data['expire_time']) {
            '1day' => Date::now()->addDay(),
            '7days' => Date::now()->addDays(7),
            '30days' => Date::now()->addDays(30),
            default => null
        };

        $data['type'] = 2;

        $this->bot->tokens()->save($data);
    }

    public function delete($id)
    {
        $share = $this->bot->tokens()->where('type', 2)->findOrFail($id);

        $share->delete();
    }
}
