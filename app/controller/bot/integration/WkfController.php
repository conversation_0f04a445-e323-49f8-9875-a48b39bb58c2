<?php

namespace app\controller\bot\integration;

use app\BaseController;
use app\controller\bot\WithBot;
use app\model\BotWkf;

class WkfController extends BaseController
{
    use WithBot;

    public function initialized()
    {
        $this->middleware(fn() => $this->authorized('developer', $this->space));
    }

    public function index()
    {
        $wkf = BotWkf::where('bot_id', $this->bot->id)->find();

        return json([
            'data' => $wkf,
        ]);
    }

    public function save()
    {
        $data = $this->validate([
            'token|Token'            => '',
            'aes_key|EncodingAESKey' => '',
            'corp_id|企业 ID'        => '',
            'secret|Secret'          => '',
        ]);

        $data['bot_id'] = $this->bot->id;

        BotWkf::create($data, replace: true);
    }
}
