<?php

namespace app\controller\bot\integration;

use app\BaseController;
use app\controller\bot\WithBot;
use app\model\BotEmbed;

class EmbedController extends BaseController
{
    use WithBot;

    public function initialized()
    {
        $this->middleware(fn() => $this->authorized('developer', $this->space));
    }

    public function index()
    {
        $embed = BotEmbed::where('bot_id', $this->bot->id)->find();
        return json($embed);
    }

    public function save()
    {
        $data = $this->validate([
            'options'          => '',
            'domain_whitelist' => '',
        ]);

        $data['bot_id'] = $this->bot->id;

        BotEmbed::create($data, replace: true);
    }
}
