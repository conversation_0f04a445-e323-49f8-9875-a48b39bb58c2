<?php

namespace app\controller\bot;

use app\BaseController;
use think\ai\Client;
use think\facade\Filesystem;
use think\helper\Arr;

class AudioController extends BaseController
{
    use WithBot;

    public function initialized()
    {
        $this->middleware(fn() => $this->authorized('developer', $this->space));
    }

    public function speech(Client $client)
    {
        $this->space->checkQuota('token', 'Token额度不足');

        $data = $this->validate([
            'model' => 'require',
            'input' => 'require',
            'voice' => 'require',
        ]);

        $result = $client->audio()->speech($data);

        $usage = Arr::get($result, 'usage.total_tokens');

        $this->space->consumeToken($usage);

        return json([
            'audio' => $result['audio'],
        ]);
    }

    public function transcriptions(Client $client)
    {
        $this->space->checkQuota('token', 'Token额度不足');

        $data = $this->validate([
            'model' => 'require',
            'file'  => 'require|file',
        ]);

        $disk = Filesystem::disk('uploads');
        $path = $disk->putFile('audio', $data['file']);

        try {
            $url = (string) url($disk->url($path))->domain(true);

            $result = $client->audio()->transcriptions([
                'model'    => $data['model'],
                'url'      => $url,
                'language' => 'zh',
            ]);

            $usage = Arr::get($result, 'usage.total_tokens');

            $this->space->consumeToken($usage);

            return json([
                'text' => $result['text'],
            ]);
        } finally {
            $disk->delete($path);
        }
    }

}
