<?php

namespace app\controller\bot;

use app\BaseController;
use app\controller\WithSpace;
use app\model\Plugin;
use app\model\PluginCredentials;
use ArrayObject;
use think\agent\Credentials;

class CredentialsController extends BaseController
{
    use WithSpace;

    public function initialized()
    {
        $this->middleware(fn() => $this->authorized('developer', $this->space));
    }

    public function index()
    {
        $credentials = new ArrayObject($this->space->getPluginCredentials());
        return json($credentials);
    }

    public function save()
    {
        $plugin = Plugin::resolve($this->space, $this->request->param('plugin'));

        $pluginCredentials = $plugin?->getCredentials();
        if (empty($pluginCredentials)) {
            abort(404);
        }

        $data = $this->validate(array_map(function ($value) {
            if ($value['required'] ?? false) {
                return 'require';
            }
            return '';
        }, $pluginCredentials), params: $this->request->param('credentials'));

        /** @var PluginCredentials $credentials */
        $credentials = PluginCredentials::where('space_id', $this->space->id)
            ->where('plugin_name', $plugin->getName())->find();

        if ($credentials) {
            $credentials->save([
                'credentials' => $credentials->credentials ?
                    $credentials->credentials->set($data, $pluginCredentials) :
                    Credentials::make($data, $pluginCredentials),
            ]);
        } else {
            PluginCredentials::create([
                'space_id'    => $this->space->id,
                'plugin_name' => $plugin->getName(),
                'credentials' => Credentials::make($data, $pluginCredentials),
            ]);
        }
    }

    public function delete()
    {
        $plugin = $this->request->param('plugin');

        PluginCredentials::where('space_id', $this->space->id)
            ->where('plugin_name', $plugin)
            ->delete();
    }
}
