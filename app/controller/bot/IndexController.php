<?php

namespace app\controller\bot;

use app\BaseController;

class IndexController extends BaseController
{
    use WithBot;

    protected $routeName = 'id';

    public function initialized()
    {
        $this->middleware(fn() => $this->authorized('developer', $this->space))
            ->only(['save', 'update', 'delete', 'config']);
    }

    public function index()
    {
        $bots = $this->space->bots()->order('id desc')->paginate(16);
        return json($bots);
    }

    public function save()
    {
        $this->space->checkQuota('bot');

        $data = $this->validate([
            'type|类型'        => 'require',
            'name|名称'        => 'require|length:2,25',
            'description|描述' => '',
        ]);

        return $this->space->bots()->save($data);
    }

    public function update()
    {
        $data = $this->validate([
            'name|名称'        => 'length:2,25',
            'description|描述' => '',
            'avatar|头像'      => '',
            'integration'      => '',
        ]);

        $this->bot->save($data);

        return json($this->bot);
    }

    public function read()
    {
        return json($this->bot);
    }

    public function delete()
    {
        $this->bot->delete();
    }

    public function config()
    {
        $data = $this->validate([
            'config' => 'require',
        ]);

        $this->bot->save($data);
    }

    public function search()
    {
        $query = $this->space->bots()->order('id desc');

        $this->filterFields($query, [
            'type' => true,
            'name',
        ]);

        $bots = $query->select();
        return json($bots);
    }
}
