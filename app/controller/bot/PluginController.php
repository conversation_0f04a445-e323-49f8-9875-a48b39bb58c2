<?php

namespace app\controller\bot;

use app\BaseController;
use app\lib\Hashids;
use app\model\Plugin;
use think\db\Query;

class PluginController extends BaseController
{
    use WithBot;

    public function initialized()
    {
        $this->middleware(fn() => $this->authorized('developer', $this->space));
    }

    public function index($name = null)
    {
        $query = Plugin::order('sort desc,type desc')->where('status', 1)->where(function (Query $query) {
            $query->whereOr('space_id', null);
            $query->whereOr('space_id', $this->space->id);
        });

        if (!empty($name)) {
            $name = explode(',', $name);

            $query->where(function (Query $query) use ($name) {
                foreach ($name as $item) {
                    if (str_starts_with($item, 'custom-')) {
                        $query->whereOr('id', Hashids::decode(substr($item, 7)));
                    } else {
                        if (str_starts_with($item, 'plugin-')) {
                            $item = substr($item, 7);
                        }
                        $query->whereOr('name', $item);
                    }
                }
            });
        }

        $plugins = $query->append(['tools', 'credentials'], true)->hidden(['config'], true)->select();
        return json($plugins);
    }
}
