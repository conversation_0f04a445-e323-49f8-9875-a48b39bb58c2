<?php

namespace app\controller\bot;

use app\BaseController;

class AnnotationController extends BaseController
{
    use WithBot;

    public function initialized()
    {
        $this->middleware(fn() => $this->authorized('developer', $this->space));
    }

    public function index($offset = null)
    {
        $query = $this->bot->annotations()
            ->order('id', 'desc')
            ->limit(12);

        if ($offset) {
            $query->where('id', '<', $offset);
        }

        $annotations = $query->select();
        return json($annotations);
    }

    public function save()
    {
        $data = $this->validate([
            'question|问题' => 'require',
            'answer|回复'   => 'require',
            'message_id'    => '',
        ]);

        return $this->bot->transaction(function () use ($data) {
            return $this->bot->annotations()->save($data);
        });
    }

    public function update($id)
    {
        $ann = $this->bot->annotations()->findOrFail($id);

        $data = $this->validate([
            'question|问题' => 'require',
            'answer|回复'   => 'require',
        ]);

        $ann->save($data);

        return $ann;
    }

    public function delete($id)
    {
        $ann = $this->bot->annotations()->where('id', $id)->findOrFail();

        $ann->delete();
    }
}
