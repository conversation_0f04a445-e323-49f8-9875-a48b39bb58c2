<?php

namespace app\controller\bot;

use app\BaseController;

class ConversationController extends BaseController
{
    use WithBot;

    public function initialized()
    {
        $this->middleware(fn() => $this->authorized('developer', $this->space));
    }

    public function index()
    {
        $conversations = $this->bot->conversations()->withTrashed()->withCount(['messages'])->order('id desc')->paginate();
        return json($conversations);
    }

    public function message($id)
    {
        /** @var \app\model\Conversation $conversation */
        $conversation = $this->bot->conversations()->withTrashed()->findOrFail($id);

        $messages = $conversation->messages()->with(['annotation'])->select();
        return json($messages);
    }
}
