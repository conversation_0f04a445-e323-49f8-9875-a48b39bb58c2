<?php

namespace app\controller\bot;

use app\BaseController;
use app\model\Conversation;
use Firebase\JWT\JWT;
use think\Filesystem;

class ChatController extends BaseController
{
    use WithBot;

    protected $routeName = 'id';

    public function initialized()
    {
        $this->middleware(fn() => $this->authorized('developer', $this->space))->except(['index']);
    }

    public function index()
    {
        $conversation = Conversation::recent()
            ->where('space_id', $this->space->id)
            ->where('bot_id', $this->bot->id)
            ->where('user_id', $this->user->hash_id)
            ->where('source', 'web')
            ->with(['messages'])
            ->find();

        $token = JWT::encode([
            'exp'     => time() + 24 * 60 * 60,
            'iat'     => time(),
            'id'      => $this->bot->id,
            'user_id' => $this->user->hash_id,
            'source'  => 'web',
        ], config('app.token'), 'HS256');

        return json([
            'conversation' => $conversation,
            'token'        => $token,
        ]);
    }

    public function save()
    {
        $params = $this->validate([
            'query'        => '',
            'files'        => '',
            'variables'    => '',
            'config'       => 'require',
            'conversation' => '',
        ]);

        $params['user'] = $this->user->hash_id;

        $this->space->checkQuota('token', 'Token额度不足');

        return $this->bot->chat('dev', $params);
    }

    public function suggestion()
    {
        $params = $this->validate([
            'conversation' => 'require',
        ]);

        $this->space->checkQuota('token', 'Token额度不足');

        return $this->bot->suggestion($params['conversation']);
    }

    public function upload(Filesystem $filesystem)
    {
        $data = $this->validate([
            'file' => 'require',
        ]);

        $disk = $filesystem->disk('uploads');
        /** @var \think\file\UploadedFile $file */
        $file = $data['file'];
        $path = $disk->putFile('chat', $file);

        $result = [
            'name' => $file->getOriginalName(),
            'size' => $file->getSize(),
            'path' => $path,
        ];

        return json($result);
    }
}
