<?php

namespace app\controller\bot;

use app\controller\WithSpace;
use app\lib\Hashids;
use app\Request;

trait WithBot
{
    use WithSpace;

    /** @var \app\model\Bot */
    protected $bot;

    protected function initializeWithBot()
    {
        $this->middleware(function (Request $request, $next) {
            $routeName = $this->routeName ?? 'bot_id';
            $id        = $request->route($routeName);
            if ($id) {
                $this->bot = $this->space->bots()->findOrFail(Hashids::decode($id));
            }
            return $next($request);
        });
    }
}
