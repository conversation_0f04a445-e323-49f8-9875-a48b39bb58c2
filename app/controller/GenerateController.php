<?php

namespace app\controller;

use app\BaseController;
use app\lib\Prompt;
use app\model\Setting;
use think\ai\Client;
use function think\swoole\helper\iterator;

class GenerateController extends BaseController
{
    use WithSpace;

    public function prompt(Client $client)
    {
        $data = $this->validate([
            'query' => 'require',
        ]);

        $result = $client->chat()->completions([
            'model'    => Setting::read('model.secondary'),
            'messages' => [
                [
                    'role'    => 'system',
                    'content' => Prompt::AGENT_PROMPT,
                ],
                [
                    'role'    => 'user',
                    'content' => $data['query'],
                ],
            ],
            'stream'   => true,
        ]);

        $generator = function () use ($result) {
            foreach ($result as $event) {
                $content = $event['delta']['content'] ?? '';
                if ($content !== '') {
                    yield 'data: ' . json_encode($content) . "\n\n";
                }
                if (!empty($event['usage'])) {
                    $this->space->consumeToken($event['usage']['total_tokens']);
                }
            }

            yield "data: [DONE]\n\n";
        };

        $response = iterator($generator());

        return $response->header([
            'Content-Type'      => 'text/event-stream',
            'Cache-Control'     => 'no-cache, must-revalidate',
            'X-Accel-Buffering' => 'no',
        ]);
    }
}
