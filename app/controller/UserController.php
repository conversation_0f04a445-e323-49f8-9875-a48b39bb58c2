<?php

namespace app\controller;

use app\BaseController;
use think\Filesystem;

class UserController extends BaseController
{
    public function name()
    {
        $data = $this->validate([
            'name|昵称' => 'require',
        ]);

        $this->user->save($data);
    }

    public function avatar(Filesystem $filesystem)
    {
        $data = $this->validate([
            'avatar|头像' => 'require',
        ]);

        $disk = $filesystem->disk('uploads');

        $path = $disk->putFile('avatar', $data['avatar']);

        $this->user->save([
            'avatar' => $disk->url($path),
        ]);
    }
}
