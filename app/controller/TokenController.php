<?php

namespace app\controller;

use app\BaseController;
use Ramsey\Uuid\Uuid;

class TokenController extends BaseController
{
    use WithSpace;

    public function initialized()
    {
        $this->middleware(fn() => $this->authorized('master', $this->space));
    }

    public function index()
    {
        $tokens = $this->space->accessTokens()->order('id desc')->paginate();
        return json($tokens);
    }

    public function save()
    {
        $data = $this->validate([
            'name|名称'            => 'require',
            'expire_time|到期时间' => '',
        ]);

        // 生成访问令牌
        $data['value'] = (string) Uuid::uuid4();

        $this->space->accessTokens()->save($data);
    }

    public function delete($id)
    {
        $token = $this->space->accessTokens()->findOrFail($id);

        $token->delete();
    }

}
