<?php

namespace app\controller;

use app\BaseController;
use think\Filesystem;

class UploadController extends BaseController
{
    public function save(Filesystem $filesystem)
    {
        $data = $this->validate([
            'dir'  => 'require|in:dataset,avatar,internal,database',
            'file' => 'require',
        ]);

        $disk = $filesystem->disk('uploads');

        $path = $disk->putFile($data['dir'], $data['file']);

        $result = [
            'url' => (string) url($disk->url($path)),
        ];

        if (in_array($data['dir'], ['dataset', 'database'])) {
            $result['value'] = $path;
        }

        return json($result);
    }
}
