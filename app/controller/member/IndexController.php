<?php

namespace app\controller\member;

use app\BaseController;
use app\controller\WithSpace;
use app\model\SpaceMember;

class IndexController extends BaseController
{
    use WithSpace;

    public function index()
    {
        $members = $this->space->members()->paginate();
        return json($members);
    }

    public function update($id)
    {
        $this->authorized('master', $this->space);

        $data = $this->validate([
            'access_level' => 'require',
        ]);

        if ($data['access_level'] < $this->space->pivot->access_level) {
            $this->space->addMember($id, $data['access_level']);
        } else {
            abort(403);
        }
    }

    public function delete($id)
    {
        if ($id == $this->user->id) {
            //自己退出
            /** @var \app\model\SpaceMember $pivot */
            $pivot = $this->space->members()->attached($id);
            if ($pivot && $pivot->access_level != SpaceMember::OWNER) {
                $pivot->delete();
            }
        } else {
            $this->authorized('master', $this->space);

            /** @var \app\model\SpaceMember $pivot */
            $pivot = $this->space->members()->attached($id);

            if ($pivot && $pivot->access_level < $this->space->pivot->access_level) {
                $pivot->delete();
            }
        }
    }
}
