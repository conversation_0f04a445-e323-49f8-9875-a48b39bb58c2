<?php

namespace app\controller\member;

use app\BaseController;
use app\controller\WithSpace;
use app\model\SpaceMember;
use think\Cache;
use think\Request;

class InviteController extends BaseController
{
    use WithSpace;

    protected function initialized()
    {
        $this->middleware(fn() => $this->authorized('master', $this->space));
    }

    public function save(Cache $cache)
    {
        $code = uniqid();
        $cache->set("invite#{$code}", [
            'space'        => $this->space->id,
            'user'         => $this->user->id,
            'access_level' => SpaceMember::MEMBER,
        ], 60 * 60 * 24);

        $result = ['code' => $code];
        return json($result);
    }

    public function update(Cache $cache)
    {
        $data = $this->validate([
            'code'         => 'require',
            'access_level' => 'require',
        ]);

        $invite = $cache->get("invite#{$data['code']}");

        if ($invite && $invite['user'] == $this->user->id && $data['access_level'] < $this->space->pivot->access_level) {
            $cache->set("invite#{$data['code']}", [
                ...$invite,
                'access_level' => $data['access_level'],
            ]);
        } else {
            abort(403);
        }
    }
}
