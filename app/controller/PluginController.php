<?php

namespace app\controller;

use app\BaseController;
use app\lib\Hashids;
use think\agent\OpenApi;

class PluginController extends BaseController
{
    use WithSpace;

    protected function initialized()
    {
        $this->middleware(fn() => $this->authorized('developer', $this->space));
    }

    public function index()
    {
        $plugins = $this->space->plugins()->order('id desc')->paginate();
        return json($plugins);
    }

    public function read($id)
    {
        $plugin = $this->resolvePlugin($id);
        return json($plugin);
    }

    public function save()
    {
        $this->space->checkQuota('plugin');

        $data = $this->validate([
            'type|类型'        => 'require|in:3,4',
            'title|名称'       => 'require|length:2,25',
            'description|描述' => '',
        ]);

        $plugin = $this->space->plugins()->save($data);

        return json($plugin);
    }

    public function update($id)
    {
        $plugin = $this->resolvePlugin($id);
        $data   = $this->validate([
            'title|名称'       => 'length:2,25',
            'description|描述' => '',
            'icon|图标'        => '',
        ]);

        $plugin->save($data);

        return json($plugin);
    }

    public function delete($id)
    {
        $plugin = $this->resolvePlugin($id);
        $plugin->delete();
    }

    public function config($id)
    {
        $plugin = $this->resolvePlugin($id);

        $data = $this->validate([
            'schema'    => '',
            'auth'      => '',
            'url'       => '',
            'transport' => '',
        ]);

        $plugin->save([
            'config' => $data,
        ]);
    }

    public function parse()
    {
        $data = $this->validate([
            'schema' => '',
        ]);

        $plugin = new OpenApi($data['schema']);

        $tools = $plugin->getTools();
        return json($tools);
    }

    protected function resolvePlugin($id)
    {
        return $this->space->plugins()
            ->where('id', Hashids::decode($id))
            ->findOrFail();
    }
}
