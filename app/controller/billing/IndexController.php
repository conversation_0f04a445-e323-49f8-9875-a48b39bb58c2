<?php

namespace app\controller\billing;

use app\BaseController;
use app\controller\WithSpace;
use app\lib\goods\Recharge;
use app\lib\goods\Subscribe;
use app\lib\goods\TemporaryRecharge;
use app\lib\Hashids;
use app\model\Order;

class IndexController extends BaseController
{
    use WithSpace;

    public function initialized()
    {
        $this->middleware(fn() => $this->authorized('master', $this->space));
    }

    public function index()
    {
        $services = Recharge::SERVICES;

        // 添加临时扩容服务
        $services['temporary'] = [
            'price' => TemporaryRecharge::PRICE_PER_1000K,
            'step'  => TemporaryRecharge::STEP,
            'unit'  => 'K',
            'name'  => '临时Token扩容',
            'description' => '临时扩容额度，下次重置时失效',
            'temporary' => true, // 标记为临时服务
        ];

        return json([
            'plans'    => Subscribe::PLANS,
            'services' => $services,
        ]);
    }

    public function subscribe()
    {
        $data = $this->validate([
            'plan'    => 'require',
            'mode'    => 'require',
            'service' => '',
        ]);

        $goods = new Subscribe($this->space, $data['plan'], $data['mode'], $data['service'] ?? []);

        $result = $goods->purchase($this->user);

        return json($result);
    }

    public function recharge()
    {
        $services = array_keys(Recharge::SERVICES);
        $services[] = 'temporary'; // 添加临时扩容服务

        $data = $this->validate([
            'service' => 'require|in:' . implode(',', $services),
            'nums'    => 'require|integer|min:1',
        ]);

        // 根据服务类型创建不同的商品
        if ($data['service'] === 'temporary') {
            $goods = new TemporaryRecharge($this->space, $data['nums']);
            $order = Order::createOrder($this->space, $goods);
            return json($order->pay());
        } else {
            $goods = new Recharge($this->space, $data['service'], $data['nums']);
            $result = $goods->purchase($this->user);
            return json($result);
        }
    }



    public function check()
    {
        $orderNo = $this->request->param('order_no');

        $order = Order::findOrFail(Hashids::decode($orderNo));
        if ($order->status != 1) {
            abort(449);
        }
    }
}
