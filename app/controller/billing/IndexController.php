<?php

namespace app\controller\billing;

use app\BaseController;
use app\controller\WithSpace;
use app\lib\goods\Recharge;
use app\lib\goods\Subscribe;
use app\lib\goods\TemporaryRecharge;
use app\lib\Hashids;
use app\model\Order;

class IndexController extends BaseController
{
    use WithSpace;

    public function initialized()
    {
        $this->middleware(fn() => $this->authorized('master', $this->space));
    }

    public function index()
    {
        return json([
            'plans'    => Subscribe::PLANS,
            'services' => Recharge::SERVICES,
        ]);
    }

    public function subscribe()
    {
        $data = $this->validate([
            'plan'    => 'require',
            'mode'    => 'require',
            'service' => '',
        ]);

        $goods = new Subscribe($this->space, $data['plan'], $data['mode'], $data['service'] ?? []);

        $result = $goods->purchase($this->user);

        return json($result);
    }

    public function recharge()
    {
        $data = $this->validate([
            'service' => 'require',
            'nums'    => 'require',
        ]);

        $goods = new Recharge($this->space, $data['service'], $data['nums']);

        $result = $goods->purchase($this->user);

        return json($result);
    }

    public function temporaryRecharge()
    {
        $data = $this->validate([
            'nums' => 'require|integer|between:1,10',
        ]);

        $goods = new TemporaryRecharge($this->space, $data['nums']);

        $result = $goods->purchase($this->user);

        return json($result);
    }

    public function check()
    {
        $orderNo = $this->request->param('order_no');

        $order = Order::findOrFail(Hashids::decode($orderNo));
        if ($order->status != 1) {
            abort(449);
        }
    }
}
