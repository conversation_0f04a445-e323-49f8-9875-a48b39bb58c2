<?php

namespace app\controller;

use app\BaseController;
use app\model\Setting;
use think\ai\Client;

class ModelController extends BaseController
{
    public function index(Client $client, $type)
    {
        $models = $client->model()->list(['type' => $type]);

        $codes = Setting::read("model.{$type}");

        $filteredModels = array_values(array_filter($models, function ($model) use ($codes) {
            return in_array($model['code'], $codes);
        }));

        return json($filteredModels);
    }
}
