<?php

namespace app\task;

use app\lib\Date;
use app\model\Dataset;
use yunwuxin\cron\Task;

class SyncDataset extends Task
{
    protected function configure()
    {
        $this->daily()->onOneServer();
    }

    public function handle()
    {
        $cursor = Dataset::where('type', Dataset::TYPE_WEB)->where('freq', '>', 0)->cursor();

        /** @var Dataset $dataset */
        foreach ($cursor as $dataset) {
            try {
                if (!$dataset->space->isAvailable()) {
                    continue;
                }
                $dataset->space->checkQuota('token');

                if (!$dataset->last_time ||
                    $dataset->last_time->startOfDay()->addDays($dataset->freq)->lt(Date::now())
                ) {
                    $dataset->sync();
                }
            } catch (\Throwable) {
            }
        }
    }
}
