<?php
// 应用公共文件

use app\lib\Date;
use GuzzleHttp\Client;

function get_server_ip()
{
    try {
        return \think\facade\Cache::remember('server_ip', function () {
            $curl = curl_init('https://ipinfo.io/ip');
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            $response = curl_exec($curl);
            curl_close($curl);

            return $response;
        }, 24 * 3600);
    } catch (Throwable) {
        return '未知';
    }
}

function fill_data($dateList, $data, $dateKey = 'date', $valueKey = 'value', $alias = null)
{
    if ($data instanceof \think\Collection) {
        $data = $data->toArray();
    }
    $keys = array_column($data, $dateKey);
    array_multisort($keys, SORT_ASC, $data);

    $result  = [];
    $dataKey = 0;

    foreach ($dateList as $date) {
        if (!empty($data[$dataKey]) && Date::parse($data[$dataKey][$dateKey])->equalTo($date)) {
            $result[] = [
                $dateKey            => $date,
                $alias ?? $valueKey => (int) $data[$dataKey][$valueKey],
            ];
            $dataKey++;
        } else {
            $result[] = [
                $dateKey            => $date,
                $alias ?? $valueKey => 0,
            ];
        }
    }

    return $result;
}

function get_date_query($field, $unit = 'hour')
{
    $formats = [
        'minute' => '%Y-%m-%d %H:%i:00',
        'hour'   => '%Y-%m-%d %H:00:00',
        'day'    => '%Y-%m-%d 00:00:00',
        'week'   => '%xW%v',
        'month'  => '%Y-%m-01',
        'year'   => '%Y-01-01',
    ];

    return "date_format({$field}, '{$formats[$unit]}')";
}

function get_period($period = '24hours')
{
    switch ($period) {
        case '1year':
            $start  = Date::now()->subYears(1)->addMonth()->startOfMonth();
            $end    = Date::now()->endOfMonth();
            $unit   = 'month';
            $format = 'Y-m-01';
            break;
        case '6months':
            $start  = Date::now()->subMonths(6)->addWeek()->startOfWeek();
            $end    = Date::now()->endOfWeek();
            $unit   = 'week';
            $format = 'Y-m-d';
            break;
        case '180days':
            $start  = Date::now()->subDays(180)->addDay()->startOfDay();
            $end    = Date::now()->endOfDay();
            $unit   = 'week';
            $format = 'Y-m-d';
            break;
        case '90days':
            $start  = Date::now()->subDays(90)->addDay()->startOfDay();
            $end    = Date::now()->endOfDay();
            $unit   = 'day';
            $format = 'Y-m-d';
            break;
        case 'last-month':
            $start  = Date::now()->subMonth()->startOfMonth();
            $end    = Date::now()->subMonth()->endOfMonth();
            $unit   = 'day';
            $format = 'Y-m-d';
            break;
        case '30days':
            $start  = Date::now()->subDays(30)->addDay()->startOfDay();
            $end    = Date::now()->endOfDay();
            $unit   = 'day';
            $format = 'Y-m-d';
            break;
        case '7days':
            $start  = Date::now()->subDays(7)->addHour()->startOfHour();
            $end    = Date::now()->endOfHour();
            $unit   = 'hour';
            $format = 'Y-m-d H:00:00';
            break;
        case '3days':
            $start  = Date::now()->subDays(3)->addHour()->startOfHour();
            $end    = Date::now()->endOfHour();
            $unit   = 'hour';
            $format = 'Y-m-d H:00:00';
            break;
        case 'yesterday':
            $start  = Date::yesterday()->startOfDay();
            $end    = Date::yesterday()->endOfDay();
            $unit   = 'hour';
            $format = 'Y-m-d H:00:00';
            break;
        case '24hours':
        default:
            $start  = Date::now()->subHours(24)->addHour()->startOfHour();
            $end    = Date::now()->endOfHour();
            $unit   = 'hour';
            $format = 'Y-m-d H:00:00';
    }

    $list = [];
    $date = $start->copy();
    do {
        if ($date->gt($end)) {
            break;
        }
        $list[] = $date->format($format);
    } while ($date = $date->add($unit, 1));

    return [$start, $end, $unit, $list];
}

function url_get_contents($url)
{
    $client = new Client([
        'timeout' => 10,
        'verify'  => false,
    ]);

    return $client->get($url)->getBody()->getContents();
}

if (!function_exists('array_find_key')) {
    function array_find_key(array $array, callable $callback): mixed
    {
        foreach ($array as $key => $value) {
            if ($callback($value, $key)) {
                return $key;
            }
        }

        return null;
    }
}

function replace_deep(array $original, array $defaults)
{
    foreach ($defaults as $key => $value) {
        if (empty($original[$key])) {
            $original[$key] = $value;
        } elseif (is_array($value) && is_array($original[$key])) {
            $original[$key] = replace_deep($original[$key], $value);
        }
    }
    return $original;
}
