<?php

namespace app\job;

use app\lib\Date;
use app\model\Dataset;
use think\facade\Log;
use think\queue\Job;
use vipnytt\SitemapParser;
use yunwuxin\model\SerializesModel;

class DatasetSyncJob
{
    use SerializesModel;

    public function __construct(protected Dataset $dataset)
    {
    }

    public function handle(Job $job)
    {
        $this->dataset->save(['status' => 2]);
        try {
            $parser = new SitemapParser();
            $parser->parse($this->dataset->config->sitemap);
            $urls = $parser->getURLs();

            foreach ($urls as $url => $tag) {
                /** @var \app\model\DatasetSource $source */
                $source = $this->dataset->sources()->where('type', 'url')->where('content', $url)->find();
                if (empty($source)) {
                    $this->dataset->sources()->save([
                        'type'    => 'url',
                        'title'   => $url,
                        'content' => $url,
                        'size'    => 0,
                    ]);
                } else {
                    $shouldRetrain = $source->status == -1;
                    $shouldRetrain = $shouldRetrain || ($source->status == 1 && (empty($tag['lastmod']) || $source->update_time->lt(Date::createFromTimeString($tag['lastmod']))));

                    if ($shouldRetrain) {
                        $source->retrain();
                    }
                }
            }

            $this->dataset->save(['status' => 1, 'last_time' => Date::now()]);
        } catch (\Throwable $e) {
            Log::error($e->getMessage());
            $this->dataset->save(['status' => -1]);
        }
        $job->delete();
    }
}
