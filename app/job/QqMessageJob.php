<?php

namespace app\job;

use app\model\BotQq;
use app\model\Conversation;
use Ramsey\Uuid\Uuid;
use think\facade\Log;
use think\helper\Arr;
use yunwuxin\model\SerializesModel;

class QqMessageJob
{
    use SerializesModel;

    public function __construct(protected BotQq $qq, protected string $type, protected array $data)
    {
    }

    public function handle()
    {
        $client = $this->qq->getClient();
        $seq    = 0;

        switch ($this->type) {
            case 'GROUP_AT_MESSAGE_CREATE'://群聊@
                $gid   = $this->data['group_openid'];
                $uid   = $this->data['author']['member_openid'];
                $user  = (string) Uuid::uuid5(Uuid::NAMESPACE_DNS, "qq#{$gid}#{$uid}#{$this->qq->id}");
                $query = $this->data['content'];

                $sendMessage = function ($content) use ($gid, $client, &$seq) {
                    $client->sendGroupMessage($gid, $content, $this->data['id'], ++$seq);
                };
                break;
            case 'C2C_MESSAGE_CREATE'://私聊
                $uid   = $this->data['author']['user_openid'];
                $user  = (string) Uuid::uuid5(Uuid::NAMESPACE_DNS, "qq#{$uid}#{$this->qq->id}");
                $query = $this->data['content'];

                $sendMessage = function ($content) use ($uid, $client, &$seq) {
                    $client->sendUserMessage($uid, $content, $this->data['id'], ++$seq);
                };
                break;
            case 'FRIEND_ADD'://添加好友
                $prologue = Arr::get($this->qq->bot->config, 'onboarding.prologue', '');

                if ($prologue) {
                    $client->sendUserMessage($this->data['openid'], $prologue);
                }
                return;
            default:
                return;
        }

        $conversation = Conversation::recent()
            ->where('bot_id', $this->qq->bot->id)
            ->where('user_id', $user)
            ->find();

        $this->qq->bot->subscribe('qq', [
            'user'         => $user,
            'conversation' => $conversation,
            'query'        => $query,
        ], function ($type, $content) use ($sendMessage) {
            try {
                switch ($type) {
                    case 'text':
                        $sendMessage($content);
                        break;
                    case 'image':

                        break;
                }
            } catch (\Exception $e) {
                Log::error($e->getMessage());
            }
        });
    }
}
