<?php

namespace app\job;

use app\model\DatasetSource;
use think\queue\Job;

class DatasetTrainJob
{
    public function fire(Job $job, $id)
    {
        $source = DatasetSource::where('status', 0)->findOrFail($id);
        try {
            $source->train();
        } catch (\Throwable $e) {
            $this->failed($source, $e);
        }
        $job->delete();
    }

    public function failed($source, $e)
    {
        if (!$source instanceof DatasetSource) {
            $source = DatasetSource::findOrFail($source);
        }
        $source->save(['status' => -1, 'message' => substr($e->getMessage(), 0, 200)]);
    }
}
