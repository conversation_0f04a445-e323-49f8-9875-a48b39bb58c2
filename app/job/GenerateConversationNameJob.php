<?php

namespace app\job;

use app\lib\Prompt;
use app\model\Conversation;
use app\model\Setting;
use think\ai\Client;
use think\helper\Arr;
use yunwuxin\model\SerializesModel;

class GenerateConversationNameJob
{
    use SerializesModel;

    public $tries = 3;

    public function __construct(protected Conversation $conversation, protected string $query)
    {
    }

    public function handle(Client $client)
    {
        $conversation = $this->conversation;
        $query        = $this->query;

        if (empty($conversation->title)) {
            $prompt = Prompt::CONVERSATION_TITLE_PROMPT;

            if (mb_strlen($query) > 2000) {
                $query = mb_substr($query, 0, 300) . "...[TRUNCATED]..." . mb_substr($query, -300);
            }

            $prompt .= $query;

            $res = $client->chat()->completions([
                'model'       => Setting::read('model.secondary'),
                'messages'    => [
                    [
                        'role'    => 'user',
                        'content' => $prompt,
                    ],
                ],
                'max_tokens'  => 100,
                'temperature' => 1,
                'stream'      => false,
                'moderation'  => false,
            ]);

            $content = json_decode(Arr::get($res, 'message.content'), true);

            $title = $content['Your Output'];

            $conversation->save([
                'title' => $title,
            ]);

            $conversation->space->consumeToken(Arr::get($res, 'usage.total_tokens'));
        }
    }
}
