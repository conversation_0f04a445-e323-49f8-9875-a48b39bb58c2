<?php

namespace app\job;

use app\model\BotWkf;
use app\model\Conversation;
use GuzzleHttp\Psr7\Utils;
use Ramsey\Uuid\Uuid;
use think\facade\Log;
use think\helper\Arr;
use think\queue\Job;

class WkfMessageJob
{
    public function fire(Job $job, $payload)
    {
        [$id, $msg] = $payload;

        $wkf = BotWkf::where('id', $id)->findOrFail();

        $provider = $wkf->getProvider();

        if ($msg['msgtype'] == 'text') {
            $user         = Uuid::uuid5(Uuid::NAMESPACE_DNS, "wkf#{$msg['open_kfid']}#{$msg['external_userid']}#{$wkf->id}");
            $conversation = Conversation::recent()
                ->where('bot_id', $wkf->bot->id)
                ->where('user_id', $user)
                ->find();

            $wkf->bot->subscribe('wkf',
                [
                    'user'         => $user,
                    'conversation' => $conversation,
                    'query'        => $msg['text']['content'],
                ],
                function ($type, $content) use ($provider, $msg) {
                    try {
                        switch ($type) {
                            case 'text':
                                $provider->sendMsg($msg['external_userid'], $msg['open_kfid'], [
                                    'msgtype' => 'text',
                                    'text'    => [
                                        'content' => $content,
                                    ],
                                ]);
                                break;
                            case 'image':
                                $result = $provider->uploadMedia(Utils::tryFopen(root_path('storage') . ltrim($content, '/'), 'r'));

                                if ($result['errcode'] == 0) {
                                    $mediaId = $result['media_id'];

                                    $provider->sendMsg($msg['external_userid'], $msg['open_kfid'], [
                                        'msgtype' => 'image',
                                        'image'   => [
                                            'media_id' => $mediaId,
                                        ],
                                    ]);
                                } else {
                                    Log::error($result['errmsg']);
                                    $provider->sendMsg($msg['external_userid'], $msg['open_kfid'], [
                                        'msgtype' => 'text',
                                        'text'    => [
                                            'content' => '[图片无法显示]',
                                        ],
                                    ]);
                                }
                                break;
                        }
                    } catch (\Exception $e) {
                        Log::error($e->getMessage());
                    }
                }
            );
        } elseif ($msg['msgtype'] == 'event') {
            try {
                //事件消息
                if ($msg['event']['event_type'] == 'enter_session' && !empty($msg['event']['welcome_code'])) {
                    //发送欢迎消息
                    $enable = Arr::get($wkf->bot->config, 'onboarding.enable', false);
                    if ($enable) {
                        $prologue  = Arr::get($wkf->bot->config, 'onboarding.prologue', '');
                        $questions = Arr::get($wkf->bot->config, 'onboarding.questions', []);

                        if ($prologue) {
                            if (!empty($questions)) {
                                //3条问题
                                $questions = array_slice($questions, 0, 3);
                                $provider->sendMsgOnEvent($msg['event']['welcome_code'], [
                                    'msgtype' => 'msgmenu',
                                    'msgmenu' => [
                                        'head_content' => $prologue,
                                        'list'         => array_map(function ($item) {
                                            return [
                                                'type'  => 'click',
                                                'click' => [
                                                    'id'      => $item,
                                                    'content' => $item,
                                                ],
                                            ];
                                        }, $questions),
                                    ],
                                ]);
                            } else {
                                $provider->sendMsgOnEvent($msg['event']['welcome_code'], [
                                    'msgtype' => 'text',
                                    'text'    => [
                                        'content' => $prologue,
                                    ],
                                ]);
                            }
                        }
                    }
                }
            } catch (\Exception $e) {
                Log::error($e->getMessage());
            }
        }

        $job->delete();
    }
}
