<?php

namespace app\job;

use app\model\BotWmp;
use app\model\Conversation;
use GuzzleHttp\Psr7\Utils;
use Ramsey\Uuid\Uuid;
use think\facade\Log;
use think\queue\Job;

class WmpMessageJob
{
    public function fire(Job $job, $payload)
    {
        [$id, $from, $content] = $payload;

        $wmp = BotWmp::where('id', $id)->findOrFail();

        $provider = $wmp->getProvider();

        //下发输入状态
        $provider->sendTypingStatus($from);

        $user         = Uuid::uuid5(Uuid::NAMESPACE_DNS, "wmp#{$from}#{$wmp->id}");
        $conversation = Conversation::recent()
            ->where('bot_id', $wmp->bot->id)
            ->where('user_id', $user)
            ->find();

        $wmp->bot->subscribe('wmp',
            [
                'user'         => $user,
                'conversation' => $conversation,
                'query'        => $content,
            ],
            function ($type, $content) use ($provider, $from) {
                try {
                    switch ($type) {
                        case 'text':
                            $provider->sendCustomMessage($from, [
                                'msgtype' => 'text',
                                'text'    => [
                                    'content' => $content,
                                ],
                            ]);
                            break;
                        case 'image':
                            $result = $provider->uploadMedia(Utils::tryFopen(root_path('storage') . ltrim($content, '/'), 'r'));

                            if (empty($result['errcode'])) {
                                $mediaId = $result['media_id'];

                                $provider->sendCustomMessage($from, [
                                    'msgtype' => 'image',
                                    'image'   => [
                                        'media_id' => $mediaId,
                                    ],
                                ]);
                            } else {
                                Log::error($result['errmsg']);
                                $provider->sendCustomMessage($from, [
                                    'msgtype' => 'text',
                                    'text'    => [
                                        'content' => '[图片无法显示]',
                                    ],
                                ]);
                            }
                            break;
                    }
                } catch (\Exception $e) {
                    Log::error($e->getMessage());
                }
            }
        );

        $job->delete();
    }
}
