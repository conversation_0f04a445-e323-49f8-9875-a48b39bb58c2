<?php

declare(strict_types = 1);

namespace app\lib\wechat\support;

use app\lib\wechat\contracts\AccessToken;
use GuzzleHttp\Client;
use GuzzleHttp\Utils;
use think\helper\Str;
use function array_merge;

class AccessTokenAwareClient
{
    public function __construct(
        protected Client      $client,
        protected AccessToken $accessToken,
    )
    {

    }

    public function post(string $url, array $options = [])
    {
        return $this->request('post', $url, $options);
    }

    public function get(string $url, array $options = [])
    {
        return $this->request('get', $url, $options);
    }

    /**
     * @param array<string, mixed> $options
     */
    public function request(string $method, string $url, array $options = [])
    {
        if ($this->accessToken) {
            $options['query'] = array_merge((array) ($options['query'] ?? []), $this->accessToken->toQuery());
        }

        if (isset($options['json'])) {
            $options['body'] = Utils::jsonEncode($options['json'], JSON_UNESCAPED_UNICODE);
            unset($options['json']);
            if (!isset($options['headers'])) {
                $options['headers'] = [];
            }
            $options['headers']['Content-Type'] = 'application/json';
        }

        $response = $this->client->request($method, ltrim($url, '/'), $options);

        $contents = $response->getBody()->getContents();

        if (Str::contains($response->getHeaderLine('Content-Type'), 'application/json') || Str::startsWith($contents, '{')) {
            return json_decode($contents, true);
        }

        return $contents;
    }

}
