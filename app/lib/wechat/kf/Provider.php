<?php

namespace app\lib\wechat\kf;

use app\lib\wechat\Account;
use app\lib\wechat\Encryptor;
use app\lib\wechat\support\AccessTokenAwareClient;
use app\lib\wechat\traits\InteractWithCache;
use app\lib\wechat\traits\InteractWithClient;
use app\lib\wechat\traits\InteractWithHttpClient;
use Symfony\Component\OptionsResolver\OptionsResolver;

class Provider
{
    use InteractWithHttpClient;
    use InteractWithClient;
    use InteractWithCache;

    protected $config;

    protected $account;
    protected $encryptor;
    protected $accessToken;

    protected $server;

    public function __construct($config)
    {
        $resolver = new OptionsResolver();
        $resolver->setRequired(['aes_key', 'token']);
        $resolver->setDefined(['app_secret', 'app_id']);
        $this->config = $resolver->resolve($config);
    }

    public function getAccount()
    {
        if (!$this->account) {
            $this->account = new Account(
                appId: (string) $this->config['app_id'],
                secret: (string) $this->config['app_secret'],
                token: (string) $this->config['token'],
                aesKey: (string) $this->config['aes_key'],
            );
        }
        return $this->account;
    }

    public function getEncryptor(): Encryptor
    {
        if (!$this->encryptor) {
            $this->encryptor = new Encryptor(
                appId: $this->getAccount()->getAppId(),
                token: $this->getAccount()->getToken(),
                aesKey: $this->getAccount()->getAesKey(),
                receiveId: $this->getAccount()->getAppId(),
            );
        }
        return $this->encryptor;
    }

    public function getServer()
    {
        if (!$this->server) {
            $this->server = new Server($this->getEncryptor());
        }

        return $this->server;
    }

    public function setAccessToken(AccessToken $accessToken)
    {
        $this->accessToken = $accessToken;
        return $this;
    }

    public function getAccessToken()
    {
        if (!$this->accessToken) {
            $this->accessToken = new AccessToken(
                appId: $this->getAccount()->getAppId(),
                appSecret: $this->getAccount()->getSecret(),
                cache: $this->getCache(),
                httpClient: $this->getHttpClient(),
            );
        }

        return $this->accessToken;
    }

    public function uploadMedia($contents, $type = 'image', $filename = 'image.png')
    {
        return $this->getClient()->post('cgi-bin/media/upload', [
            'query'     => [
                'type' => $type,
            ],
            'multipart' => [
                [
                    'name'     => 'media',
                    'contents' => $contents,
                    'filename' => $filename,
                ],
            ],
        ]);
    }

    public function sendMsg($to, $kfid, $message)
    {
        return $this->getClient()->post('cgi-bin/kf/send_msg', [
            'json' => [
                'touser'    => $to,
                'open_kfid' => $kfid,
                ...$message,
            ],
        ]);
    }

    public function sendMsgOnEvent($code, $message)
    {
        return $this->getClient()->post('cgi-bin/kf/send_msg_on_event', [
            'json' => [
                'code' => $code,
                ...$message,
            ],
        ]);
    }

    protected function getHttpClientDefaultOptions(): array
    {
        return ['base_uri' => 'https://qyapi.weixin.qq.com/'];
    }

    public function createClient(): AccessTokenAwareClient
    {
        return (new AccessTokenAwareClient(
            client: $this->getHttpClient(),
            accessToken: $this->getAccessToken(),
        ));
    }

}
