<?php

namespace app\lib\wechat\traits;

use app\lib\wechat\Encryptor;
use app\lib\wechat\Message;
use app\lib\wechat\support\Xml;
use Exception;

trait DecryptXmlMessage
{
    public function decryptMessage(
        Message    $message,
        Encryptor  $encryptor,
        string     $signature,
        int|string $timestamp,
        string     $nonce
    ): Message
    {
        $ciphertext = $message->Encrypt;

        $this->validateSignature($encryptor->getToken(), $ciphertext, $signature, $timestamp, $nonce);

        $message->merge(Xml::parse(
            $encryptor->decrypt(
                ciphertext: $ciphertext,
                msgSignature: $signature,
                nonce: $nonce,
                timestamp: $timestamp
            )
        ) ?? []);

        return $message;
    }

    protected function validateSignature(
        string     $token,
        string     $ciphertext,
        string     $signature,
        int|string $timestamp,
        string     $nonce
    ): void
    {
        if (empty($signature)) {
            throw new Exception('Request signature must not be empty.');
        }

        $params = [$token, $timestamp, $nonce, $ciphertext];

        sort($params, SORT_STRING);

        if ($signature !== sha1(implode($params))) {
            throw new Exception('Invalid request signature.');
        }
    }
}
