<?php

namespace app\lib\wechat\traits;

use Psr\SimpleCache\CacheInterface;

trait InteractWithCache
{
    protected ?CacheInterface $cache = null;

    public function setCache(CacheInterface $cache): static
    {
        $this->cache = $cache;

        return $this;
    }

    public function getCache(): CacheInterface
    {
        if (!$this->cache) {
            throw new \Exception('cache not set');
        }

        return $this->cache;
    }
}
