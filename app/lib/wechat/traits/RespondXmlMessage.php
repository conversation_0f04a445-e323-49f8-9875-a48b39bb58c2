<?php

namespace app\lib\wechat\traits;

use app\lib\wechat\Encryptor;
use app\lib\wechat\Message;
use app\lib\wechat\support\Xml;
use InvalidArgumentException;
use RuntimeException;
use think\Response;

trait RespondXmlMessage
{
    /**
     * @throws RuntimeException
     * @throws InvalidArgumentException
     */
    public function transformToReply(mixed $response, Message $message, ?Encryptor $encryptor = null): Response
    {
        if (empty($response)) {
            return response('success');
        }

        return $this->createXmlResponse(
            attributes: array_filter(
                array_merge(
                    [
                        'ToUserName'   => $message->FromUserName,
                        'FromUserName' => $message->ToUserName,
                        'CreateTime'   => time(),
                    ],
                    $this->normalizeResponse($response),
                )
            ),
            encryptor: $encryptor
        );
    }

    /**
     * @return array<string, mixed>
     *
     * @throws InvalidArgumentException
     */
    protected function normalizeResponse(mixed $response): array
    {
        if (is_callable($response)) {
            $response = $response();
        }

        if (is_array($response)) {
            if (!isset($response['MsgType'])) {
                throw new InvalidArgumentException('MsgType cannot be empty.');
            }

            return $response;
        }

        if (is_string($response) || is_numeric($response)) {
            return [
                'MsgType' => 'text',
                'Content' => $response,
            ];
        }

        throw new InvalidArgumentException(
            sprintf('Invalid Response type "%s".', gettype($response))
        );
    }

    /**
     * @param array<string, mixed> $attributes
     *
     * @throws RuntimeException
     */
    protected function createXmlResponse(array $attributes, ?Encryptor $encryptor = null): Response
    {
        $xml = Xml::build($attributes);

        return response($encryptor ? $encryptor->encrypt($xml) : $xml, 200, ['Content-Type' => 'application/xml']);
    }
}
