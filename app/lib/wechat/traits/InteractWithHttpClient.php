<?php

namespace app\lib\wechat\traits;

use Guzzle<PERSON>ttp\Client;
use GuzzleHttp\HandlerStack;

trait InteractWithHttpClient
{
    protected ?Client $httpClient = null;

    public function getHttpClient(): Client
    {
        if (!$this->httpClient) {
            $this->httpClient = $this->createHttpClient();
        }

        return $this->httpClient;
    }

    protected function createHttpClient(): Client
    {
        $handler = HandlerStack::create();

        return new Client(array_merge(
            ['verify' => false, 'handler' => $handler],
            $this->getHttpClientDefaultOptions()
        ));
    }

    /**
     * @return array<string,mixed>
     */
    protected function getHttpClientDefaultOptions(): array
    {
        return [];
    }
}
