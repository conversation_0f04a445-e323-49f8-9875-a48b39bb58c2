<?php

namespace app\lib\wechat\mp;

use app\lib\wechat\Encryptor;
use app\lib\wechat\traits\DecryptXmlMessage;
use app\lib\wechat\traits\InteractWithHandlers;
use app\lib\wechat\traits\RespondXmlMessage;
use Closure;
use think\Request;
use think\Response;

class Server
{
    use InteractWithHandlers;
    use DecryptXmlMessage;
    use RespondXmlMessage;

    public function __construct(protected Encryptor $encryptor)
    {

    }

    public function serve(Request $request)
    {
        $query = $request->get();

        if (!empty($query['echostr'])) {
            //验证服务器
            return response($query['echostr']);
        }

        $message = Message::createFromRequest($request);

        $encrypted = !empty($query['msg_signature']);

        if ($encrypted) {
            $this->prepend($this->decryptRequestMessage($query));
        }

        $response = $this->handle(response('success'), $message);

        if (!($response instanceof Response)) {
            $response = $this->transformToReply($response, $message, $encrypted ? $this->encryptor : null);
        }

        return $response;
    }

    protected function decryptRequestMessage($query): Closure
    {
        return function (Message $message, Closure $next) use ($query): mixed {

            $message = $this->decryptMessage(
                message: $message,
                encryptor: $this->encryptor,
                signature: $query['msg_signature'] ?? '',
                timestamp: $query['timestamp'] ?? '',
                nonce: $query['nonce'] ?? ''
            );

            return $next($message);
        };
    }

}
