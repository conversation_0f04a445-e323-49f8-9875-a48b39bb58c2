<?php

namespace app\lib;

use think\agent\Util;
use think\helper\Arr;

class Separator
{

    public function separate($content)
    {
        $chunks = $this->getChunks([
            [
                'title'   => '',
                'content' => $content,
                'tokens'  => $this->getTokensNum($content),
                'depth'   => 0,
            ],
        ]);

        $chunks = array_filter($chunks, function ($chunk) {
            return !empty($chunk['title']) || !empty($chunk['content']);
        });

        //合并
        $previous = null;
        foreach ($chunks as $key => &$chunk) {
            if ($previous && $chunk['tokens'] < 200 && $chunk['tokens'] + $previous['tokens'] < 400) {
                $previous['content'] = join("\n", array_filter([
                    $previous['title'],
                    $previous['content'],
                    $chunk['title'],
                    $chunk['content'],
                ]));
                $previous['title']   = '';
                $previous['tokens']  += $chunk['tokens'];
                unset($chunks[$key]);
            } else {
                $previous = &$chunk;
            }
        }
        unset($previous);

        //合并内容标题
        return array_map(function ($chunk) {
            return trim($this->getTitle($chunk) . "\n" . $chunk['content']);
        }, array_values($chunks));
    }

    protected function getTitle($chunk)
    {
        $title = $chunk['title'];
        if (!empty($chunk['parent'])) {
            $title = $this->getTitle($chunk['parent']) . "\n" . $title;
        }
        return trim($title);
    }

    protected function getChunks($chunks)
    {
        return Arr::flatMap(function ($current) {
            if ($current['tokens'] < 300) {
                return [$current];
            }

            $depth = $current['depth'] + 1;

            if ($depth > 5) {
                if ($current['tokens'] > 500) {
                    $chunks = $this->splitByLength($current['content']);
                    return array_map(function ($chunk) use ($current) {
                        return [
                            'parent'  => $current['parent'] ?? null,
                            'title'   => $current['title'],
                            'content' => trim($chunk),
                            'tokens'  => $this->getTokensNum($chunk),
                            'depth'   => $current['depth'],
                        ];
                    }, $chunks);
                }
                return [$current];
            }

            $pattern = '/^' . str_repeat('#', $depth) . '\s+/m';
            $splits  = preg_split($pattern, $current['content'], -1, PREG_SPLIT_DELIM_CAPTURE);

            if (count($splits) == 1) {
                return $this->getChunks([
                    [
                        'parent'  => $current['parent'] ?? null,
                        'title'   => $current['title'],
                        'content' => $current['content'],
                        'tokens'  => $current['tokens'],
                        'depth'   => $depth,
                    ],
                ]);
            }

            $chunks = [];
            $first  = array_shift($splits);

            if ($first) {
                if ($this->getTokensNum($first) < 50) {
                    $current['title'] .= "\n" . trim($first);
                } else {
                    $chunks[] = [
                        'title'   => trim($current['title']),
                        'content' => trim($first),
                        'tokens'  => $this->getTokensNum(trim($first)),
                        'depth'   => $depth,
                    ];
                }
            }

            array_push($chunks, ...array_map(function ($chunk) use ($depth, $current) {
                $parts = preg_split("/\r\n|\n|\r/", $chunk, 2);
                if (count($parts) == 2) {
                    [$title, $content] = $parts;
                    $title = str_repeat('#', $depth) . ' ' . $title;

                    return [
                        'parent'  => $current,
                        'title'   => trim($title),
                        'content' => trim($content),
                        'tokens'  => $this->getTokensNum(trim($content)),
                        'depth'   => $depth,
                    ];
                } else {
                    return [
                        'title'   => $current['title'],
                        'content' => trim($chunk),
                        'tokens'  => $this->getTokensNum(trim($chunk)),
                        'depth'   => $depth,
                    ];
                }
            }, $splits));

            return $this->getChunks($chunks);
        }, $chunks);
    }

    protected function splitByLength($content)
    {
        $splits = preg_split("/\r\n|\n|\r/", $content);

        $chunks  = [];
        $current = '';

        foreach ($splits as $split) {
            $current .= $split . "\n";
            if ($this->getTokensNum($current) > 350) {
                $chunks[] = $current;
                $current  = '';
            }
        }

        if ($current) {
            $chunks[] = $current;
        }

        return $chunks;
    }

    protected function getTokensNum($text)
    {
        return Util::tikToken($text);
    }
}
