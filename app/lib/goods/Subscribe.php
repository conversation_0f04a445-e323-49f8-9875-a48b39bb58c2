<?php

namespace app\lib\goods;

use app\lib\Date;
use app\lib\Goods;
use app\model\Order;
use app\model\Space;
use think\exception\ValidateException;

class Subscribe extends Goods
{
    const PLANS = [
        'trial'        => [
            'name'   => '体验版',
            'slogan' => '适用于体验和测试应用',
            'price'  => null,
            'rights' => [
                'token'    => 100,
                'bot'      => 2,
                'member'   => 2,
                'dataset'  => 2,
                'database' => 2,
                'plugin'   => 2,
                'history'  => 30,
                'api'      => false,
            ],
            'level'  => 0,
        ],
        'standard'     => [
            'name'   => '基础版',
            'slogan' => '适用于小型业务规模的应用',
            'price'  => 59,
            'rights' => [
                'token'    => 1000,
                'bot'      => 5,
                'member'   => 5,
                'dataset'  => 5,
                'database' => 0,
                'plugin'   => 5,
                'history'  => 90,
                'api'      => false,
            ],
            'level'  => 10,
        ],
        'professional' => [
            'name'   => '专业版',
            'slogan' => '适用于中型业务规模的应用',
            'price'  => 199,
            'rights' => [
                'token'    => 5000,
                'bot'      => 20,
                'member'   => 20,
                'dataset'  => 20,
                'database' => 10,
                'plugin'   => 10,
                'history'  => 180,
                'api'      => true,
            ],
            'level'  => 20,
        ],
        'enterprise'   => [
            'name'   => '企业版',
            'slogan' => '适用于大型业务规模的应用',
            'price'  => 599,
            'rights' => [
                'token'    => 20000,
                'bot'      => 100,
                'member'   => 100,
                'dataset'  => 100,
                'database' => 50,
                'plugin'   => 100,
                'history'  => 360,
                'api'      => true,
            ],
            'level'  => 30,
        ],
        'private'      => [
            'name'   => '私有化部署',
            'slogan' => '',
            'price'  => null,
            'rights' => null,
            'level'  => 40,
        ],
    ];

    public function __construct(Space $space, protected $plan, protected $mode, protected $service = [])
    {
        if (empty(self::PLANS[$this->plan])) {
            throw new ValidateException('订阅计划不存在');
        }

        if (!$space->isExpired() && $space->expire_time->diffInDays(Date::now()) > 7 && !empty($this->service)) {
            throw new ValidateException('仅在空间订阅计划到期 7 天内续费时可以调整增值项目');
        }

        $this->space = $space;

        $amount = self::PLANS[$this->plan]['price'];

        foreach (Recharge::SERVICES as $key => $item) {
            if (!empty($this->service) && empty($this->service[$key])) {
                continue;
            }
            $nums   = (int) ($this->space[$key] ?? 0);
            $amount += $item['price'] * $nums;
        }

        $this->amount = $amount * ($this->mode == 'month' ? 1 : 10) * 100;
    }

    public function invoke(Order $order)
    {
        $expireTime = value(function () {
            $unit       = $this->mode == 'month' ? 'month' : 'year';
            $now        = Date::now();
            $expireTime = $this->space->expire_time;
            $isExpired  = $expireTime->lte($now);

            if ($isExpired) {
                return $now->addUnit($unit);
            }

            $price     = self::PLANS[$this->space->plan]['price'] ?? null;
            $nextPrice = self::PLANS[$this->plan]['price'] ?? null;

            if ($price && $nextPrice && $this->plan != $this->space->plan) {
                $days       = $expireTime->diffInDays($now);
                $nextDays   = ceil($days * $price / $nextPrice);
                $expireTime = $now->addDays($nextDays);
            }

            return $expireTime->addUnit($unit);
        });

        $this->space->expire_time = $expireTime;
        $this->space->plan        = $this->plan;

        //调整增值服务
        if (!empty($this->service)) {
            foreach ($this->service as $key => $nums) {
                if (!$nums) {
                    $this->space->$key = 0;
                }
            }
        }

        $this->space->save();

        return $this->space;
    }

    public function getSubject()
    {
        $name = self::PLANS[$this->plan]['name'];
        $unit = $this->mode == 'month' ? '个月' : '年';
        return "{$name} 1{$unit}";
    }
}
