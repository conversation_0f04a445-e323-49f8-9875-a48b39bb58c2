<?php

namespace app\lib\goods;

use app\lib\Date;
use app\lib\Goods;
use app\model\Order;
use app\model\Space;
use Exception;
use think\exception\ValidateException;

class Recharge extends Goods
{
    const SERVICES = [
        'token' => [
            'name'  => 'Token额度',
            'price' => 30,
            'unit'  => 'K',
            'step'  => 1000,
        ],
    ];

    public function __construct(Space $space, protected $service, protected $nums = 1)
    {
        if (empty(self::SERVICES[$this->service])) {
            throw new ValidateException('服务不存在');
        }

        $this->space = $space;

        if ($this->space->isExpired() || $this->space->plan == 'trial') {
            throw new ValidateException('当前订阅计划不支持购买增值服务');
        }

        $now   = Date::now();
        $month = max(1, $this->space->expire_time->diffInMonths($now));

        $this->amount = self::SERVICES[$this->service]['price'] * $this->nums * $month * 100;
    }

    public function invoke(Order $order)
    {
        switch ($this->service) {
            case 'token':
                $this->space->token += $this->nums;
                $this->space->save();
                break;
            default:
                throw new Exception('服务不存在');
        }
    }

    public function getSubject()
    {
        $service = self::SERVICES[$this->service];

        $name = $service['name'];

        if (!empty($service['step'])) {
            $nums = $this->nums * $service['step'];
            $name .= " [{$nums}{$service['unit']}]";
        }

        return $name;
    }
}
