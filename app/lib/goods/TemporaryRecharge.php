<?php

namespace app\lib\goods;

use app\lib\Goods;
use app\model\Order;
use app\model\Space;
use think\exception\ValidateException;

class TemporaryRecharge extends Goods
{
    const PRICE_PER_1000K = 50; // 50元/1000K
    const STEP = 1000; // 1000K每档

    public function __construct(Space $space, protected $nums = 1)
    {
        if ($space->isExpired() || $space->plan == 'trial') {
            throw new ValidateException('当前订阅计划不支持购买临时扩容');
        }

        if ($nums < 1 || $nums > 10) {
            throw new ValidateException('购买数量必须在1-10档之间');
        }

        $this->space = $space;
        $this->amount = self::PRICE_PER_1000K * $this->nums * 100; // 转为分
    }

    public function invoke(Order $order)
    {
        // 临时扩容不需要修改任何数据，只需要订单记录即可
        // 额度计算通过查询订单记录实现
    }

    public function getSubject()
    {
        $totalTokens = $this->nums * self::STEP;
        return "临时Token扩容 [{$totalTokens}K]";
    }

    /**
     * 获取指定空间在当前重置周期内的临时扩容总额度
     */
    public static function getTemporaryQuotaForSpace(Space $space)
    {
        $resetTime = $space->reset_time;
        $lastResetTime = $resetTime->copy()->subMonth();

        $temporaryOrders = Order::where('space_id', $space->id)
            ->where('goods_type', self::class)
            ->where('status', 1) // 已支付
            ->where('create_time', '>', $lastResetTime)
            ->where('create_time', '<=', $resetTime)
            ->select();

        $totalTemporaryTokens = 0;
        foreach ($temporaryOrders as $order) {
            $goods = $order->goods;
            if ($goods instanceof self) {
                $totalTemporaryTokens += $goods->nums * self::STEP;
            }
        }

        return $totalTemporaryTokens;
    }
}
