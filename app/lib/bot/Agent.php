<?php

namespace app\lib\bot;

use app\lib\bot\agent\BaseAgent;
use app\lib\bot\agent\tool\DatabaseExecute;
use app\lib\bot\agent\tool\DatasetSearch;
use app\lib\bot\concerns\InteractWithAnnotation;
use app\lib\Hashids;
use app\model\Bot;
use think\helper\Arr;

class Agent extends BaseAgent
{
    use InteractWithAnnotation;

    public function __construct(protected Bot $bot, protected $source = null)
    {
        $this->space = $this->bot->space;
    }

    protected function initTools()
    {
        if (!empty($this->config['tools'])) {
            $credentials = $this->space->getPluginCredentials();

            foreach ($this->config['tools'] as $tool) {
                if (!($tool['enable'] ?? true)) {
                    continue;
                }

                if (str_starts_with($tool['plugin'], 'plugin-')) {
                    $this->addPlugin(substr($tool['plugin'], 7), $tool['name'], $tool['args'] ?? []);
                } else {
                    $plugin = $this->resolvePlugin($tool['plugin']);
                    if ($plugin) {
                        $object = $plugin->getTool($tool['name']);

                        if (isset($credentials[$tool['plugin']])) {
                            $object->setCredentials($credentials[$tool['plugin']]);
                        }

                        $key = "{$tool['plugin']}-{$tool['name']}";

                        $this->addFunction($key, $object, $tool['args'] ?? []);
                    }
                }
            }
        }
    }

    protected function initDataset()
    {
        if (!empty($this->config['dataset']['datasets'])) {
            $ids = array_map(function ($id) {
                return Hashids::decode($id, false);
            }, $this->config['dataset']['datasets']);

            $datasets = $this->space->datasets()
                ->whereIn('id', $ids)
                ->select();

            if (!empty($datasets)) {
                $this->addFunction('dataset-search', new DatasetSearch($datasets));
            }
        }
    }

    protected function initDatabase()
    {
        if (!empty($this->config['database']['databases'])) {
            $ids = array_map(function ($id) {
                return Hashids::decode($id, false);
            }, $this->config['database']['databases']);

            $databases = $this->space->databases()
                ->whereIn('id', $ids)
                ->select();

            foreach ($databases as $database) {
                $this->addFunction("database-execute-{$database->hash_id}", new DatabaseExecute($database, $this->conversation->user_id));
            }
        }
    }

    protected function init($params)
    {
        $this->config = Arr::get($params, 'config', $this->bot->config);
        $this->checkConfig();

        $query = Arr::get($params, 'query', '');

        $variables = Arr::get($params, 'variables');
        $this->checkVariables($variables);

        $files = null;
        if (Arr::get($this->config, 'input.file.enable', false)) {
            $files = Arr::get($params, 'files', []);
            $files = $this->checkFiles($files);
        }

        $conversationId = Arr::get($params, 'conversation');
        $userId         = Arr::get($params, 'user');

        $this->initConversation($conversationId, $userId);
        $this->initMessage($query, $files, $variables);

        if (Arr::get($this->config, 'model.tool', false)) {
            $this->initTools();
            $this->initDataset();
            $this->initDatabase();
        }
    }

    protected function start()
    {
        yield from $this->sendConversation();

        $skip = yield from $this->checkAnnotation();

        if (!$skip) {
            yield from parent::start();
        }
    }
}
