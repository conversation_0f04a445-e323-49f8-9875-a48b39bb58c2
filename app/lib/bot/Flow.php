<?php

namespace app\lib\bot;

use app\Exception;
use app\lib\bot\concerns\InteractWithAnnotation;
use app\lib\bot\concerns\InteractWithConversation;
use app\lib\bot\flow\Context;
use app\lib\bot\flow\Graph;
use app\lib\bot\flow\Node;
use app\model\Bot;
use app\model\Space;
use Generator;
use think\helper\Arr;
use Throwable;

class Flow
{
    use InteractWithConversation, InteractWithAnnotation;

    protected Space $space;
    /** @var \app\lib\bot\flow\Context */
    protected $context;

    protected $config = [];
    protected $round  = 0;
    protected $chunks = [];

    /** @var Graph */
    protected $graph;

    public function __construct(protected Bot $bot, protected $source = null)
    {
        $this->space = $this->bot->space;
    }

    protected function getConfig($name, $default = null)
    {
        return Arr::get($this->config, $name, $default);
    }

    protected function checkVariables($variables)
    {
        foreach ($this->getConfig('feature.variable.variables', []) as $var) {
            if (($var['required'] ?? false) && empty($variables[$var['key']])) {
                throw new Exception("缺少变量{{$var['key']}}");
            }
        }
    }

    protected function checkFiles($files)
    {
        return array_filter($files, function ($file) {
            return !empty($file['name']) && !empty($file['path']) && !empty($file['size']);
        });
    }

    protected function buildGraph()
    {
        $nodes = $this->getConfig('nodes', []);
        $edges = $this->getConfig('edges', []);

        $this->graph = new Graph($this->context, $nodes, $edges);
    }

    protected function init($params)
    {
        $this->config = Arr::get($params, 'config', $this->bot->config);

        $query = Arr::get($params, 'query', '');

        $variables = Arr::get($params, 'variables');
        $this->checkVariables($variables);

        $files = null;
        if (Arr::get($this->config, 'feature.input.file.enable', false)) {
            $files = Arr::get($params, 'files', []);
            $files = $this->checkFiles($files);
        }

        $conversationId = Arr::get($params, 'conversation');
        $userId         = Arr::get($params, 'user');

        $this->initConversation($conversationId, $userId);
        $this->initMessage($query, $files, $variables);

        $this->context = new Context($this->space, $this->bot, $this->conversation, $this->message);
        $this->buildGraph();
    }

    public function run($params)
    {
        $this->init($params);
        try {
            $start = microtime(true);
            yield from $this->start();
            yield from  $this->sendOutput();
        } catch (Throwable $e) {
            yield from $this->sendChunkData($this->round, 'error', $e->getMessage());
        } finally {
            $latency = round((microtime(true) - $start) * 1000);

            $usage = $this->consumeTokens($this->context->getUsage());

            //更新统计
            yield [
                'stats' => [
                    'usage'   => $usage,
                    'latency' => $latency,
                ],
            ];

            if (!empty($this->chunks)) {
                $id = $this->saveMessage($usage, $latency);

                //更新消息ID
                if (!empty($id)) {
                    yield [
                        'id' => $id,
                    ];
                }
            }

            $this->round   = 0;
            $this->chunks  = [];
            $this->context = null;
            $this->graph   = null;
        }
    }

    protected function start()
    {
        yield from $this->sendConversation();

        $skip = yield from $this->checkAnnotation();

        if (!$skip) {
            $node = $this->graph->getStartNode();
            yield from $this->iteration($node);
        }
    }

    protected function iteration(Node $node)
    {
        $chunkIndex = $this->round;
        $this->round++;
        $start = microtime(true);

        yield from $this->sendChunkData($chunkIndex, 'node', [
            'id'    => $node->getId(),
            'title' => $node->getTitle(),
            'type'  => $node->getType(),
            'input' => $node->getInput(),
        ]);

        try {
            $result = app()->invoke([$node, 'run']);

            if (!empty($result)) {
                if ($result instanceof Generator) {
                    foreach ($result as $content) {
                        yield from $this->sendContent($chunkIndex, $content);
                    }
                } else {
                    yield from $this->sendContent($chunkIndex, $result);
                }
            }

            $node->done();
        } catch (Throwable $e) {
            $node->fail();
            yield from $this->sendChunkData($chunkIndex, 'node', [
                'error' => true,
            ]);
            yield from $this->sendChunkData($chunkIndex, 'error', $e->getMessage());
        }

        $latency = microtime(true) - $start;

        yield from $this->sendChunkData($chunkIndex, 'node', [
            'latency' => $latency,
        ]);

        $nodes = $node->getNextNodes();

        foreach ($nodes as $node) {
            yield from $this->iteration($node);
        }
    }

    protected function sendOutput()
    {
        foreach ($this->context->getOutput() as $id => $output) {
            $chunkIndex = array_find_key($this->chunks, function ($chunk) use ($id) {
                return Arr::get($chunk, 'node.id') == $id;
            });

            yield from $this->sendChunkData($chunkIndex, 'node', [
                'output' => $output,
            ]);
        }
    }

    protected function sendContent($chunkIndex, $content)
    {
        if (is_array($content)) {
            ['index' => $index, 'value' => $value] = $content;

            if (!is_array($this->chunks[$chunkIndex]['content'] ?? null)) {
                $this->chunks[$chunkIndex]['content'] = [];
            }
            $this->updateChunk($chunkIndex, "content.{$index}", $value, is_string($value));
            yield [
                'chunks' => [
                    'index'   => $chunkIndex,
                    'content' => $content,
                ],
            ];
        } else {
            yield from $this->sendChunkData($chunkIndex, 'content', $content, true);
        }
    }

    public function sendChunkData($chunkIndex, $key, $value, $append = false)
    {
        $this->updateChunk($chunkIndex, $key, $value, $append);

        yield [
            'chunks' => [
                'index' => $chunkIndex,
                $key    => $value,
            ],
        ];
    }

    protected function updateChunk($chunkIndex, $key, $value, $append = false)
    {
        if (is_array($value)) {
            foreach ($value as $k => $v) {
                Arr::set($this->chunks, "{$chunkIndex}.{$key}.{$k}", $v);
            }
        } else {
            if ($append) {
                $value = Arr::get($this->chunks, "{$chunkIndex}.{$key}", '') . $value;
            }
            Arr::set($this->chunks, "{$chunkIndex}.{$key}", $value);
        }
    }

    protected function consumeTokens(int $usage): int
    {
        $this->space->consumeToken($usage);
        return $usage;
    }
}
