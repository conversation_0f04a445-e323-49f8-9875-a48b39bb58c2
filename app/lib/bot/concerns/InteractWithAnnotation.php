<?php

namespace app\lib\bot\concerns;

use think\ai\Client;

trait InteractWithAnnotation
{
    protected function checkAnnotation()
    {
        if (!empty($this->bot->annotation) && !empty($this->message->query)) {
            //TODO 优化

            $result = app(Client::class)->embeddings()->create([
                'model' => $this->bot->annotation['model'],
                'input' => $this->message->query,
            ]);

            if (isset($this->context)) {
                $this->context->consumeTokens($result['usage']['total_tokens']);
            }
            if (isset($this->usage)) {
                $this->usage += $result['usage']['total_tokens'];
            }

            $annotation = $this->bot->searchAnnotation($result['embeddings']);

            if ($annotation) {
                foreach (preg_split('//u', $annotation->answer, -1, PREG_SPLIT_NO_EMPTY) as $text) {
                    //模拟打字效果
                    yield from $this->sendChunkData(0, 'content', $text, true);
                    usleep(5000);
                }
                return true;
            }
        }
        return false;
    }
}
