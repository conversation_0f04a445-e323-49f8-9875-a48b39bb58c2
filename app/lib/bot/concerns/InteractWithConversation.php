<?php

namespace app\lib\bot\concerns;

use app\job\GenerateConversationNameJob;
use app\lib\Date;
use app\model\Conversation;
use think\facade\Log;

trait InteractWithConversation
{
    /** @var Conversation */
    protected $conversation;

    protected $isNewConversation = false;

    /** @var \app\model\Message */
    protected $message;

    protected function initConversation($conversationId, $userId)
    {
        if (!empty($conversationId)) {
            if ($conversationId instanceof Conversation) {
                $conversation = $conversationId;
            } else {
                $query = Conversation::where('space_id', $this->space->id)
                    ->where('bot_id', $this->bot->id)
                    ->where('id', $conversationId);

                if (!empty($userId)) {
                    $query->where('user_id', $userId);
                }

                $conversation = $query->find();
            }
        }

        if (empty($conversation)) {
            $conversation = Conversation::create([
                'space_id' => $this->space->id,
                'bot_id'   => $this->bot->id,
                'user_id'  => $userId,
                'source'   => $this->source,
            ]);

            $this->isNewConversation = true;
        } else {
            $conversation->save(['update_time' => Date::now()]);
        }

        $this->conversation = $conversation;
    }

    protected function initMessage($query, $files = null, $variables = null)
    {
        $this->message = $this->conversation->messages()->make([
            'space_id'  => $this->space->id,
            'bot_id'    => $this->bot->id,
            'query'     => $query,
            'files'     => json_encode($files),
            'variables' => json_encode($variables),
        ]);
    }

    protected function sendConversation()
    {
        yield ['conversation' => $this->conversation->id];
    }

    protected function saveMessage($usage, $latency)
    {
        try {
            $this->message->save([
                'chunks'  => $this->chunks,
                'usage'   => $usage,
                'latency' => $latency,
            ]);

            if ($this->isNewConversation && $this->source != 'dev') {
                //自动生成标题
                queue(new GenerateConversationNameJob($this->conversation, $this->message->content));
            }

            return $this->message->id;
        } catch (\Exception $e) {
            Log::write($e->getMessage(), 'error');
        }
    }
}
