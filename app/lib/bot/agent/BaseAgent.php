<?php

namespace app\lib\bot\agent;

use app\Exception;
use app\lib\bot\concerns\InteractWithConversation;
use app\lib\Date;
use app\model\Plugin;
use app\model\Space;
use RuntimeException;
use think\agent\Agent;
use think\ai\Client;
use think\Filesystem;
use think\helper\Arr;

abstract class BaseAgent extends Agent
{
    use InteractWithConversation;

    protected $canUseTool      = true;
    protected $resolvedPlugins = [];

    protected Space $space;

    protected function checkConfig()
    {
        if (empty($this->config['model']['name'])) {
            throw new RuntimeException('请配置模型');
        }
    }

    protected function getConfig($name, $default = null)
    {
        return Arr::get($this->config, $name, $default);
    }

    protected function checkVariables($variables)
    {
        foreach ($this->getConfig('variable.variables', []) as $var) {
            if (($var['required'] ?? false) && empty($variables[$var['key']])) {
                throw new Exception("缺少变量{{$var['key']}}");
            }
        }
    }

    protected function checkFiles($files)
    {
        return array_filter($files, function ($file) {
            return !empty($file['name']) && !empty($file['path']) && !empty($file['size']);
        });
    }

    protected function resolvePlugin($name)
    {
        if (!array_key_exists($name, $this->resolvedPlugins)) {
            $this->resolvedPlugins[$name] = Plugin::resolve($this->space, $name);
        }
        return $this->resolvedPlugins[$name];
    }

    protected function buildPromptMessages()
    {
        $promptMessages = [];

        if (!empty($this->config['prompt'])) {
            $promptMessages[] = [
                'role'    => 'system',
                'content' => $this->replaceVars($this->config['prompt'], $this->message->variables ?? []),
            ];
        }

        $contextTokens = Arr::get($this->config, 'model.context_tokens', 0);
        $vision        = Arr::get($this->config, 'model.vision', false);

        $historyRound = Arr::get($this->config, 'model.params.history_round', 5);

        if ($historyRound > 0) {
            $historyMessages = $this->getHistoryMessages($historyRound, $contextTokens);
            $promptMessages  = array_merge($promptMessages, $historyMessages);
        }

        $content = $this->message->content;
        $files   = $this->message->files;

        if ($vision && !empty($files)) {
            $content = [
                [
                    'type' => 'text',
                    'text' => $content,
                ],
                ...$this->getImagesContent($files),
            ];
        }

        $promptMessages[] = [
            'role'    => 'user',
            'content' => $content,
        ];

        return $promptMessages;
    }

    protected function getImagesContent($files)
    {
        $disk = \think\facade\Filesystem::disk('uploads');
        return Arr::flatMap(function ($file) use ($disk) {
            if (!empty($file['url'])) {
                return [
                    [
                        'type'      => 'image_url',
                        'image_url' => [
                            'url' => $file['url'],
                        ],
                    ],
                ];
            }
            if (empty($file['name']) || empty($file['path']) || empty($file['size'])) {
                return [];
            }
            $ext = pathinfo($file['name'], PATHINFO_EXTENSION);
            if (in_array($ext, ['jpg', 'jpeg', 'png', 'gif'])) {
                $content = $disk->read($file['path']);
                //转为base64
                $url = 'data:image/' . $ext . ';base64,' . base64_encode($content);

                return [
                    [
                        'type'      => 'image_url',
                        'image_url' => [
                            'url' => $url,
                        ],
                    ],
                ];
            }

            return [];
        }, $files);
    }

    protected function getHistoryMessages($round, $contextTokens)
    {
        //获取历史记录
        /** @var \app\model\Message[] $messages */
        $messages = $this->conversation->messages()
            ->limit($round)
            ->order('create_time desc')
            ->select();

        return $this->buildHistoryMessages($messages, $contextTokens);
    }

    protected function saveImage($image)
    {
        $disk = app(Filesystem::class)->disk('uploads');

        $name = date('Ymd') . '/' . md5(uniqid());
        $path = "chat/{$name}.png";

        $filename = $disk->path($path);

        $dirname = dirname($filename);
        if (!is_dir($dirname)) {
            mkdir($dirname, 0755, true);
        }

        if (str_starts_with($image, 'http')) {
            $content = fopen($image, 'r');
        } else {
            if (str_starts_with($image, 'data:image/')) {
                $parts = explode(',', $image, 2);
                if (count($parts) == 2) {
                    $image = $parts[1];
                }
            }

            $content = base64_decode($image);
        }

        file_put_contents($filename, $content);

        if (is_resource($content)) {
            fclose($content);
        }

        return $disk->url($path);
    }

    protected function getSystemVars()
    {
        return [
            'time'   => (string) Date::now(),
            'ip'     => request()->ip(),
            'source' => $this->conversation->source_label,
        ];
    }

    protected function consumeTokens(int $usage): int
    {
        $this->space->consumeToken($usage);
        return $usage;
    }

    protected function getClient(): Client
    {
        return app(Client::class);
    }
}
