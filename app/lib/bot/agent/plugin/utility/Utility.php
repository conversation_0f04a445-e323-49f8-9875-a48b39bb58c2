<?php

namespace app\lib\bot\agent\plugin\utility;

use app\lib\bot\agent\plugin\Builtin;
use app\lib\bot\agent\plugin\utility\tools\FileReader;
use app\lib\bot\agent\plugin\utility\tools\Ip;
use app\lib\bot\agent\plugin\utility\tools\Time;
use think\agent\tool\CodeRunner;

class Utility extends Builtin
{
    protected $title       = '实用工具';
    protected $description = '一些实用工具';

    protected $tools = [
        CodeRunner::class,
        FileReader::class,
        Time::class,
        Ip::class,
    ];
}
