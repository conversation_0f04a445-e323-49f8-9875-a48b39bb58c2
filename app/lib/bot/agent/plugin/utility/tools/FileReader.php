<?php

namespace app\lib\bot\agent\plugin\utility\tools;

use think\agent\tool\Args;
use think\agent\tool\FunctionCall;
use think\Filesystem;

class FileReader extends FunctionCall
{
    public $title       = '文档阅读';
    public $description = '支持解析各类型文件（.pdf、.txt、.docx、.md、.pptx、.jpg、.png、.xlsx），并完成文本信息内容的提取';
    public $parameters  = [
        'path' => [
            'type'        => 'string',
            'description' => '文件路径',
            'required'    => true,
        ],
    ];

    public function getFee()
    {
        return 0;
    }

    public function run(Args $args)
    {
        $disk   = app(Filesystem::class)->disk('uploads');
        $path   = $disk->path($args['path']);
        $reader = new \app\lib\FileReader($path);
        return $reader->getText();
    }
}
