<?php

namespace app\lib\bot\agent\plugin\utility\tools;

use app\lib\Date;
use think\agent\tool\Args;
use think\agent\tool\FunctionCall;

class Time extends FunctionCall
{
    public $title       = '获取当前时间';
    public $description = '一个用于获取当前时间的工具。';

    public function getFee()
    {
        return 0;
    }

    protected function run(Args $args)
    {
        return (string) Date::now();
    }
}
