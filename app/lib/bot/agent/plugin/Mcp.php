<?php

namespace app\lib\bot\agent\plugin;

use app\Exception;
use app\lib\mcp\ServerEvent;
use app\lib\mcp\StreamableHttp;
use think\agent\Plugin;
use think\agent\tool\Args;
use think\agent\tool\FunctionCall;
use think\agent\tool\result\Raw;
use think\facade\Cache;
use think\helper\Arr;

class Mcp extends Plugin
{
    protected $client;

    protected $tools;

    public function __construct(protected $url, protected $transport = 'sse', protected $headers = null)
    {
    }

    public function getTools()
    {
        if (is_null($this->tools)) {
            $this->tools = $this->buildTools();
        }
        return $this->tools;
    }

    protected function buildTools()
    {
        if (empty($this->url)) {
            return [];
        }

        $tools = Cache::remember("mcp-tools-{$this->transport}:{$this->url}", function () {
            return $this->getClient()->listTools();
        }, 60 * 10);

        // 使用弱引用避免循环引用
        $clientRef = \WeakReference::create($this);

        return array_map(function ($tool) use ($clientRef) {
            return new class($clientRef, $tool) extends FunctionCall {

                public function __construct(protected \WeakReference $clientRef, $tool)
                {
                    $this->name        = $tool['name'];
                    $this->title       = $tool['name'];
                    $this->description = $tool['description'];
                    $this->parameters  = $tool['inputSchema'];
                }

                public function getParameters()
                {
                    $required   = Arr::get($this->parameters, 'required', []);
                    $parameters = Arr::get($this->parameters, 'properties', []);
                    foreach ($required as $key) {
                        if (isset($parameters[$key])) {
                            $parameters[$key]['required'] = true;
                        }
                    }
                    return $parameters;
                }

                public function getFee()
                {
                    return 0;
                }

                public function getLlmDescription()
                {
                    return $this->description;
                }

                public function getLlmParameters()
                {
                    return $this->parameters;
                }

                protected function getClient()
                {
                    $mcp = $this->clientRef->get();
                    if ($mcp === null) {
                        throw new Exception('MCP plugin instance has been garbage collected');
                    }
                    return $mcp->getClient();
                }

                protected function run(Args $args)
                {
                    $result = $this->getClient()->callTool($this->name, [...$args]);

                    return new Raw([
                        'response' => json_encode(Arr::get($result, 'content'), JSON_UNESCAPED_UNICODE),
                        'error'    => Arr::get($result, 'isError', false),
                    ]);
                }
            };
        }, $tools);
    }

    public function getClient()
    {
        if (empty($this->client)) {
            $this->client = match ($this->transport) {
                'sse' => ServerEvent::connect($this->url, $this->headers),
                'http', 'streamable-http', 'streamable_http' => StreamableHttp::connect($this->url, $this->headers),
                default => throw new Exception("Unsupported transport: {$this->transport}")
            };
        }
        return $this->client;
    }

    /**
     * 手动关闭连接
     * 建议在不再需要使用插件时调用此方法
     */
    public function close()
    {
        if (!empty($this->client)) {
            $this->client->close();
            $this->client = null;
        }
    }

    public function __destruct()
    {
        $this->close();
    }
}
