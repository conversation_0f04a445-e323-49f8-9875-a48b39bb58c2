openapi: "3.1.0"
info:
  version: 1.0.0
  title: 知识管理
  description: 知识管理
servers:
  - url: https://k.topthink.com/api
paths:
  /book/{id}/search:
    get:
      summary: "文档检索"
      description: "搜索知识管理中相关文档内容用于回答用户问题"
      operationId: search
      parameters:
        - name: keyword
          in: query
          description: "The user's search query term. The term may not be empty."
          required: true
          schema:
            type: string
        - name: id
          in: path
          description: "文档ID"
          required: true
          provider: user
          schema:
            type: string
