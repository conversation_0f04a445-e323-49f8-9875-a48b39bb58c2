<?php

namespace app\lib\bot\agent\plugin\knowledge;

use think\agent\OpenApi;

class Knowledge extends OpenApi
{
    protected $title       = '知识管理';
    protected $description = '基于顶想云知识管理的接口';

    protected $auth = [
        'provider'     => 'user',
        'type'         => 'http',
        'scheme'       => 'bearer',
        'bearerFormat' => 'Token',
        'token'        => [
            'title' => '访问令牌',
            'url'   => 'https://doc.topthink.com/think-bot/knowledge-base.html#%E7%AC%AC%E4%BA%8C%E7%A7%8D%E6%96%B9%E5%BC%8F%E4%BD%BF%E7%94%A8%E7%9F%A5%E8%AF%86%E7%AE%A1%E7%90%86%E5%B7%A5%E5%85%B7',
        ],
    ];
}
