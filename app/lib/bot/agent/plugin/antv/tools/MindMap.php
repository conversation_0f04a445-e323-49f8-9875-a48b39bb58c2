<?php

namespace app\lib\bot\agent\plugin\antv\tools;

use think\agent\tool\Args;

class MindMap extends Tool
{
    public $title       = '生成思维导图';
    public $description = '生成一个思维导图，它以层次化结构组织和呈现信息，从中心主题向外辐射出各个分支，例如，展示主话题与其子话题之间关系的图示。';
    public $parameters  = [
        'data'   => [
            'type'        => 'string',
            'description' => 'Data for mind map chart, such as, { name:\'main topic\', children:[{ name:\'topic 1\', children:[{ name:\'subtopic 1-1\' }] }.',
            'required'    => true,
        ],
        'width'  => [
            'type'        => 'number',
            'description' => 'Set the width of chart, default is 600.',
            'required'    => false,
        ],
        'height' => [
            'type'        => 'number',
            'description' => 'Set the height of chart, default is 400.',
            'required'    => false,
        ],
        'title'  => [
            'type'        => 'string',
            'description' => 'Set the title of chart.',
            'required'    => false,
        ],
    ];

    protected function run(Args $args)
    {
        $data   = json_decode($args->get('data'), true);
        $width  = $args->get('width', 600);
        $height = $args->get('height', 400);
        $title  = $args->get('title', '');

        $options = [
            'type'   => 'mind-map',
            'data'   => $data,
            'width'  => $width,
            'height' => $height,
            'title'  => $title,
        ];

        return $this->generateImage($options);
    }
}
