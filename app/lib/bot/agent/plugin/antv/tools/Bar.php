<?php

namespace app\lib\bot\agent\plugin\antv\tools;

use think\agent\tool\Args;

class Bar extends Tool
{
    public $title       = '生成条形图';
    public $description = '生成一个条形图来展示不同类别之间的数值比较，例如，对比不同种类数据并且进行横向比较。';
    public $parameters  = [
        'data'       => [
            'type'        => 'string',
            'description' => 'Data of bar chart, such as, [{ category:\'2018\', value:99.9 }].',
            'required'    => true,
        ],
        'stack'      => [
            'type'        => 'boolean',
            'description' => 'Whether stacking is enabled. When enabled, bar charts require a \'group\' field in the data.',
            'required'    => false,
        ],
        'group'      => [
            'type'        => 'boolean',
            'description' => 'Whether grouping is enabled. When enabled, column charts require a \'group\' field in the data. When `group` is true, `stack` should be false.',
            'required'    => false,
        ],
        'width'      => [
            'type'        => 'number',
            'description' => 'Set the width of chart, default is 600.',
            'required'    => false,
        ],
        'height'     => [
            'type'        => 'number',
            'description' => 'Set the height of chart, default is 400.',
            'required'    => false,
        ],
        'title'      => [
            'type'        => 'string',
            'description' => 'Set the title of chart.',
            'required'    => false,
        ],
        'axisXTitle' => [
            'type'        => 'string',
            'description' => 'Set the axisXTitle of chart.',
            'required'    => false,
        ],
        'axisYTitle' => [
            'type'        => 'string',
            'description' => 'Set the axisYTitle of chart.',
            'required'    => false,
        ],
    ];

    protected function run(Args $args)
    {
        $width      = $args->get('width', 600);
        $height     = $args->get('height', 400);
        $title      = $args->get('title', '');
        $stack      = $args->get('stack', false);
        $group      = $args->get('group', false);
        $axisXTitle = $args->get('axisXTitle', '');
        $axisYTitle = $args->get('axisYTitle', '');
        $data       = json_decode($args->get('data'), true);

        $options = [
            'type'       => 'bar',
            'data'       => $data,
            'stack'      => $stack,
            'group'      => $group,
            'width'      => $width,
            'height'     => $height,
            'title'      => $title,
            'axisXTitle' => $axisXTitle,
            'axisYTitle' => $axisYTitle,
        ];

        return $this->generateImage($options);
    }
}
