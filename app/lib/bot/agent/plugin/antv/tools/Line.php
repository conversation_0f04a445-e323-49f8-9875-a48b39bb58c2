<?php

namespace app\lib\bot\agent\plugin\antv\tools;

use think\agent\tool\Args;

class Line extends Tool
{
    public $title       = '生成折线图';
    public $description = '生成一个折线图来显示随时间变化的趋势，例如，2000 年到 2016 年苹果电脑销量与苹果公司利润比例的变化。';
    public $parameters  = [
        'data'       => [
            'type'        => 'string',
            'description' => 'Data of line chart, such as, [{ time:\'2018\', value:99.9 }].',
            'required'    => true,
        ],
        'width'      => [
            'type'        => 'number',
            'description' => 'Set the width of chart, default is 600.',
            'required'    => false,
        ],
        'height'     => [
            'type'        => 'number',
            'description' => 'Set the height of chart, default is 400.',
            'required'    => false,
        ],
        'title'      => [
            'type'        => 'string',
            'description' => 'Set the title of chart.',
            'required'    => false,
        ],
        'axisXTitle' => [
            'type'        => 'string',
            'description' => 'Set the axisXTitle of chart.',
            'required'    => false,
        ],
        'axisYTitle' => [
            'type'        => 'string',
            'description' => 'Set the axisYTitle of chart.',
            'required'    => false,
        ],
    ];

    protected function run(Args $args)
    {
        $width      = $args->get('width', 600);
        $height     = $args->get('height', 400);
        $title      = $args->get('title', '');
        $axisXTitle = $args->get('axisXTitle', '');
        $axisYTitle = $args->get('axisYTitle', '');
        $data       = json_decode($args->get('data'), true);

        $options = [
            'type'       => 'line',
            'data'       => $data,
            'width'      => $width,
            'height'     => $height,
            'title'      => $title,
            'axisXTitle' => $axisXTitle,
            'axisYTitle' => $axisYTitle,
        ];

        return $this->generateImage($options);
    }
}
