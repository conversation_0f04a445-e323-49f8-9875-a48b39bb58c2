<?php

namespace app\lib\bot\agent\plugin\antv\tools;

use think\agent\tool\Args;

class DualAxes extends Tool
{
    public $title       = '生成双轴图';
    public $description = '生成一个双轴图，双轴图是一种组合图表，通常结合柱状图和折线图来同时展示数据的趋势和对比，例如随时间变化的销售和利润趋势。';
    public $parameters  = [
        'categories' => [
            'type'        => 'string',
            'description' => 'Categories for dual axes chart, such as, [\'2015\', \'2016\', \'2017\'].',
            'required'    => true,
        ],
        'series'     => [
            'type'        => 'string',
            'description' => 'Series for dual axes chart, such as, [{ "type":"column", "data":[91.9, 99.1, 101.6, 114.4, 121],"axisYTitle":"销售额" },{"type":"line","data":[0.055, 0.06, 0.062, 0.07, 0.075],"axisYTitle":"利润率"}].',
            'required'    => true,
        ],
        'width'      => [
            'type'        => 'number',
            'description' => 'Set the width of chart, default is 600.',
            'required'    => false,
        ],
        'height'     => [
            'type'        => 'number',
            'description' => 'Set the height of chart, default is 400.',
            'required'    => false,
        ],
        'title'      => [
            'type'        => 'string',
            'description' => 'Set the title of chart.',
            'required'    => false,
        ],
        'axisXTitle' => [
            'type'        => 'string',
            'description' => 'Set the axisXTitle of chart.',
            'required'    => false,
        ],
    ];

    protected function run(Args $args)
    {
        $width      = $args->get('width', 600);
        $height     = $args->get('height', 400);
        $title      = $args->get('title', '');
        $axisXTitle = $args->get('axisXTitle', '');
        $categories = json_decode($args->get('categories'), true);
        $series     = json_decode($args->get('series'), true);

        $options = [
            'type'       => 'dual-axes',
            'categories' => $categories,
            'series'     => $series,
            'width'      => $width,
            'height'     => $height,
            'title'      => $title,
            'axisXTitle' => $axisXTitle,
        ];

        return $this->generateImage($options);
    }
}
