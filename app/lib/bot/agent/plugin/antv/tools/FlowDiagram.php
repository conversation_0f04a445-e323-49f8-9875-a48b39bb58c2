<?php

namespace app\lib\bot\agent\plugin\antv\tools;

use think\agent\tool\Args;

class FlowDiagram extends Tool
{
    public $title       = '生成流程图';
    public $description = '生成流程图，用于直观地表示过程或系统的步骤和决策点。它展示了从开始到结束的整个流程。每个节点代表一个特定的步骤或决策点，边则表示步骤之间的顺序和关系。';
    public $parameters  = [
        'data'   => [
            'type'        => 'string',
            'description' => 'Data for flow diagram chart, such as, { nodes:[{ name:\'node1\' }, { name:\'node2\' }], edges:[{ source:\'node1\', target:\'node2\', name:\'edge1\' }] }.',
            'required'    => true,
        ],
        'width'  => [
            'type'        => 'number',
            'description' => 'Set the width of chart, default is 600.',
            'required'    => false,
        ],
        'height' => [
            'type'        => 'number',
            'description' => 'Set the height of chart, default is 400.',
            'required'    => false,
        ],
        'title'  => [
            'type'        => 'string',
            'description' => 'Set the title of chart.',
            'required'    => false,
        ],
    ];

    protected function run(Args $args)
    {
        $data   = json_decode($args->get('data'), true);
        $width  = $args->get('width', 600);
        $height = $args->get('height', 400);
        $title  = $args->get('title', '');

        $options = [
            'type'   => 'flow-diagram',
            'data'   => $data,
            'width'  => $width,
            'height' => $height,
            'title'  => $title,
        ];

        return $this->generateImage($options);
    }
}
