<?php

namespace app\lib\bot\agent\plugin\antv\tools;

use think\agent\tool\Args;

class Pie extends Tool
{
    public $title       = '生成饼图';
    public $description = '生成一个饼图来展示各部分的比例，例如市场占有率和预算分配。';
    public $parameters  = [
        'data'        => [
            'type'        => 'string',
            'description' => 'Data of pie chart, such as, [{ category:\'2018\', value:99.9 }].',
            'required'    => true,
        ],
        'innerRadius' => [
            'type'        => 'number',
            'description' => 'Set the innerRadius of pie chart, the value between 0 and 1. Set the pie chart as a donut chart. Set the value to 0.6 or number in [0 ,1] to enable it.',
            'required'    => false,
        ],
        'width'       => [
            'type'        => 'number',
            'description' => 'Set the width of chart, default is 600.',
            'required'    => false,
        ],
        'height'      => [
            'type'        => 'number',
            'description' => 'Set the height of chart, default is 400.',
            'required'    => false,
        ],
        'title'       => [
            'type'        => 'string',
            'description' => 'Set the title of chart.',
            'required'    => false,
        ],
    ];

    protected function run(Args $args)
    {
        $width       = $args->get('width', 600);
        $height      = $args->get('height', 400);
        $title       = $args->get('title', '');
        $innerRadius = $args->get('innerRadius', 0);
        $data        = json_decode($args->get('data'), true);

        $options = [
            'type'        => 'pie',
            'data'        => $data,
            'innerRadius' => $innerRadius,
            'width'       => $width,
            'height'      => $height,
            'title'       => $title,
        ];

        return $this->generateImage($options);
    }
}
