<?php

namespace app\lib\bot\agent\plugin\antv\tools;

use think\agent\tool\Args;

class NetworkGraph extends Tool
{
    public $title       = '生成网络图';
    public $description = '生成一个网络图来展示实体（节点）之间的关系（边），例如，社交网络中人与人之间的关系。';
    public $parameters  = [
        'data'   => [
            'type'        => 'string',
            'description' => 'Data for network graph chart, such as, { nodes:[{ name:\'node1\' }, { name:\'node2\' }], edges:[{ source:\'node1\', target:\'node2\', name:\'edge1\' }] }.',
            'required'    => true,
        ],
        'width'  => [
            'type'        => 'number',
            'description' => 'Set the width of chart, default is 600.',
            'required'    => false,
        ],
        'height' => [
            'type'        => 'number',
            'description' => 'Set the height of chart, default is 400.',
            'required'    => false,
        ],
        'title'  => [
            'type'        => 'string',
            'description' => 'Set the title of chart.',
            'required'    => false,
        ],
    ];

    protected function run(Args $args)
    {
        $data   = json_decode($args->get('data'), true);
        $width  = $args->get('width', 600);
        $height = $args->get('height', 400);
        $title  = $args->get('title', '');

        $options = [
            'type'   => 'network-graph',
            'data'   => $data,
            'width'  => $width,
            'height' => $height,
            'title'  => $title,
        ];

        return $this->generateImage($options);
    }
}
