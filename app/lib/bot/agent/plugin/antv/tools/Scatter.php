<?php

namespace app\lib\bot\agent\plugin\antv\tools;

use think\agent\tool\Args;

class Scatter extends Tool
{
    public $title       = '生成散点图';
    public $description = '生成一个散点图来展示两个变量之间的关系，有助于发现它们之间的关系或趋势，例如相关性的强度、数据分布模式。';
    public $parameters  = [
        'data'   => [
            'type'        => 'string',
            'description' => 'Data of scatter chart, such as, [{ x:10, y:15 }].',
            'required'    => true,
        ],
        'width'  => [
            'type'        => 'number',
            'description' => 'Set the width of chart, default is 600.',
            'required'    => false,
        ],
        'height' => [
            'type'        => 'number',
            'description' => 'Set the height of chart, default is 400.',
            'required'    => false,
        ],
        'title'  => [
            'type'        => 'string',
            'description' => 'Set the title of chart.',
            'required'    => false,
        ],
    ];

    protected function run(Args $args)
    {
        $data   = json_decode($args->get('data'), true);
        $width  = $args->get('width', 600);
        $height = $args->get('height', 400);
        $title  = $args->get('title', '');

        $options = [
            'type'   => 'scatter',
            'data'   => $data,
            'width'  => $width,
            'height' => $height,
            'title'  => $title,
        ];

        return $this->generateImage($options);
    }
}
