<?php

namespace app\lib\bot\agent\plugin\antv\tools;

use think\agent\tool\Args;

class WordCloud extends Tool
{
    public $title       = '生成词云图';
    public $description = '生成一个词云图，通过文本大小的变化来展示词频或权重，例如，分析社交媒体、评论或反馈中的常见词。';
    public $parameters  = [
        'data'   => [
            'type'        => 'string',
            'description' => 'Data of word cloud chart, such as, [{ "value":9.23723855786, "text":"之源" }].',
            'required'    => true,
        ],
        'width'  => [
            'type'        => 'number',
            'description' => 'Set the width of chart, default is 600.',
            'required'    => false,
        ],
        'height' => [
            'type'        => 'number',
            'description' => 'Set the height of chart, default is 400.',
            'required'    => false,
        ],
        'title'  => [
            'type'        => 'string',
            'description' => 'Set the title of chart.',
            'required'    => false,
        ],
    ];

    protected function run(Args $args)
    {
        $data   = json_decode($args->get('data'), true);
        $width  = $args->get('width', 600);
        $height = $args->get('height', 400);
        $title  = $args->get('title', '');

        $options = [
            'type'   => 'word-cloud',
            'data'   => $data,
            'width'  => $width,
            'height' => $height,
            'title'  => $title,
        ];

        return $this->generateImage($options);
    }
}
