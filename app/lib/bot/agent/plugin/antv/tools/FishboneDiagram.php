<?php

namespace app\lib\bot\agent\plugin\antv\tools;

use think\agent\tool\Args;

class FishboneDiagram extends Tool
{
    public $title       = '生成鱼骨图';
    public $description = '生成鱼骨图，它是一种以核心问题为鱼头，通过鱼骨分支的形式分析和展示问题原因或结果的图表。它利用鱼骨的结构，将问题分解为多个类别，并在每个类别下进一步细分具体原因或结果，从而清晰地展现问题的全貌。';
    public $parameters  = [
        'data'   => [
            'type'        => 'string',
            'description' => 'Data for fishbone diagram chart, such as, { name:\'main topic\', children:[{ name:\'topic 1\', children:[{ name:\'subtopic 1-1\' }] }.',
            'required'    => true,
        ],
        'width'  => [
            'type'        => 'number',
            'description' => 'Set the width of chart, default is 600.',
            'required'    => false,
        ],
        'height' => [
            'type'        => 'number',
            'description' => 'Set the height of chart, default is 400.',
            'required'    => false,
        ],
        'title'  => [
            'type'        => 'string',
            'description' => 'Set the title of chart.',
            'required'    => false,
        ],
    ];

    protected function run(Args $args)
    {
        $data   = json_decode($args->get('data'), true);
        $width  = $args->get('width', 600);
        $height = $args->get('height', 400);
        $title  = $args->get('title', '');

        $options = [
            'type'   => 'fishbone-diagram',
            'data'   => $data,
            'width'  => $width,
            'height' => $height,
            'title'  => $title,
        ];

        return $this->generateImage($options);
    }
}
