<?php

namespace app\lib\bot\agent\plugin\antv\tools;

use think\agent\tool\Args;

class Treemap extends Tool
{
    public $title       = '生成矩阵树图';
    public $description = '生成一个矩阵树图来展示层次化数据，并能直观地显示同级项目之间的比较，例如，用矩阵树图显示磁盘空间使用情况。';
    public $parameters  = [
        'data'   => [
            'type'        => 'string',
            'description' => 'Data of word treemap chart, such as, [{ name:\'分类 1\', value:560 }].',
            'required'    => true,
        ],
        'width'  => [
            'type'        => 'number',
            'description' => 'Set the width of chart, default is 600.',
            'required'    => false,
        ],
        'height' => [
            'type'        => 'number',
            'description' => 'Set the height of chart, default is 400.',
            'required'    => false,
        ],
        'title'  => [
            'type'        => 'string',
            'description' => 'Set the title of chart.',
            'required'    => false,
        ],
    ];

    protected function run(Args $args)
    {
        $data   = json_decode($args->get('data'), true);
        $width  = $args->get('width', 600);
        $height = $args->get('height', 400);
        $title  = $args->get('title', '');

        $options = [
            'type'   => 'treemap',
            'data'   => $data,
            'width'  => $width,
            'height' => $height,
            'title'  => $title,
        ];

        return $this->generateImage($options);
    }
}
