<?php

namespace app\lib\bot\agent\plugin\antv\tools;

use think\agent\tool\Args;

class Histogram extends Tool
{
    public $title       = '生成直方图';
    public $description = '生成一个直方图来显示特定范围内数据点的频率，可以观察数据分布情况，例如正态分布和偏态分布，并识别数据集中区域和极端值。';
    public $parameters  = [
        'data'      => [
            'type'        => 'string',
            'description' => 'Data of histogram chart, such as, [{ category:\'2018\', value:99.9 }].',
            'required'    => true,
        ],
        'binNumber' => [
            'type'        => 'number',
            'description' => 'Number of intervals to define the number of intervals in a histogram.',
            'required'    => false,
        ],
        'width'     => [
            'type'        => 'number',
            'description' => 'Set the width of chart, default is 600.',
            'required'    => false,
        ],
        'height'    => [
            'type'        => 'number',
            'description' => 'Set the height of chart, default is 400.',
            'required'    => false,
        ],
        'title'     => [
            'type'        => 'string',
            'description' => 'Set the title of chart.',
            'required'    => false,
        ],
    ];

    protected function run(Args $args)
    {
        $width     = $args->get('width', 600);
        $height    = $args->get('height', 400);
        $title     = $args->get('title', '');
        $binNumber = $args->get('binNumber', 10);
        $data      = json_decode($args->get('data'), true);

        $options = [
            'type'      => 'histogram',
            'data'      => $data,
            'binNumber' => $binNumber,
            'width'     => $width,
            'height'    => $height,
            'title'     => $title,
        ];

        return $this->generateImage($options);
    }
}
