<?php

namespace app\lib\bot\agent\plugin\antv\tools;

use think\agent\tool\Args;

class Radar extends Tool
{
    public $title       = '生成雷达图';
    public $description = '生成一个雷达图来展示多维数据（四个维度或更多），例如，从五个维度（易用性、功能性、摄像头、基准测试得分和电池续航）评估华为和苹果手机。';
    public $parameters  = [
        'data'   => [
            'type'        => 'string',
            'description' => 'Data of radar chart, such as, [{ category:\'2018\', value:99.9 }].',
            'required'    => true,
        ],
        'width'  => [
            'type'        => 'number',
            'description' => 'Set the width of chart, default is 600.',
            'required'    => false,
        ],
        'height' => [
            'type'        => 'number',
            'description' => 'Set the height of chart, default is 400.',
            'required'    => false,
        ],
        'title'  => [
            'type'        => 'string',
            'description' => 'Set the title of chart.',
            'required'    => false,
        ],
    ];

    protected function run(Args $args)
    {
        $width  = $args->get('width', 600);
        $height = $args->get('height', 400);
        $title  = $args->get('title', '');
        $data   = json_decode($args->get('data'), true);

        $options = [
            'type'   => 'radar',
            'data'   => $data,
            'width'  => $width,
            'height' => $height,
            'title'  => $title,
        ];

        return $this->generateImage($options);
    }
}
