<?php

namespace app\lib\bot\agent\plugin\antv\tools;

use app\Exception;
use GuzzleHttp\Client;
use think\agent\tool\FunctionCall;
use think\agent\tool\result\Image;
use think\helper\Arr;

abstract class Tool extends FunctionCall
{
    public function getFee()
    {
        return 0;
    }

    protected function generateImage($options)
    {
        $client   = new Client();
        $response = $client->post('https://antv-studio.alipay.com/api/gpt-vis', [
            'json' => $options,
        ]);

        $result = json_decode($response->getBody()->getContents(), true);

        $success = Arr::get($result, 'success', false);

        if (!$success) {
            throw new Exception(Arr::get($result, 'errorMessage', '生成图表失败'));
        }

        return new Image($result['resultObj']);
    }
}
