<?php

namespace app\lib\bot\agent\plugin\antv\tools;

use think\agent\tool\Args;

class Area extends Tool
{
    public $title       = '生成面积图';
    public $description = '生成一个面积图来表示在连续自变量下的数据趋势，并观察整体数据趋势，例如，位移 = 速度（平均速度或瞬时速度）× 时间:s = v × t。如果 x 轴是时间（t），y 轴是每个时刻的速度（v），则面积图可以让您观察速度随时间的变化趋势，并通过面积的大小推断出所行驶的距离。';
    public $parameters  = [
        'data'       => [
            'type'        => 'string',
            'description' => 'Data of arae chart, such as, [{ time:\'2018\', value:99.9 }].',
            'required'    => true,
        ],
        'stack'      => [
            'type'        => 'boolean',
            'description' => 'Whether stacking is enabled. When enabled, area charts require a \'group\' field in the data.',
            'required'    => false,
        ],
        'width'      => [
            'type'        => 'number',
            'description' => 'Set the width of chart, default is 600.',
            'required'    => false,
        ],
        'height'     => [
            'type'        => 'number',
            'description' => 'Set the height of chart, default is 400.',
            'required'    => false,
        ],
        'title'      => [
            'type'        => 'string',
            'description' => 'Set the title of chart.',
            'required'    => false,
        ],
        'axisXTitle' => [
            'type'        => 'string',
            'description' => 'Set the axisXTitle of chart.',
            'required'    => false,
        ],
        'axisYTitle' => [
            'type'        => 'string',
            'description' => 'Set the axisYTitle of chart.',
            'required'    => false,
        ],
    ];

    protected function run(Args $args)
    {
        $width      = $args->get('width', 600);
        $height     = $args->get('height', 400);
        $title      = $args->get('title', '');
        $axisXTitle = $args->get('axisXTitle', '');
        $axisYTitle = $args->get('axisYTitle', '');
        $stack      = $args->get('stack', false);
        $data       = json_decode($args->get('data'), true);

        $options = [
            'type'       => 'area',
            'width'      => $width,
            'height'     => $height,
            'title'      => $title,
            'axisXTitle' => $axisXTitle,
            'axisYTitle' => $axisYTitle,
            'stack'      => $stack,
            'data'       => $data,
        ];

        return $this->generateImage($options);
    }
}
