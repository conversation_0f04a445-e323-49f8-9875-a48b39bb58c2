<?php

namespace app\lib\bot\agent\plugin\antv;

use app\lib\bot\agent\plugin\antv\tools\Area;
use app\lib\bot\agent\plugin\antv\tools\Bar;
use app\lib\bot\agent\plugin\antv\tools\Column;
use app\lib\bot\agent\plugin\antv\tools\DualAxes;
use app\lib\bot\agent\plugin\antv\tools\FishboneDiagram;
use app\lib\bot\agent\plugin\antv\tools\FlowDiagram;
use app\lib\bot\agent\plugin\antv\tools\Histogram;
use app\lib\bot\agent\plugin\antv\tools\Line;
use app\lib\bot\agent\plugin\antv\tools\MindMap;
use app\lib\bot\agent\plugin\antv\tools\NetworkGraph;
use app\lib\bot\agent\plugin\antv\tools\Pie;
use app\lib\bot\agent\plugin\antv\tools\Radar;
use app\lib\bot\agent\plugin\antv\tools\Scatter;
use app\lib\bot\agent\plugin\antv\tools\Treemap;
use app\lib\bot\agent\plugin\antv\tools\WordCloud;
use app\lib\bot\agent\plugin\Builtin;

class Antv extends Builtin
{
    protected $title       = '数据可视化';
    protected $description = '基于 AntV 可视化解决方案封装的可视化图表生成插件，支持 15+ 常用可视化图表，通过工具创建各种类型的图表。';

    protected $tools = [
        Line::class,
        Column::class,
        Pie::class,
        Area::class,
        Bar::class,
        Histogram::class,
        Scatter::class,
        Treemap::class,
        WordCloud::class,
        DualAxes::class,
        Radar::class,
        MindMap::class,
        NetworkGraph::class,
        FlowDiagram::class,
        FishboneDiagram::class,
    ];
}
