<?php

namespace app\lib\bot\agent\tool;

use app\model\Dataset;
use app\model\Setting;
use think\agent\tool\Args;
use think\agent\tool\FunctionCall;
use think\ai\Client;

class DatasetSearch extends FunctionCall
{
    public $title       = '知识库';
    public $description = '搜索相关内容用于回答用户问题';
    public $parameters  = [
        'query' => [
            'type'        => 'string',
            'description' => '用户的问题',
            'required'    => true,
        ],
    ];

    public function __construct(protected $datasets)
    {

    }

    public function run(Args $args)
    {
        $query = $args['query'];

        $chunks = array_merge(...$this->datasets->map(function (Dataset $dataset) use ($query) {
            return $dataset->search($query);
        })->toArray());
        $chunks = array_filter($chunks, function ($chunk) {
            return !empty($chunk['payload']['content']);
        });
        $model  = Setting::read('model.rerank');
        if (!empty($chunks) && !empty($model)) {
            //重排
            $chunks = app(Client::class)->rerank()->create([
                'model'     => $model,
                'query'     => $query,
                'documents' => array_map(function ($item) {
                    $content = $item['payload']['content'];
                    if (!empty($item['payload']['title'])) {
                        $content = "{$item['payload']['title']}{$content}";
                    }
                    return $content;
                }, $chunks),
                'score'     => 0.1,
            ])['documents'];
        }

        if (empty($chunks)) {
            return '未在[知识库]中找到相关内容';
        }

        return array_reduce(array_slice($chunks, 0, 3), function ($carry, $item) {
            return "{$carry}{$item}\n";
        }, '');
    }
}
