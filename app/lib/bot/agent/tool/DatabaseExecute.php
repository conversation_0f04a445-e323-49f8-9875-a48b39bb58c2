<?php

namespace app\lib\bot\agent\tool;

use app\model\Database;
use app\model\DatabaseField;
use PhpMyAdmin\SqlParser\Components\ArrayObj;
use PhpMyAdmin\SqlParser\Components\Condition;
use Php<PERSON>yAdmin\SqlParser\Parser;
use PhpMyAdmin\SqlParser\Statements\DeleteStatement;
use PhpMyAdmin\SqlParser\Statements\InsertStatement;
use PhpMyAdmin\SqlParser\Statements\SelectStatement;
use PhpMyAdmin\SqlParser\Statements\UpdateStatement;
use PhpMyAdmin\SqlParser\Utils\Query;
use think\agent\tool\Args;
use think\agent\tool\FunctionCall;
use think\db\Connection;

class DatabaseExecute extends FunctionCall
{
    protected $title      = '数据库';
    protected $parameters = [
        'sql' => [
            'type'        => 'string',
            'description' => '需要执行的sql语句',
            'required'    => true,
        ],
    ];

    /**
     * @param Database $database
     * @param $userId
     */
    public function __construct(protected $database, protected $userId)
    {
    }

    public function getDescription()
    {
        $fields = $this->database->fields->reduce(function ($prev, DatabaseField $field) {
            $type = match ($field->type) {
                'string' => 'varchar(255)',
                'integer' => 'int(11)',
                'float' => 'float',
                'boolean' => 'tinyint(1)',
                'datetime' => 'datetime',
            };
            return $prev . sprintf("  %s %s %s comment '%s',\n", $field->name, $type, $field->required ? 'not null' : '', $field->label);
        }, '');

        $ddl = sprintf(<<<EOT
create table %s (
    id int(11) unsigned auto_increment primary key,
    create_time timestamp default CURRENT_TIMESTAMP not null,
    %s
) comment '%s';
EOT, $this->database->getTableName(), $fields, $this->database->description);

        return sprintf("当前数据库的DDL：\n%s", $ddl);
    }

    protected function run(Args $args)
    {
        return $this->database->runWithConnection(function (Connection $connection) use ($args) {
            $sql = $args->get('sql');

            $parser    = new Parser($sql);
            $statement = $parser->statements[0];

            switch (true) {
                case $statement instanceof InsertStatement:
                    if ($this->database->mode == Database::MODE_READONLY) {
                        throw new \Exception('当前数据库为只读模式，不允许插入数据');
                    }
                    $statement->into->columns[] = 'user_id';
                    $statement->values          = array_map(function (ArrayObj $value) {
                        $value->raw[]    = "'{$this->userId}'";
                        $value->values[] = $this->userId;
                        return $value;
                    }, $statement->values);
                    break;
                case $statement instanceof SelectStatement:
                    if ($this->database->mode == Database::MODE_READONLY || $this->database->mode == Database::MODE_PUBLIC) {
                        break;
                    }
                case $statement instanceof UpdateStatement:
                case $statement instanceof DeleteStatement:
                    if ($this->database->mode == Database::MODE_READONLY) {
                        throw new \Exception('当前数据库为只读模式，不允许修改数据');
                    }
                    if (empty($statement->where)) {
                        $statement->where = [];
                    } else {
                        $start             = new Condition();
                        $start->expr       = '(';
                        $start->isOperator = true;

                        $end             = new Condition();
                        $end->expr       = ')';
                        $end->isOperator = true;

                        $statement->where = [
                            $start,
                            ...$statement->where,
                            $end,
                        ];

                        $expr               = new Condition();
                        $expr->expr         = 'AND';
                        $expr->isOperator   = true;
                        $statement->where[] = $expr;
                    }

                    $expr               = new Condition();
                    $expr->expr         = "user_id = '{$this->userId}'";
                    $expr->identifiers  = ['user_id'];
                    $expr->leftOperand  = 'user_id';
                    $expr->operator     = '=';
                    $expr->rightOperand = "'{$this->userId}'";

                    $statement->where[] = $expr;
                    break;
                default:
                    throw new \Exception('不支持的SQL语句');
            }

            $tables = Query::getTables($statement);

            if (count($tables) > 1) {
                throw new \Exception('不支持跨表操作');
            }

            if (trim($tables[0], '`') !== $this->database->getTableName()) {
                throw new \Exception('不支持的表');
            }

            $sql = $statement->build();

            if ($statement instanceof SelectStatement) {
                return $connection->query($sql);
            } else {
                $connection->execute($sql);
                if ($statement instanceof InsertStatement) {
                    $pdo = $connection->getPdo();
                    if ($pdo) {
                        return ['id' => $pdo->lastInsertId()];
                    }
                }
            }
        });
    }
}
