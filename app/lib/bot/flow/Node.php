<?php

namespace app\lib\bot\flow;

use app\Exception;
use think\helper\Arr;
use think\helper\Str;

/**
 * @method run();
 */
abstract class Node
{
    protected $data   = [];
    protected $edges  = [];
    protected $status = 'pending';

    public function __construct(protected Context $context, public string $id, array $data)
    {
        $this->data = replace_deep($data, $this->data);
    }

    public function addEdge(Edge $edge)
    {
        $this->edges[] = $edge;
    }

    public function getId()
    {
        return $this->id;
    }

    public function getTitle()
    {
        return $this->getData('title');
    }

    public function getType()
    {
        return Str::snake(class_basename($this), '-');
    }

    public function done()
    {
        $this->status = 'done';
    }

    public function fail()
    {
        $this->status = 'fail';
    }

    public function isPending()
    {
        return $this->status == 'pending';
    }

    public function skip()
    {
        $this->status = 'skip';

        $nodes = $this->getTargetNodes();

        foreach ($nodes as $node) {
            if (!Arr::first($node->getSourceNodes(), function (Node $node) {
                return !$node->isSkip();
            })) {
                $node->skip();
            }
        }
    }

    public function isSkip()
    {
        return $this->status == 'skip';
    }

    public function isComplete()
    {
        return $this->status != 'pending';
    }

    public function isFail()
    {
        return $this->status == 'fail';
    }

    public function getAncestorNodes()
    {
        $nodes = $this->getSourceNodes();

        return Arr::flatMap(function (Node $node) {
            return array_merge([$node], $node->getAncestorNodes());
        }, $nodes);
    }

    public function getSourceNodes()
    {
        $edges = array_filter($this->edges, function (Edge $edge) {
            return $edge->hasTarget($this);
        });

        return array_map(function (Edge $edge) {
            return $edge->getSource();
        }, $edges);
    }

    public function getTargetNodes($handle = null)
    {
        $edges = array_filter($this->edges, function (Edge $edge) use ($handle) {
            return $edge->hasSource($this) && (is_null($handle) || $edge->isHandle($handle));
        });

        return array_unique(array_map(function (Edge $edge) {
            return $edge->getTarget();
        }, $edges), SORT_REGULAR);
    }

    public function getNextNodes()
    {
        if ($this->isFail()) {
            return [];
        }
        $nodes = $this->getTargetNodes();

        return array_filter($nodes, function (Node $node) {
            if (!$node->isPending()) {
                return false;
            }

            $nodes = $node->getSourceNodes();

            foreach ($nodes as $node) {
                if (!$node->isComplete() || $node->isFail()) {
                    return false;
                }
            }

            return true;
        });
    }

    protected function useHandle(int $handle)
    {
        $targets = $this->getTargetNodes($handle);

        foreach ($this->getTargetNodes() as $target) {
            if (!in_array($target, $targets)) {
                $target->skip();
            }
        }
    }

    public function getInput($name = null)
    {
        if (is_null($name)) {
            $input = $this->getData('input');
            if (empty($input)) {
                return null;
            }
            return array_map(function ($value) {
                return $this->context->getOutput($value);
            }, $input);
        }

        $value = $this->getData("input.{$name}");

        return $this->context->getOutput($value);
    }

    protected function setOutput($output)
    {
        $this->context->setOutput($this->id, $output);
    }

    protected function getData($name, $default = null)
    {
        $value = Arr::get($this->data, $name, $default);

        if (is_null($value)) {
            throw new Exception("unknown data: {$name}");
        }

        return $value;
    }

    protected function getReplacedData($name)
    {
        $value = $this->getData($name);

        return $this->replaceVars($value, [], true);
    }

    protected function getSplitData($name)
    {
        $value  = $this->getData($name);
        $values = preg_split('/\{{2}([^{\s\/]+?)}{2}/i', $value, -1, PREG_SPLIT_DELIM_CAPTURE | PREG_SPLIT_NO_EMPTY);
        foreach ($values as $item) {
            yield $this->context->getOutput($item);
        }
    }

    protected function getRefData($name)
    {
        $value = $this->getData($name);

        if (empty($value)) {
            return null;
        }

        return $this->context->getOutput($value);
    }

    protected function replaceVars($prompt, $vars = [], $withOutput = false)
    {
        if ($withOutput) {
            $ids  = array_map(fn(Node $node) => $node->id, $this->getAncestorNodes());
            $vars = array_merge($this->context->getOutputs($ids), $vars);
        }
        if (preg_match_all('/\{{2}([^{\s\/]+?)}{2}/i', $prompt, $matches)) {
            foreach ($matches[1] as $key) {
                if (isset($vars[$key])) {
                    $prompt = str_replace("{{{$key}}}", $vars[$key], $prompt);
                }
            }
        }
        return $prompt;
    }

}
