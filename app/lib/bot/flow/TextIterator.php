<?php

namespace app\lib\bot\flow;

use IteratorIterator;
use JsonSerializable;

class TextIterator extends IteratorIterator implements JsonSerializable
{
    protected $text   = '';
    protected $data   = '';
    protected $rewind = false;

    public function isRewind()
    {
        return $this->rewind;
    }

    public function rewind(): void
    {
        parent::rewind();
        $this->rewind = true;
        $this->data   = parent::current();
        $this->text   = $this->data;
    }

    public function next(): void
    {
        parent::next();
        $this->data = parent::current();
        $this->text .= $this->data;
    }

    public function current(): mixed
    {
        return $this->data;
    }

    public function __toString(): string
    {
        if (!$this->rewind) {
            $this->rewind();
            while ($this->valid()) {
                $this->next();
            }
        }

        return $this->text;
    }

    public function jsonSerialize(): mixed
    {
        return $this->__toString();
    }
}
