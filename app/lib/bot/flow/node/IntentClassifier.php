<?php

namespace app\lib\bot\flow\node;

use app\lib\bot\flow\Node;
use think\ai\Client;
use think\helper\Arr;

class IntentClassifier extends Node
{
    const PROMPT = <<<EOT
请帮我执行一个“问题分类”任务，将问题分类为以下几种类型之一：

"""
{{classes}}
"""

## 对话记录
{{history}}

## 开始任务

现在，我们开始分类，我会给你一个"问题"，请结合背景知识和对话记录，将问题分类到对应的类型中，并返回类型ID。
注意，仅需要返回类型ID这个数字，不要输出其他内容

问题："{{query}}"
类型ID=

EOT;

    protected $data = [
        'input'  => [
            'query' => '<EMAIL>',
        ],
        'vision' => [
            'files' => '<EMAIL>',
        ],
    ];

    public function run(Client $client)
    {
        $classes = $this->getData('classes', []);

        $classesStr = implode("\n------\n", array_map(
            fn($key, $val) => json_encode(["类型ID" => $key, "问题类型" => $val]),
            array_keys($classes),
            $classes
        ));

        if ($this->getData('history.enable', false)) {
            $historyMessages = $this->context->getHistoryMessages(
                $this->getData('history.round', 5),
                $this->getData('model.context_tokens', 0)
            );
            $history         = implode("\n", array_map(function ($message) {
                $role = match ($message['role']) {
                    'user' => 'Human',
                    'assistant' => 'Assistant',
                };
                return "{$role}: {$message['content']}";
            }, $historyMessages));
        } else {
            $history = '';
        }

        $query   = $this->getInput('query');
        $content = $this->replaceVars(self::PROMPT, [
            'classes' => $classesStr,
            'history' => $history,
            'query'   => $query,
        ]);

        if ($this->getData('model.vision', false) && $this->getData('vision.enable', false)) {
            $files = $this->getRefData('vision.files');
            if (!empty($files)) {
                $content = [
                    [
                        'type' => 'text',
                        'text' => $content,
                    ],
                    ...$this->context->getImagesContent($files),
                ];
            }
        }

        $messages = [
            [
                'role'    => 'user',
                'content' => $content,
            ],
        ];

        $res = $client->chat()->completions([
            'model'       => $this->getData('model.name'),
            'temperature' => $this->getData('model.params.temperature', 0.8),
            'messages'    => $messages,
            'stream'      => false,
        ]);

        $index = (int) Arr::get($res, 'message.content', -1);

        $this->useHandle($index);

        $this->context->consumeTokens(Arr::get($res, 'usage.total_tokens'));

        $this->setOutput(['class' => $classes[$index] ?? null]);
    }
}
