<?php

namespace app\lib\bot\flow\node;

use app\Exception;
use app\lib\bot\flow\Context;
use app\lib\bot\flow\Node;
use think\ai\Client;

class Agent extends Node
{
    protected $data = [
        'input' => [
            'query' => '<EMAIL>',
            'files' => '<EMAIL>',
        ],
    ];

    public function run(Client $client)
    {
        $id = $this->getData('agent');
        if (empty($id)) {
            throw new Exception('请选择一个智能体');
        }

        /** @var \app\model\Bot $bot */
        $bot = $this->context->bot->space->bots()->where('type', 'agent')->find($id);
        if (empty($bot)) {
            throw new Exception('智能体不存在');
        }

        $agent = new class($bot) extends \app\lib\bot\Agent {

            public Context $context;

            protected function getHistoryMessages($round, $contextTokens)
            {
                return $this->context->getHistoryMessages($round, $contextTokens);
            }

            protected function initConversation($conversation, $userId)
            {
                $this->conversation = $conversation;
            }

            protected function sendConversation()
            {
                return [];
            }

            protected function checkAnnotation()
            {
                return [];
            }

            protected function saveMessage($usage, $latency)
            {
            }

            protected function consumeTokens(int $usage): int
            {
                $this->context->consumeTokens($usage);
                return $usage;
            }
        };

        $agent->context = $this->context;

        $variables = array_map(function ($value) {
            return $this->context->getOutput($value);
        }, $this->getData('variables', []));

        $result = $agent->run([
            'query'        => $this->getInput('query'),
            'files'        => $this->getInput('files'),
            'variables'    => $variables,
            'conversation' => $this->context->conversation,
        ]);

        $result->rewind();
        $index = 0;

        while ($result->valid()) {
            $item = $result->current();

            if (!empty($chunks = $item['chunks'] ?? null)) {
                if (isset($chunks['content'])) {
                    yield [
                        'index' => $index,
                        'value' => $chunks['content'],
                    ];
                    if (empty($chunks['content'])) {
                        $index++;
                    }
                }
                if (!empty($chunks['error'])) {
                    yield [
                        'index' => $index,
                        'value' => [
                            'error' => $chunks['error'],
                        ],
                    ];
                    $index++;
                }
                if (!empty($chunks['tools']['content'])) {
                    yield [
                        'index' => $index,
                        'value' => $chunks['tools']['content'],
                    ];
                    $index++;
                }
            }

            $result->next();
        }
    }
}
