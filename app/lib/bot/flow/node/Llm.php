<?php

namespace app\lib\bot\flow\node;

use app\lib\bot\flow\Node;
use app\lib\bot\flow\TextIterator;
use think\ai\Client;

class Llm extends Node
{
    protected $data = [
        'query'  => '{{<EMAIL>}}',
        'vision' => [
            'files' => '<EMAIL>',
        ],
    ];

    public function run(Client $client)
    {
        $messages = [];

        $prompt = $this->getReplacedData('prompt');
        if (!empty($prompt)) {
            $messages[] = [
                'role'    => 'system',
                'content' => $prompt,
            ];
        }

        if ($this->getData('history.enable', false)) {
            $historyMessages = $this->context->getHistoryMessages(
                $this->getData('history.round', 5),
                $this->getData('model.context_tokens', 0)
            );

            $messages = array_merge($messages, $historyMessages);
        }

        $content = $this->getReplacedData('query');

        if ($this->getData('model.vision', false) && $this->getData('vision.enable', false)) {
            $files = $this->getRefData('vision.files');
            if (!empty($files)) {
                $content = [
                    [
                        'type' => 'text',
                        'text' => $content,
                    ],
                    ...$this->context->getImagesContent($files),
                ];
            }
        }

        $messages[] = [
            'role'    => 'user',
            'content' => $content,
        ];

        $result = $client->chat()->completions([
            'model'       => $this->getData('model.name'),
            'temperature' => $this->getData('model.params.temperature', 0.8),
            'messages'    => $messages,
            'stream'      => true,
        ]);

        $this->setOutput([
            'content' => new TextIterator(value(function () use ($result) {
                foreach ($result as $event) {
                    $content = $event['delta']['content'] ?? '';
                    if ($content !== '') {
                        yield $content;
                    }

                    if (!empty($event['usage'])) {
                        $this->context->consumeTokens($event['usage']['total_tokens']);
                    }
                }
            })),
        ]);
    }
}
