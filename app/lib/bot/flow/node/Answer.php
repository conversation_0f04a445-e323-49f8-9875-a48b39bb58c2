<?php

namespace app\lib\bot\flow\node;

use app\lib\bot\flow\Node;
use app\lib\bot\flow\TextIterator;

class Answer extends Node
{
    public function run()
    {
        $chunks = $this->getSplitData('content');

        foreach ($chunks as $chunk) {
            //输出
            if ($chunk instanceof TextIterator) {
                foreach ($chunk as $item) {
                    yield $item;
                }
            } else {
                yield $chunk;
            }
        }
    }
}
