<?php

namespace app\lib\bot\flow\node;

use app\lib\bot\flow\Node;
use app\lib\Hashids;
use app\model\Dataset;
use app\model\Setting;
use think\ai\Client;

class KnowledgeRetrieval extends Node
{
    protected $data = [
        'input' => [
            'query' => '<EMAIL>',
        ],
    ];

    public function run(Client $client)
    {
        $query = $this->getInput('query');

        $datasets = $this->context->bot->space->datasets()
            ->whereIn('id', array_map(function ($id) {
                return Hashids::decode($id);
            }, $this->getData('datasets')))
            ->select();

        $chunks = array_merge(...$datasets->map(function (Dataset $dataset) use ($query) {
            return $dataset->search($query);
        })->toArray());

        $model = Setting::read('model.rerank');
        if (!empty($chunks) && !empty($model)) {
            //重排
            $chunks = $client->rerank()->create([
                'model'     => $model,
                'query'     => $query,
                'documents' => array_map(function ($item) {
                    $content = $item['payload']['content'];
                    if (!empty($item['payload']['title'])) {
                        $content = "{$item['payload']['title']}{$content}";
                    }
                    return $content;
                }, $chunks),
                'score'     => 0.1,
            ])['documents'];
        }

        $this->setOutput([
            'result' => array_reduce(array_slice($chunks, 0, 3), function ($carry, $item) {
                return "{$carry}{$item}\n";
            }, ''),
        ]);
    }
}
