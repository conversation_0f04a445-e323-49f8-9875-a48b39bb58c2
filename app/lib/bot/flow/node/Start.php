<?php

namespace app\lib\bot\flow\node;

use app\lib\bot\flow\Node;
use app\lib\Date;

class Start extends Node
{

    public function run()
    {
        $this->setOutput([
            ...$this->context->message->variables,
            'sys.query'  => $this->context->message->query,
            'sys.files'  => $this->context->message->files ?? [],
            'sys.time'   => (string) Date::now(),
            'sys.ip'     => request()->ip(),
            'sys.source' => $this->context->conversation->source_label,
        ]);
    }
}
