<?php

namespace app\lib\bot\flow;

class Edge
{
    public function __construct(protected Node $source, protected Node $target, protected int $handle = 0)
    {
    }

    public function getSource()
    {
        return $this->source;
    }

    public function getTarget()
    {
        return $this->target;
    }

    public function isHandle(int $handle)
    {
        return $this->handle == $handle;
    }

    public function hasSource(Node $node)
    {
        return $this->source === $node;
    }

    public function hasTarget(Node $node)
    {
        return $this->target === $node;
    }
}
