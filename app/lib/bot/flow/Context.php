<?php

namespace app\lib\bot\flow;

use app\model\Bot;
use app\model\Conversation;
use app\model\Message;
use app\model\Space;
use think\agent\Util;
use think\facade\Filesystem;
use think\helper\Arr;

class Context
{
    protected $output = [];
    protected $usage  = 0;

    public function __construct(
        public Space        $space,
        public Bot          $bot,
        public Conversation $conversation,
        public Message      $message
    )
    {
    }

    public function consumeTokens(int $usage)
    {
        $this->usage += $usage;
    }

    public function setOutput($id, $output)
    {
        $this->output[$id] = $output;
    }

    public function getOutput($name = null)
    {
        if (is_null($name)) {
            return $this->output;
        }

        $default = $name;
        $outputs = $this->getOutputs();
        $output  = $outputs[$name] ?? $default;

        if ($output instanceof TextIterator && $output->isRewind()) {
            [$id, $name] = explode('@', $name, 2);
            return $this->output[$id][$name] = (string) $output;
        }

        return $output;
    }

    public function getOutputs($ids = null)
    {
        $outputs = [];

        foreach ($this->output as $id => $output) {
            if ($ids !== null && !in_array($id, $ids)) {
                continue;
            }
            foreach ($output as $key => $value) {
                $outputs["{$id}@{$key}"] = $value;
            }
        }
        return $outputs;
    }

    public function getUsage()
    {
        return $this->usage;
    }

    public function getHistoryMessages($round, $contextTokens)
    {
        //获取历史记录
        /** @var \app\model\Message[] $messages */
        $messages = $this->conversation->messages()
            ->limit($round)
            ->order('create_time desc')
            ->select();

        $historyMessages = [];

        foreach ($messages as $message) {
            $chunkMessages = [
                [
                    'role'    => 'user',
                    'content' => $message->content,
                ],
            ];

            $content = "";

            foreach ($message->chunks as $chunk) {
                if (!empty($chunk['error'])) {
                    break 2;
                }
                if (!empty($chunk['content'])) {
                    if (is_array($chunk['content'])) {
                        foreach ($chunk['content'] as $item) {
                            if (is_string($item['value'] ?? null)) {
                                $content .= $item['value'];
                            }
                        }
                    } else {
                        $content .= $chunk['content'];
                    }
                }
            }

            $chunkMessages[] = [
                'role'    => 'assistant',
                'content' => $content,
            ];

            $tempHistoryMessages = array_merge($chunkMessages, $historyMessages);
            if ($contextTokens > 0) {
                $tokens = Util::tikToken($tempHistoryMessages);
                if ($tokens > $contextTokens * .6) {
                    break;
                }
            }
            $historyMessages = $tempHistoryMessages;
        }

        return $historyMessages;
    }

    public function getImagesContent($files)
    {
        $disk = Filesystem::disk('uploads');
        return Arr::flatMap(function ($file) use ($disk) {
            if (!empty($file['url'])) {
                return [
                    [
                        'type'      => 'image_url',
                        'image_url' => [
                            'url' => $file['url'],
                        ],
                    ],
                ];
            }
            if (empty($file['name']) || empty($file['path']) || empty($file['size'])) {
                return [];
            }
            $ext = pathinfo($file['name'], PATHINFO_EXTENSION);
            if (in_array($ext, ['jpg', 'jpeg', 'png', 'gif'])) {
                $content = $disk->read($file['path']);
                //转为base64
                $url = 'data:image/' . $ext . ';base64,' . base64_encode($content);

                return [
                    [
                        'type'      => 'image_url',
                        'image_url' => [
                            'url' => $url,
                        ],
                    ],
                ];
            }

            return [];
        }, $files);
    }

}
