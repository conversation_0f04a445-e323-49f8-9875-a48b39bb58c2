<?php

namespace app\lib\bot\flow;

use app\lib\bot\flow\node\Start;
use think\helper\Arr;
use think\helper\Str;

class Graph
{
    /** @var \app\lib\bot\flow\Node[] */
    protected $nodes = [];
    /** @var \app\lib\bot\flow\Edge[] */
    protected $edges = [];

    public function __construct(Context $context, $nodes = [], $edges = [])
    {
        foreach ($nodes as $node) {
            $node = $this->createNode($context, $node);
            if ($node) {
                $this->nodes[$node->id] = $node;
            }
        }

        foreach ($edges as $edge) {
            $source = $this->nodes[$edge['source']] ?? null;
            $target = $this->nodes[$edge['target']] ?? null;

            if (!$source || !$target) {
                continue;
            }

            $edge = new Edge($source, $target, $edge['sourceHandle'] ?? 0);

            $source->addEdge($edge);
            $target->addEdge($edge);

            $this->edges[] = $edge;
        }
    }

    public function getStartNode()
    {
        return Arr::first($this->nodes, function ($node) {
            return $node instanceof Start;
        });
    }

    protected function createNode($context, $node): ?Node
    {
        $class = "\\app\\lib\\bot\\flow\\node\\" . Str::studly($node['type']);
        if (class_exists($class)) {
            return new $class($context, $node['id'], $node['data']);
        }
    }
}
