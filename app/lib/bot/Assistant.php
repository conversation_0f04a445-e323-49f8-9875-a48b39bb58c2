<?php

namespace app\lib\bot;

use app\lib\bot\agent\BaseAgent;
use app\lib\bot\agent\plugin\antv\Antv;
use app\lib\bot\agent\plugin\knowledge\Knowledge;
use app\lib\bot\assistant\Database;
use app\lib\Date;
use app\model\Conversation;
use app\model\Space;
use think\helper\Arr;

class Assistant extends BaseAgent
{
    protected $source = null;

    protected $config = [
        'model'  => [
            'name' => 'gpt-4o-mini',
        ],
        'prompt' => <<<EOT
# 角色
你是小智，一个关于ThinkBot智能小助手，可以解决用户在使用ThinkBot过程中遇到的问题以及指导

## 技能
- 你可以使用知识库中的知识，调用知识库搜索相关知识，并向用户提供简洁和专业的答案。
- 可以查询数据库
- 用户有统计分析等需求时可以使用相应的数据可视化工具来展示数据
- 当前时间是{{time}}

## 限制
- 你的回答必须简洁明了，不能过于复杂，但可以包含图片内容。
- 你的回答必须专业，不能使用口语化的表达方式。
- 知识库如果有相关文档，请在回复内容最后显示相关文档
- 数据库只可以查询，不可以写入
- 查询数据库时不可以使用`*`查询所有字段，必须明确指定所需的字段
- 从数据库获取智能体信息展示给用户时不要仅显示ID，需要显示智能体的名称
- 从数据库获取列表数据时每次最多返回50条数据
- 仅回答关于ThinkBot的问题，并且不要胡编乱造
- 对于知识库中没有的问题，请回复：这个小智暂时也不清楚呢，建议添加我们的专属客服微信 topthinkcloud，有什么其它可以帮你的么？

## 流程
判断用户意图：
1. 如果用户是咨询使用问题则去知识库中检索答案回复用户
2. 如果用户是询问智能体以及消息的数据相关问题去数据库中查询后回复用户
3. 如果用户有分析统计需求则需要使用图表展示工具，以图表的方式展示数据给用户
EOT,
    ];

    public function __construct(Space $space)
    {
        $this->space = $space;
    }

    protected function init($params)
    {
        $this->checkConfig();

        $query = Arr::get($params, 'query', '');

        $conversationId = Arr::get($params, 'conversation');
        $userId         = Arr::get($params, 'user');

        $this->initConversation($conversationId, $userId);
        $this->initMessage($query);
        //添加相关工具
        $this->initTools();
    }

    protected function initTools()
    {
        //知识库
        $plugin = new Knowledge(auth: [
            'provider'     => 'system',
            'type'         => 'http',
            'scheme'       => 'bearer',
            'bearerFormat' => 'Token',
            'token'        => '4f1aa791-8ed5-47bf-afbc-59376a91ccd0',
        ]);
        $tool   = $plugin->getTool('search');
        $this->addFunction('knowledge-search', $tool, ['id' => '8lrxg88k24']);
        //数据库
        $this->addFunction('database-query', new Database($this->space));
        //图表
        $plugin = new Antv();
        $charts = ['line', 'column', 'pie', 'area', 'bar', 'histogram', 'scatter', 'dual_axes'];
        foreach ($charts as $chart) {
            $tool = $plugin->getTool($chart);
            $this->addFunction("chart-{$chart}", $tool);
        }
    }

    protected function start()
    {
        yield from $this->sendConversation();

        yield from parent::start();
    }

    protected function initConversation($conversationId, $userId)
    {
        if (!empty($conversationId)) {
            if ($conversationId instanceof Conversation) {
                $conversation = $conversationId;
            } else {
                $query = Conversation::where('space_id', $this->space->id)
                    ->where('bot_id', 0)
                    ->where('id', $conversationId);

                if (!empty($userId)) {
                    $query->where('user_id', $userId);
                }

                $conversation = $query->find();
            }
        }

        if (empty($conversation)) {
            $conversation = Conversation::create([
                'space_id' => $this->space->id,
                'bot_id'   => 0,
                'user_id'  => $userId,
            ]);

            $this->isNewConversation = true;
        } else {
            $conversation->save(['update_time' => Date::now()]);
        }

        $this->conversation = $conversation;
    }

    protected function initMessage($query, $files = null, $variables = null)
    {
        $this->message = $this->conversation->messages()->make([
            'space_id'  => $this->space->id,
            'bot_id'    => 0,
            'query'     => $query,
            'files'     => json_encode($files),
            'variables' => json_encode($variables),
        ]);
    }
}
