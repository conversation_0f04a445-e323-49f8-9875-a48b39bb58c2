<?php

namespace app\lib\bot\assistant;

use app\model\Space;
use Php<PERSON>yAdmin\SqlParser\Components\Condition;
use PhpMyAdmin\SqlParser\Components\Expression;
use PhpMyAdmin\SqlParser\Parser;
use PhpMyAdmin\SqlParser\Statements\SelectStatement;
use think\agent\tool\Args;
use think\agent\tool\FunctionCall;
use think\facade\Db;

class Database extends FunctionCall
{
    protected $title      = '数据库';
    protected $parameters = [
        'sql' => [
            'type'        => 'string',
            'description' => '需要执行的sql语句',
            'required'    => true,
        ],
    ];

    public function __construct(protected Space $space)
    {
    }

    public function getDescription()
    {
        $ddl = <<<EOT
create table bot (
    id int(11) unsigned auto_increment primary key,
    name varchar(255) not null comment '名称',
    description text comment '描述',
    create_time timestamp default CURRENT_TIMESTAMP not null
) comment '智能体';
create table conversation (
    id int(11) unsigned auto_increment primary key,
    bot_id int(11) not null comment '智能体ID',
    user_id varchar(255) not null comment '用户ID',
    title varchar(255) not null comment '标题',
    source varchar(255) not null comment '来源',
    create_time timestamp default CURRENT_TIMESTAMP not null,
    update_time timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
) comment '对话';
create table message (
    id int(11) unsigned auto_increment primary key,
    conversation_id int(11) not null comment '对话ID',
    query text not null comment '用户提问',
    chunks text not null comment '回答内容',
    create_time timestamp default CURRENT_TIMESTAMP not null,
    update_time timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
) comment '消息';
EOT;

        return sprintf("当前数据库的DDL：\n%s", $ddl);
    }

    protected function run(Args $args)
    {
        $sql = $args->get('sql');

        $parser    = new Parser($sql);
        $statement = $parser->statements[0];

        if (!$statement instanceof SelectStatement) {
            throw new \Exception('不支持的SQL语句');
        }

        $tables = $statement->from;

        if (count($tables) > 1) {
            throw new \Exception('不支持多表操作');
        }

        $table = $tables[0];

        if (empty($statement->where)) {
            $statement->where = [];
        } else {
            $start             = new Condition();
            $start->expr       = '(';
            $start->isOperator = true;

            $end             = new Condition();
            $end->expr       = ')';
            $end->isOperator = true;

            $statement->where = [
                $start,
                ...$statement->where,
                $end,
            ];

            $expr               = new Condition();
            $expr->expr         = 'AND';
            $expr->isOperator   = true;
            $statement->where[] = $expr;
        }

        $where   = [];
        $where[] = $this->createCondition($table, 'space_id', $this->space->id);

        switch ($table->table) {
            case 'bot':
                break;
            case 'conversation':
                $where[] = $this->createCondition($table, 'bot_id', 0, '>');
                break;
            case 'message':
                $where[] = $this->createCondition($table, 'bot_id', 0, '>');
                break;
            default:
                throw new \Exception('不支持的表');
        }

        $where = array_reduce($where, function ($carry, $item) {
            if (!empty($carry)) {
                $expr             = new Condition();
                $expr->expr       = 'AND';
                $expr->isOperator = true;
                $carry[]          = $expr;
            }
            $carry[] = $item;
            return $carry;
        }, []);

        $statement->where = [
            ...$statement->where,
            ...$where,
        ];

        $sql = $statement->build();

        $connection = Db::connect();

        return $connection->query($sql);
    }

    protected function createCondition(Expression $table, $name, $value, $operator = '=')
    {
        if ($table->alias) {
            $name = "{$table->alias}.{$name}";
        } else {
            $name = "{$table->table}.{$name}";
        }

        if (is_numeric($value)) {
            $value = (int) $value;
        } else {
            $value = "'{$value}'";
        }

        $expr               = new Condition();
        $expr->expr         = "{$name} {$operator} {$value}";
        $expr->identifiers  = [$name];
        $expr->leftOperand  = $name;
        $expr->operator     = $operator;
        $expr->rightOperand = $value;

        return $expr;
    }
}
