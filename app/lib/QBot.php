<?php

namespace app\lib;

use app\job\QqMessageJob;
use app\model\Bot;
use app\model\BotQq;
use GuzzleHttp\Client;
use think\Cache;
use think\Request;

class QBot
{
    protected $cacheKey;

    public function __construct(protected BotQq $qq, protected Cache $cache)
    {
        $this->cacheKey = sprintf('qq.access_token.%s', $this->qq->app_secret);
    }

    public function sendUserMessage($to, $content, $msgId = null, $seq = 1)
    {
        $this->getHttpClient()->post("/v2/users/{$to}/messages", [
            'json' => [
                'content'  => $content,
                'msg_type' => 0,
                'msg_id'   => $msgId,
                'msg_seq'  => $seq,
            ],
        ]);
    }

    public function sendGroupMessage($to, $content, $msgId, $seq = 1)
    {
        $this->getHttpClient()->post("/v2/groups/{$to}/messages", [
            'json' => [
                'content'  => $content,
                'msg_type' => 0,
                'msg_id'   => $msgId,
                'msg_seq'  => $seq,
            ],
        ]);
    }

    public function serve(Request $request)
    {
        $op = $request->post('op');
        $d  = $request->post('d');

        //验证签名
        if (!$this->verifySignature($request)) {
            return json(['message' => '签名验证失败'], 401);
        }

        switch ($op) {
            case 0://Dispatch
                $enabled = $this->qq->bot->enabled(Bot::INTEGRATION_QQ);
                if ($enabled) {
                    $t = $request->post('t');
                    switch ($t) {
                        case 'GROUP_AT_MESSAGE_CREATE'://群聊@
                        case 'C2C_MESSAGE_CREATE'://私聊
                        case 'FRIEND_ADD'://添加好友
                            queue(new QqMessageJob($this->qq, $t, $d));
                            break;
                    }
                }
                break;
            case 13://回调地址验证
                // 生成密钥对
                $keyPair    = $this->getKeyPair();
                $privateKey = sodium_crypto_sign_secretkey($keyPair);

                // 构造消息进行签名
                $msg          = $d['event_ts'] . $d['plain_token'];
                $signature    = sodium_crypto_sign_detached($msg, $privateKey);
                $signatureHex = bin2hex($signature);

                return json([
                    'plainToken' => $d['plain_token'],
                    'signature'  => $signatureHex,
                ]);
        }

        return json();
    }

    protected function getKeyPair()
    {
        $seed = $this->qq->app_secret;
        while (strlen($seed) < SODIUM_CRYPTO_SIGN_SEEDBYTES) {
            $seed .= $seed;
        }
        $seed = substr($seed, 0, SODIUM_CRYPTO_SIGN_SEEDBYTES);

        return sodium_crypto_sign_seed_keypair($seed);
    }

    protected function verifySignature(Request $request)
    {
        // 生成公钥和私钥
        $keyPair   = $this->getKeyPair();
        $publicKey = sodium_crypto_sign_publickey($keyPair);

        $signature = $request->header('x-signature-ed25519');

        if (empty($signature)) {
            return false;
        }

        $sig = hex2bin($signature);
        if ($sig === false || strlen($sig) !== 64 || (ord($sig[63]) & 224) !== 0) {
            return false;
        }

        $timestamp = $request->header('x-signature-timestamp');
        if (empty($timestamp)) {
            return false;
        }

        $body = $request->getContent();

        $msg = $timestamp . $body;

        return sodium_crypto_sign_verify_detached($sig, $msg, $publicKey);
    }

    protected function getAccessToken()
    {
        $token = $this->cache->get($this->cacheKey);

        if ((bool) $token && is_string($token)) {
            return $token;
        }

        $client = new Client();

        $response = $client->post('https://bots.qq.com/app/getAppAccessToken', [
            'json' => [
                'appId'        => $this->qq->app_id,
                'clientSecret' => $this->qq->app_secret,
            ],
        ]);

        $result = json_decode($response->getBody()->getContents(), true);

        if (empty($result['access_token'])) {
            throw new \Exception('Failed to get access_token: ' . json_encode($result, JSON_UNESCAPED_UNICODE));
        }

        $this->cache->set($this->cacheKey, $result['access_token'], intval($result['expires_in']));

        return $result['access_token'];
    }

    protected function getHttpClient()
    {
        $accessToken = $this->getAccessToken();

        return new Client([
            'base_uri' => 'https://api.sgroup.qq.com',
            'headers'  => [
                'Authorization' => "QQBot {$accessToken}",
            ],
        ]);
    }
}
