<?php

namespace app\lib;

use app\model\User;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use yunwuxin\auth\credentials\BaseCredentials;
use yunwuxin\auth\credentials\TokenCredentials;
use yunwuxin\auth\interfaces\Provider;

class UserProvider implements Provider
{

    /**
     * @param BaseCredentials $credentials
     * @return mixed
     */
    public function retrieveByCredentials($credentials)
    {
        if ($credentials instanceof TokenCredentials) {
            $token = $credentials->getToken();
            if ($token) {
                try {
                    $decoded = (array) JWT::decode($token, new Key(config('app.token'), 'HS256'));
                    $id      = $decoded['id'];

                    return User::findOrFail($id);
                } catch (\Throwable) {

                }
            }
        }
    }
}
