<?php

namespace app\lib;

use think\exception\HttpException;
use Throwable;

class Hashids
{
    public static function hashids($salt = null, $minHashLength = 6)
    {
        return new \Hashids\Hashids($salt ?: 'bot.topthink.com', $minHashLength, 'abcdefghijklmnopqrstuvwxyz123456789');
    }

    public static function encode($id, $salt = null)
    {
        return self::hashids($salt)->encode($id);
    }

    public static function decode($hash, $force = true, $salt = null)
    {
        try {
            [$id] = self::hashids($salt)->decode($hash);
        } catch (Throwable) {
            $id = null;
        }

        if (empty($id) && $force) {
            throw new HttpException(404);
        }

        return $id;
    }

}
