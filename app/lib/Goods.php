<?php

namespace app\lib;

use app\model\Order;
use app\model\Space;
use app\model\User;
use yunwuxin\model\SerializesModel;

abstract class Goods
{
    use SerializesModel;

    protected ?Space $space;

    protected $amount;

    /** @var bool 是否可以撤销 */
    protected $canRevoke = false;

    public function canRevoke(Order $order)
    {
        return $this->canRevoke;
    }

    /**
     * @return mixed
     */
    public function getAmount()
    {
        return $this->amount;
    }

    public function setAmount($amount)
    {
        $this->amount = (int) $amount;
        return $this;
    }

    public function revoke(Order $order)
    {

    }

    abstract public function invoke(Order $order);

    abstract public function getSubject();

    /**
     * @return Order
     */
    public function purchase(User $user)
    {
        return Order::create([
            'space_id'   => $this->space?->id ?? 0,
            'user_id'    => $user->id,
            'subject'    => $this->getSubject(),
            'amount'     => $this->getAmount(),
            'goods_type' => get_class($this),
            'goods'      => $this,
        ])->pay();
    }
}
