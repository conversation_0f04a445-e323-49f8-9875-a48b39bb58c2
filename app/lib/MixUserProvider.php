<?php

namespace app\lib;

use app\model\AccessToken;
use app\model\User;
use Exception;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Ramsey\Uuid\Uuid;
use yunwuxin\auth\credentials\BaseCredentials;
use yunwuxin\auth\credentials\RequestCredentials;
use yunwuxin\auth\interfaces\Provider;

class MixUserProvider implements Provider
{
    /**
     * @param BaseCredentials $credentials
     * @return mixed
     */
    public function retrieveByCredentials($credentials)
    {
        if ($credentials instanceof RequestCredentials) {
            $request = $credentials->getRequest();
            $token   = $credentials->getToken();

            if (Uuid::isValid($token)) {
                $accessToken = AccessToken::getByToken($token);

                if ($accessToken && $accessToken->space->plan_level >= 20) {
                    $request->setSpace($accessToken->space);
                    return $accessToken->space->owner;
                }
            } else {
                try {
                    $decoded = (array) JWT::decode($token, new Key(config('app.token'), 'HS256'));
                    $id      = $decoded['id'];

                    return User::findOrFail($id);
                } catch (Exception) {

                }
            }
        }
    }
}
