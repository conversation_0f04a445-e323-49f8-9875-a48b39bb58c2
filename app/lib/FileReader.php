<?php

namespace app\lib;

use Exception;
use League\Flysystem\UnableToReadFile;
use PhpOffice\PhpPresentation\Reader\PowerPoint2007;
use PhpOffice\PhpPresentation\Shape\RichText;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpWord\Element\AbstractContainer;
use PhpOffice\PhpWord\Element\AbstractElement;
use PhpOffice\PhpWord\Element\Text;
use PhpOffice\PhpWord\Reader\Word2007;
use RuntimeException;
use Smalot\PdfParser\Config;
use Smalot\PdfParser\Parser;
use Symfony\Component\Process\Process;

class FileReader
{
    protected $ext;

    public function __construct(protected $filename)
    {
        $this->ext = pathinfo($this->filename, PATHINFO_EXTENSION);
    }

    public function getText()
    {
        $text = match ($this->ext) {
            'md', 'txt' => $this->getPlainText(),
            'png', 'jpg' => $this->getImageText(),
            'pdf' => $this->getPdfText(),
            'docx' => $this->getDocxText(),
            'pptx' => $this->getPptxText(),
            'xlsx' => $this->getXlsxText(),
            default => throw new RuntimeException('不支持的文件类型'),
        };

        //过滤非utf8字符
        return mb_convert_encoding($text, 'UTF-8', 'UTF-8');
    }

    protected function getXlsxText()
    {
        $reader      = new Xlsx();
        $spreadsheet = $reader->load($this->filename);

        $text = "";
        foreach ($spreadsheet->getAllSheets() as $sheet) {
            $sheetText = join("\n", array_map(function ($row) {
                return join("\t", $row);
            }, $sheet->toArray()));

            if (!empty($sheetText)) {
                $text .= "# {$sheet->getTitle()}\n\n{$sheetText}\n";
            }
        }

        return $text;
    }

    protected function getImageText()
    {
        $process = Process::fromShellCommandline("tesseract {$this->filename} - --psm 6 -l chi_sim+eng");

        $process->run();

        if (!$process->isSuccessful()) {
            throw new Exception('图片读取失败');
        }

        $text = $process->getOutput();

        $text = preg_replace('/[^\x{4e00}-\x{9fa5}a-zA-Z0-9\s]/u', '', $text);

        // 去除空行
        $text = preg_replace("/(^[\r\n]*|[\r\n]+)[\s\t]*[\r\n]+/", "\n", $text);

        return $text;
    }

    protected function getPptxText()
    {
        $reader       = new PowerPoint2007();
        $presentation = $reader->load($this->filename);
        $text         = '';

        foreach ($presentation->getAllSlides() as $slide) {
            foreach ($slide->getShapeCollection() as $shape) {
                if ($shape instanceof RichText) {
                    foreach ($shape->getParagraphs() as $paragraph) {
                        $text .= $paragraph->getPlainText() . "\n";
                    }
                }
            }
        }

        return $text;
    }

    protected function getDocxText()
    {
        $getWordText = function (AbstractElement $element) use (&$getWordText) {
            $result = '';
            if ($element instanceof AbstractContainer) {
                foreach ($element->getElements() as $element) {
                    $result .= $getWordText($element);
                }
                $result .= "\n";
            } elseif ($element instanceof Text) {
                $result .= $element->getText();
            }

            return $result;
        };

        $reader = new Word2007();
        $word   = $reader->load($this->filename);
        $text   = '';

        foreach ($word->getSections() as $section) {
            foreach ($section->getElements() as $element) {
                $text .= $getWordText($element);
            }
        }

        return $text;
    }

    protected function getPdfText()
    {
        $config = new Config();
        $config->setRetainImageContent(false);
        $parser = new Parser([], $config);
        $pdf    = $parser->parseFile($this->filename);
        return $pdf->getText();
    }

    protected function getPlainText()
    {
        $contents = @file_get_contents($this->filename);

        if ($contents === false) {
            throw UnableToReadFile::fromLocation($this->filename, error_get_last()['message'] ?? '');
        }

        return $contents;
    }
}
