<?php
declare (strict_types = 1);

namespace app;

use Guz<PERSON>Http\HandlerStack;
use GuzzleHttp\Utils;
use think\ai\Client;
use think\App;
use think\Config;
use think\Service;

/**
 * 应用服务类
 */
class AppService extends Service
{
    public function register()
    {
        // 服务注册
        $this->app->resolving(function ($instance, App $container) {
            if ($instance instanceof BaseController) {
                $container->invoke([$instance, 'initialize'], [], true);
            }
        });

        //AI Client
        $this->app->bind(Client::class, function (App $app, Config $config) {
            $enable = $config->get('cloud.enable');

            if ($enable) {
                $token = $app->make(\TopThinkCloud\Client::class)->getClientToken();
            } else {
                $token = env('AI_TOKEN', env('LICENSE', ''));
            }

            $handler = new HandlerStack(Utils::chooseHandler());

            $client = new Client($token, $handler);
            $client->setEndpoint(env('AI_HOST', 'https://ai.topthink.com'));

            return $client;
        });
    }

    public function boot()
    {
        // 服务启动
    }
}
