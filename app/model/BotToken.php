<?php

namespace app\model;

use app\lib\Date;
use app\lib\Hashids;
use think\Model;

/**
 * Class app\model\BotToken
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $expire_time
 * @property \app\lib\Date $last_time
 * @property \app\lib\Date $update_time
 * @property int $bot_id
 * @property int $id
 * @property int $type
 * @property string $name
 * @property-read \app\model\Bot $bot
 * @property-read mixed $token
 */
class BotToken extends Model
{
    const TYPE_API   = 1;
    const TYPE_SHARE = 2;
    const TYPE_CHAT  = 3;

    protected $append = ['token'];

    protected $type = [
        'expire_time' => Date::class,
        'last_time'   => Date::class,
    ];

    public function bot()
    {
        return $this->belongsTo(Bot::class);
    }

    protected function getTokenAttr()
    {
        return Hashids::hashids('bot_token', $this->getAttr('type') == self::TYPE_SHARE ? 8 : 16)->encode($this->id);
    }

    /**
     * @param $token
     * @param $share
     * @return self
     */
    public static function getByToken($token, $share = false)
    {
        try {
            [$id] = Hashids::hashids('bot_token', $share ? 8 : 16)->decode($token);
            $query = self::where('id', $id);
            if ($share) {
                $query->where('type', self::TYPE_SHARE);
            }

            return $query->find();
        } catch (\Throwable) {
            return null;
        }
    }

    public function isAvailable()
    {
        return !$this->expire_time || $this->expire_time->gt(Date::now());
    }
}
