<?php

namespace app\model;

use app\lib\wechat\mp\Provider;
use think\Cache;
use think\Model;

/**
 * Class app\model\BotWmp
 *
 * @property \app\lib\Date $create_time
 * @property int $bot_id
 * @property int $id
 * @property string $aes_key
 * @property string $app_id
 * @property string $app_secret
 * @property string $token
 * @property-read \app\model\Bot $bot
 */
class BotWmp extends Model
{
    public function bot()
    {
        return $this->belongsTo(Bot::class);
    }

    /**
     * @return Provider
     */
    public function getProvider()
    {
        return $this->invoke(function (Cache $cache) {
            $provider = new Provider([
                'aes_key'    => $this->aes_key,
                'token'      => $this->token,
                'app_id'     => $this->app_id,
                'app_secret' => $this->app_secret,
            ]);

            $provider->setCache($cache);

            return $provider;
        });
    }
}
