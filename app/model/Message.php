<?php

namespace app\model;

use think\Filesystem;
use think\Model;

/**
 * Class app\model\Message
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property array $chunks
 * @property array $files
 * @property array $variables
 * @property int $bot_id
 * @property int $conversation_id
 * @property int $id
 * @property int $latency
 * @property int $space_id
 * @property int $usage
 * @property string $content
 * @property string $query
 * @property-read \app\model\Bot $bot
 * @property-read \app\model\BotAnnotation $annotation
 */
class Message extends Model
{
    protected $type = [
        'chunks'    => 'json',
        'files'     => 'json',
        'variables' => 'json',
    ];

    public function bot()
    {
        return $this->belongsTo(Bot::class);
    }

    public function annotation()
    {
        return $this->hasOne(BotAnnotation::class);
    }

    protected function getContentAttr()
    {
        return $this->invoke(function (Filesystem $filesystem) {
            $content = $this->query;

            if (!empty($this->files)) {
                $disk = $filesystem->disk('uploads');

                $content .= "\n";
                $content .= json_encode(['files' => array_map(function ($file) use ($disk) {
                    return [
                        'name' => $file['name'],
                        'path' => $file['path'],
                        'url'  => (string) url($disk->url($file['path']))->domain(true),
                    ];
                }, $this->files)]);
            }

            return $content;
        });
    }

    protected function getChunksAttr($chunks)
    {
        $chunks = array_values(json_decode($chunks, true));

        return array_map(function ($chunk) {
            if (!empty($chunk['tools'])) {
                $chunk['tools'] = array_values($chunk['tools']);
            }
            return $chunk;
        }, $chunks);
    }
}
