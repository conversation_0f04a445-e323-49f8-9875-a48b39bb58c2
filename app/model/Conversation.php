<?php

namespace app\model;

use app\lib\Date;
use think\db\Query;
use think\Model;
use think\model\concern\SoftDelete;

/**
 * Class app\model\Conversation
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $bot_id
 * @property int $id
 * @property int $space_id
 * @property string $delete_time
 * @property string $source
 * @property string $title
 * @property string $user_id
 * @property-read \app\model\Message[] $messages
 * @property-read \app\model\Space $space
 * @property-read mixed $source_label
 * @method static \think\db\Query onlyTrashed()
 * @method static \think\db\Query recent()
 * @method static \think\db\Query withTrashed()
 */
class Conversation extends Model
{
    use SoftDelete;

    protected $append = ['source_label'];

    public function space()
    {
        return $this->belongsTo(Space::class);
    }

    public function messages()
    {
        return $this->hasMany(Message::class);
    }

    protected function getSourceLabelAttr()
    {
        return match ($this->source) {
            'dev' => '调试',
            'web' => '网页',
            'chat' => 'ThinkChat',
            'api' => 'API',
            'share' => '分享',
            'embed' => '嵌入',
            'wkf' => '微信客服',
            'wmp' => '微信公众号',
            'ding' => '钉钉机器人',
            'lark' => '飞书机器人',
            'qq' => 'QQ机器人',
            default => '--',
        };
    }

    public function scopeRecent(Query $query)
    {
        //获取最近的一次且在12小时内的会话
        return $query->order('update_time', 'desc')
            ->where('update_time', '>', Date::now()->subHours(12));
    }
}
