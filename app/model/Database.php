<?php

namespace app\model;

use app\lib\Hashids;
use Phinx\Db\Adapter\SQLiteAdapter;
use think\db\Connection;
use think\db\connector\Sqlite;
use think\migration\db\Column;
use think\migration\db\Table;
use think\Model;
use think\model\concern\SoftDelete;

/**
 * Class app\model\Database
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property \app\lib\Date $delete_time
 * @property int $id
 * @property int $space_id
 * @property int $user_id
 * @property int $mode
 * @property string $name
 * @property string $description
 * @property-read \app\model\Space $space
 * @property-read \app\model\User $user
 * @property-read mixed $hash_id
 * @property-read \app\model\DatabaseField[]|\think\model\Collection<\app\model\DatabaseField> $fields
 */
class Database extends Model
{
    use SoftDelete;

    const MODE_PRIVATE  = 1;
    const MODE_PUBLIC   = 2;
    const MODE_READONLY = 3;

    protected $append = ['hash_id'];

    public static function onAfterInsert(self $model): void
    {
        $model->runWithTable(function (Table $table) {
            $table->addColumn(Column::string('user_id')->setNullable());
            $table->addColumn(Column::timestamp('create_time')->setDefault('CURRENT_TIMESTAMP'));
            $table->create();
        });
    }

    public function space()
    {
        return $this->belongsTo(Space::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function fields()
    {
        return $this->hasMany(DatabaseField::class);
    }

    protected function getHashIdAttr()
    {
        return Hashids::encode($this->id);
    }

    /**
     * 获取空间数据库路径
     *
     * @return string
     */
    protected function getDbPath()
    {
        $dir  = root_path() . 'storage/database';
        $path = $dir . '/space_' . $this->space_id . '.sqlite3';

        //确保文件存在
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }

            touch($path);
        }

        return $path;
    }

    /**
     * 获取表名
     *
     * @return string
     */
    public function getTableName()
    {
        return $this->hash_id;
    }

    public function runWithTable(callable $callback)
    {
        $config = [
            'name'    => $this->getDbPath(),
            'charset' => 'utf8',
            'suffix'  => '',
        ];

        $adapter = new SQLiteAdapter($config);
        try {
            $tableName = $this->getTableName();

            //清理残留的tmp table
            if ($adapter->hasTable("tmp_{$tableName}")) {
                $adapter->dropTable("tmp_{$tableName}");
            }

            $table = new Table($tableName, [], $adapter);
            return $callback($table);
        } finally {
            $adapter->disconnect();
        }
    }

    public function runWithConnection(callable $callback)
    {
        $config = [
            'type'     => 'sqlite',
            'database' => $this->getDbPath(),
            'charset'  => 'utf8',
        ];

        $connection = new Sqlite($config);
        try {
            return $callback($connection);
        } finally {
            $connection->close();
        }
    }

    public function runWithQuery(callable $callback)
    {
        return $this->runWithConnection(function (Connection $connection) use ($callback) {
            return $callback($connection->table($this->getTableName()));
        });
    }

}
