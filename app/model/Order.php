<?php

namespace app\model;

use app\lib\Cloud;
use app\lib\Hashids;
use think\Model;
use Throwable;

/**
 * Class app\model\Order
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $amount
 * @property int $id
 * @property int $payable_id
 * @property int $space_id
 * @property int $status
 * @property int $user_id
 * @property mixed $goods
 * @property string $goods_type
 * @property string $payable_type
 * @property string $subject
 * @property-read mixed $hash_id
 * @property-read mixed $payable
 */
class Order extends Model
{
    protected $type = [
        'goods' => 'serialize',
    ];

    protected function getHashIdAttr()
    {
        return Hashids::encode($this->id);
    }

    public function payable()
    {
        return $this->morphTo();
    }

    public function paid()
    {
        $this->transaction(function () {
            $model = $this->goods->invoke($this);

            if ($model instanceof Model) {
                $this->payable()->associate($model);
            }

            $this->save(['status' => 1]);
        });
    }

    /**
     * @return void
     * @internal
     */
    public function revoke()
    {
        $this->transaction(function () {
            $this->goods->revoke($this);
            $this->save(['status' => -1]);
        });
    }

    public function canRevoke()
    {
        try {
            return $this->goods->canRevoke($this);
        } catch (Throwable) {
            return false;
        }
    }

    public function pay()
    {
        $params = [
            'order_no'   => $this->hash_id,
            'subject'    => $this->subject,
            'amount'     => $this->amount,
            'notify_url' => (string) url('/api/webhook/order')->domain(true),
        ];

        return app(Cloud::class)->createCharge($params);
    }
}
