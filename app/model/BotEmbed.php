<?php

namespace app\model;

use think\Model;

/**
 * Class app\model\BotEmbed
 *
 * @property \app\lib\Date $create_time
 * @property int $bot_id
 * @property int $id
 * @property mixed $options
 * @property string $domain_whitelist
 * @property string $primary_color
 */
class BotEmbed extends Model
{
    protected $json = ['options'];

    public function checkDomain($domain)
    {
        if (empty($this->domain_whitelist)) {
            return true;
        }

        $whiteList = explode(',', $this->domain_whitelist);

        foreach ($whiteList as $item) {
            $item    = preg_quote(trim($item), '/');
            $pattern = "/^([a-z0-9]+\.)?{$item}$/i";
            if (preg_match($pattern, $domain)) {
                return true;
            }
        }
        return false;
    }
}
