<?php

namespace app\model;

use app\Exception;
use app\lib\bot\Agent;
use app\lib\bot\Flow;
use app\lib\Hashids;
use app\lib\Prompt;
use app\lib\Qdrant;
use Qdrant\Models\Request\CreateCollection;
use Qdrant\Models\Request\CreateIndex;
use Qdrant\Models\Request\SearchRequest;
use Qdrant\Models\Request\VectorParams;
use Qdrant\Models\VectorStruct;
use think\agent\Util;
use think\ai\Client;
use think\helper\Arr;
use think\Model;
use think\model\concern\SoftDelete;
use function think\swoole\helper\iterator;

/**
 * Class app\model\Bot
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property array $annotation
 * @property array $config
 * @property array $feature
 * @property int $id
 * @property int $integration
 * @property int $space_id
 * @property string $avatar
 * @property string $delete_time
 * @property string $description
 * @property string $name
 * @property-read \app\model\BotAnnotation[] $annotations
 * @property-read \app\model\BotToken[] $tokens
 * @property-read \app\model\Conversation[] $conversations
 * @property-read \app\model\Space $space
 * @property-read mixed $hash_id
 * @method static \think\db\Query onlyTrashed()
 * @method static \think\db\Query withTrashed()
 */
class Bot extends Model
{
    use SoftDelete;

    const INTEGRATION_API   = 1;
    const INTEGRATION_SHARE = 2;
    const INTEGRATION_EMBED = 4;
    const INTEGRATION_WKF   = 8;
    const INTEGRATION_WMP   = 16;
    const INTEGRATION_CHAT  = 32;
    const INTEGRATION_QQ    = 64;

    protected $append = ['hash_id', 'feature'];
    protected $type   = [
        'config'     => 'json',
        'annotation' => 'json',
    ];

    public function space()
    {
        return $this->belongsTo(Space::class);
    }

    public function conversations()
    {
        return $this->hasMany(Conversation::class);
    }

    public function tokens()
    {
        return $this->hasMany(BotToken::class);
    }

    public function isAvailable()
    {
        return $this->space && $this->space->isAvailable();
    }

    public function enabled($integration)
    {
        return !!($this->integration & $integration);
    }

    /**
     * @param $source
     * @return \app\lib\bot\Agent
     * @deprecated
     */
    public function getAgent($source = null)
    {
        return new Agent($this, $source);
    }

    public function getApp($source)
    {
        return match ($this->getAttr('type')) {
            'agent' => new Agent($this, $source),
            'flow' => new Flow($this, $source),
            default => throw new Exception('unknown bot type')
        };
    }

    public function chat($source, $params)
    {
        $app    = $this->getApp($source);
        $result = $app->run($params);

        $stream = Arr::get($params, 'stream', true);

        if ($stream) {
            $result->rewind();
            $generator = function () use ($result) {
                while ($result->valid()) {
                    yield 'data: ' . json_encode($result->current()) . "\n\n";
                    $result->next();
                }
                yield "data: [DONE]\n\n";
            };

            $response = iterator($generator());

            return $response->header([
                'Content-Type'      => 'text/event-stream',
                'Cache-Control'     => 'no-cache, must-revalidate',
                'X-Accel-Buffering' => 'no',
            ]);
        } else {
            $conversation = null;
            $chunks       = [];
            $stats        = null;
            foreach ($result as $data) {
                if (isset($data['conversation'])) {
                    $conversation = $data['conversation'];
                }
                $stats = $data['stats'] ?? $stats;

                if (isset($data['chunks'])) {
                    $chunkIndex = $data['chunks']['index'];

                    $chunks[$chunkIndex] = $chunks[$chunkIndex] ?? ['content' => '', 'tools' => [], 'error' => false];

                    if (isset($data['chunks']['tools'])) {
                        $toolIndex = $data['chunks']['tools']['index'];

                        $chunks[$chunkIndex]['tools'][$toolIndex] = $chunks[$chunkIndex]['tools'][$toolIndex] ?? [];

                        foreach ($data['chunks']['tools'] as $key => $value) {
                            /** @var string $key */
                            if ($key != 'index') {
                                $chunks[$chunkIndex]['tools'][$toolIndex][$key] = $value;
                            }
                        }
                    } else {
                        $chunks[$chunkIndex]['content'] .= $data['chunks']['content'] ?? '';
                        $chunks[$chunkIndex]['error']   = $data['chunks']['error'] ?? $chunks[$chunkIndex]['error'];
                    }
                }
            }

            return json([
                'conversation' => $conversation,
                'chunks'       => $chunks,
                'stats'        => $stats,
            ]);
        }
    }

    public function subscribe($source, $params, $callback)
    {
        $app    = $this->getApp($source);
        $result = $app->run($params);

        $content = '';
        foreach ($result as $data) {
            if (isset($data['chunks'])) {
                if (isset($data['chunks']['content'])) {
                    $content .= $data['chunks']['content'];
                    if (empty($data['chunks']['content'])) {
                        if (!empty($content)) {
                            $callback('text', $content);
                        }
                        $content = '';
                    }
                }
                if (isset($data['chunks']['tools'])) {
                    if (isset($data['chunks']['tools']['content'])) {
                        $toolContent = $data['chunks']['tools']['content'];
                        switch (true) {
                            case is_string($toolContent):
                                //文本
                                if (!empty($toolContent)) {
                                    $callback('text', $toolContent);
                                }
                                break;
                            case  $toolContent['type'] == 'image':
                                //图片
                                $callback('image', $toolContent['image']);
                                break;
                        }
                    }
                }
            }
        }
    }

    public function suggestion($conversation)
    {
        return $this->invoke(function (Client $client) use ($conversation) {
            $conversation = Conversation::where('space_id', $this->space_id)
                ->where('bot_id', $this->id)
                ->where('id', $conversation)
                ->findOrFail();

            /** @var \app\model\Message[] $messages */
            $messages = $conversation->messages()
                ->limit(3)
                ->order('create_time desc')
                ->select();

            $historyMessages = [];

            foreach ($messages as $message) {
                $chunkMessages = [
                    [
                        'role'    => 'user',
                        'content' => $message->content,
                    ],
                ];

                foreach ($message->chunks as $chunk) {
                    if (!empty($chunk['error'])) {
                        break 2;
                    }

                    $chunkMessages[] = [
                        'role'    => 'assistant',
                        'content' => $chunk['content'] ?? 'No response',
                    ];
                }

                $tempHistoryMessages = array_merge($chunkMessages, $historyMessages);

                $tokens = Util::tikToken($tempHistoryMessages);
                if ($tokens > 3000) {
                    break;
                }

                $historyMessages = $tempHistoryMessages;
            }

            $res = $client->chat()->completions([
                'model'       => Setting::read('model.secondary'),
                'messages'    => [
                    ...$historyMessages,
                    [
                        'role'    => 'user',
                        'content' => Prompt::SUGGESTION_PROMPT,
                    ],
                ],
                'max_tokens'  => 256,
                'temperature' => 0,
                'stream'      => false,
            ]);

            $this->space->consumeToken(Arr::get($res, 'usage.total_tokens'));

            return json_decode(Arr::get($res, 'message.content'), true);
        });
    }

    public function getAnnotationCollection($create = false)
    {
        $collection = app(Qdrant::class)->collections("annotation@{$this->hash_id}");

        if (empty($this->annotation)) {
            if (!$create) {
                throw new Exception('向量库不存在');
            }
            //创建集合
            $this->transaction(function () use ($collection) {
                $models = app(Client::class)->model()->list(['type' => 'text']);
                $code   = Setting::read('model.embedding');
                $model  = Arr::first($models, function ($model) use ($code) {
                    return $model['code'] == $code;
                });

                if (empty($model)) {
                    throw new Exception('向量模型不存在');
                }

                $this->save([
                    'annotation' => [
                        'model'  => $code,
                        'length' => Arr::get($model, 'params.size', 1536),
                    ],
                ]);

                //创建向量库
                $createCollection = new CreateCollection();
                $vectorParam      = new VectorParams($this->annotation['length'], VectorParams::DISTANCE_COSINE);

                $createCollection->addVector($vectorParam);
                $collection->create($createCollection);

                //创建索引
                $index = $collection->index();
                $index->create(new CreateIndex('annotation_id', 'integer'));
            });
        }

        return $collection;
    }

    public function searchAnnotation($vector)
    {
        try {
            $connection = $this->getAnnotationCollection();

            $searchRequest = (new SearchRequest(new VectorStruct($vector, '')))
                ->setLimit(1)
                ->setScoreThreshold(0.9)
                ->setWithPayload(true);

            $response = $connection->points()->search($searchRequest);

            $result = Arr::get($response, 'result.0');

            if ($result) {
                $id = Arr::get($result, 'payload.annotation_id');
                return $this->annotations()->find($id);
            }
        } catch (\Exception) {

        }
        return null;
    }

    public function annotations()
    {
        return $this->hasMany(BotAnnotation::class);
    }

    protected function getFeatureAttr()
    {
        $type = $this->getAttr('type');

        return match ($type) {
            'agent' => Arr::only($this->config, ['variable', 'onboarding', 'suggestion', 'input', 'output']),
            'flow' => $this->config['feature'],
            default => throw new Exception('unknown type: ' . $type),
        };
    }

    protected function getConfigAttr($value)
    {
        $type    = $this->getAttr('type');
        $feature = [
            'variable'   => [
                'variables' => [],
            ],
            'onboarding' => [
                'enable' => false,
            ],
            'suggestion' => [
                'enable' => false,
            ],
            'tools'      => [],
            'input'      => [
                'speech' => [
                    'enable' => false,
                ],
                'file'   => [
                    'enable' => false,
                ],
            ],
            'output'     => [
                'speech' => [
                    'enable' => false,
                ],
            ],
        ];
        switch ($type) {
            case 'agent':
                $default = [
                    'model'   => [
                        'name' => null,
                    ],
                    'prompt'  => '',
                    'dataset' => [
                        'datasets' => [],
                    ],
                    'database' => [
                        'databases' => [],
                    ],
                    'tools'   => [],
                    ...$feature,
                ];

                if ($value) {
                    return Arr::mergeDeep($default, json_decode($value, true));
                }
                return $default;
            case 'flow':
                $default = [
                    'feature' => $feature,
                    'nodes'   => [],
                    'edges'   => [],
                ];

                if ($value) {
                    return Arr::mergeDeep($default, json_decode($value, true));
                }
                return $default;
        }

        return [];
    }

    protected function getAvatarAttr($avatar)
    {
        if (!empty($avatar)) {
            $url = $avatar;
        } else {
            $url = '/avatar/' . md5("bot@{$this->id}");
        }

        return (string) url($url);
    }

    protected function getHashIdAttr()
    {
        return Hashids::encode($this->id);
    }
}
