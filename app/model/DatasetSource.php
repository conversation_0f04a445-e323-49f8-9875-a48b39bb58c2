<?php

namespace app\model;

use app\job\DatasetTrainJob;
use app\lib\FileReader;
use app\lib\Hashids;
use app\lib\Separator;
use Exception;
use fivefilters\Readability\Configuration;
use fivefilters\Readability\Readability;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Worksheet\MemoryDrawing;
use Qdrant\Models\Filter\Condition\MatchInt;
use Qdrant\Models\Filter\Filter;
use Qdrant\Models\PointsStruct;
use Ramsey\Uuid\Uuid;
use think\ai\Client;
use think\Filesystem;
use think\Model;

/**
 * Class app\model\DatasetSource
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $dataset_id
 * @property int $id
 * @property int $size
 * @property int $status
 * @property string $content
 * @property string $error
 * @property string $message
 * @property string $title
 * @property string $type
 * @property-read \app\model\Dataset $dataset
 * @property-read mixed $hash_id
 */
class DatasetSource extends Model
{
    protected $append = ['hash_id'];

    public static function onAfterInsert(self $model): void
    {
        queue(DatasetTrainJob::class, $model->id, queue: 'train');
    }

    public static function onBeforeDelete(self $model)
    {
        try {
            $collection = $model->dataset->collection();

            $points = $collection->points();

            $filter = new Filter();
            $filter->addMust(new MatchInt('source', $model->id));
            $points->deleteByFilter($filter);
        } catch (Exception) {

        }
    }

    public function dataset()
    {
        return $this->belongsTo(Dataset::class);
    }

    public function train()
    {
        $this->dataset->space->checkQuota('token', 'Token额度不足');
        $this->save(['status' => 2, 'message' => 0]);
        $this->invoke(function (Client $client, Separator $separator, Filesystem $filesystem) {

            $disk = $filesystem->disk('uploads');

            $collection = $this->dataset->collection(true);

            $points = $collection->points();

            //删除旧数据
            $filter = new Filter();
            $filter->addMust(new MatchInt('source', $this->id));
            $points->deleteByFilter($filter);

            $trainContent = function ($content) use ($client, $separator, $points) {
                $progress = 5;
                $this->save(['message' => $progress]);

                if (is_array($content)) {
                    $chunks = $content;
                } else {
                    $chunks = $separator->separate($content);
                }

                if (!empty($chunks)) {
                    $total = count($chunks);

                    array_reduce(array_chunk($chunks, 10), function ($carry, $items) use ($points, $client, $total, &$progress) {
                        $result = $client->embeddings()->create([
                            'model' => $this->dataset->model,
                            'input' => $items,
                        ]);

                        //计费
                        $this->dataset->space->consumeToken($result['usage']['total_tokens']);

                        $embeddings = $result['embeddings'];

                        $points->upsert(PointsStruct::createFromArray(array_map(function ($item, $key) use ($embeddings) {
                            return [
                                'id'      => Uuid::uuid1()->toString(),
                                'vector'  => $embeddings[$key],
                                'payload' => [
                                    'source'  => $this->id,
                                    'content' => $item,
                                ],
                            ];
                        }, $items, array_keys($items))));

                        $progress += count($items) / $total * 95;

                        $this->save(['message' => number_format($progress, 1)]);
                    });
                }
            };

            switch ($this->getAttr('type')) {
                case 'text':
                    $title   = $this->title == '手动输入' ? '' : "# {$this->title}";
                    $content = trim("{$title}\n\n{$this->content}");
                    $trainContent($content);
                    break;
                case 'file':
                    $reader = new FileReader($disk->path($this->content));
                    $text   = $reader->getText();
                    $trainContent($text);
                    break;
                case 'qa':
                    $path   = $disk->path($this->content);
                    $reader = new Xlsx();
                    $reader->setReadEmptyCells(false);
                    $spreadsheet = $reader->load($path);

                    $sheet = $spreadsheet->getSheet(0);

                    $drawingCollection = [];
                    foreach ($sheet->getDrawingCollection() as $drawing) {
                        $coordinates = $drawing->getCoordinates();
                        if (!isset($drawingCollection[$coordinates])) {
                            $drawingCollection[$coordinates] = [];
                        }
                        $drawingCollection[$drawing->getCoordinates()][] = $drawing;
                    }

                    $rows   = $sheet->getHighestRow();
                    $chunks = [];
                    for ($i = 2; $i <= $rows; $i++) {
                        $question = $sheet->getCell('A' . $i)->getValue();
                        $answer   = $sheet->getCell('B' . $i)->getValue();

                        if ($drawings = $drawingCollection["B{$i}"] ?? null) {
                            foreach ($drawings as $drawing) {
                                if ($drawing instanceof MemoryDrawing) {
                                    ob_start();
                                    call_user_func(
                                        $drawing->getRenderingFunction(),
                                        $drawing->getImageResource()
                                    );
                                    $content = ob_get_contents();
                                    ob_end_clean();
                                } else {
                                    if ($drawing->getPath()) {
                                        $content = fopen($drawing->getPath(), 'r');
                                    }
                                }
                                if (!empty($content)) {
                                    $name = date('Ymd') . '/' . md5(uniqid());
                                    $path = "dataset/{$name}.png";

                                    $filename = $disk->path($path);

                                    $dirname = dirname($filename);
                                    if (!is_dir($dirname)) {
                                        mkdir($dirname, 0755, true);
                                    }

                                    file_put_contents($filename, $content);

                                    if (is_resource($content)) {
                                        fclose($content);
                                    }
                                    $url = $disk->url($path);

                                    $answer .= "\n![]({$url})";
                                }
                            }
                        }

                        if (empty($question) || empty($answer)) {
                            continue;
                        }

                        $chunks[] = trim("{$question}\n\n{$answer}");
                    }
                    $trainContent($chunks);
                    break;
                case 'url':
                    $html = url_get_contents($this->content);

                    try {
                        $readability = new Readability(new Configuration());
                        $readability->parse($html);
                        $a = strip_tags($readability->getContent());

                        $title = $readability->getTitle();

                        $content = "# {$title}\n\n" . join("\n", array_filter(array_map(function ($line) {
                                return trim($line);
                            }, preg_split("/\r\n|\n|\r/", $a)), function ($line) {
                                return !empty($line);
                            }));

                        $this->title = $title;
                        $this->size  = strlen($content);

                        $trainContent($content);
                    } catch (Exception) {
                        throw new Exception('网页内容解析失败');
                    }
                    break;
                default:
                    abort(400, '未知的数据类型');
            }
        });
        $this->save(['status' => 1, 'message' => null]);
    }

    public function retrain()
    {
        $this->save(['status' => 0]);
        queue(DatasetTrainJob::class, $this->id, queue: 'train');
    }

    // protected function getStatusAttr($value)
    // {
    //     //最大超时8分钟
    //     if (($value == 0 || $value == 2) && $this->update_time->lt(Date::now()->subMinutes(8))) {
    //         $this->save(['status' => -1, 'message' => 'Timeout']);
    //         return -1;
    //     }
    //     return $value;
    // }

    protected function getHashIdAttr()
    {
        return Hashids::encode($this->id);
    }
}
