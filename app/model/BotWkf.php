<?php

namespace app\model;

use app\lib\wechat\kf\Provider;
use think\Cache;
use think\Model;

/**
 * Class app\model\BotWkf
 *
 * @property \app\lib\Date $create_time
 * @property int $bot_id
 * @property int $id
 * @property string $aes_key
 * @property string $corp_id
 * @property string $cursor
 * @property string $secret
 * @property string $token
 * @property-read \app\model\Bot $bot
 */
class BotWkf extends Model
{
    public function bot()
    {
        return $this->belongsTo(Bot::class);
    }

    /**
     * @return Provider
     */
    public function getProvider()
    {
        return $this->invoke(function (Cache $cache) {
            $provider = new Provider([
                'aes_key'    => $this->aes_key,
                'token'      => $this->token,
                'app_id'     => $this->corp_id,
                'app_secret' => $this->secret,
            ]);

            $provider->setCache($cache);

            return $provider;
        });
    }
}
