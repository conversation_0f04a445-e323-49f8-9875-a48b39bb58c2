<?php

namespace app\model;

use app\lib\Date;
use think\facade\Cache;
use think\Model;

/**
 * Class app\model\AccessToken
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $expire_time
 * @property \app\lib\Date $last_time
 * @property \app\lib\Date $update_time
 * @property int $id
 * @property int $space_id
 * @property string $name
 * @property string $value
 * @property-read \app\model\Space $space
 */
class AccessToken extends Model
{
    const TOKEN_KEY = 'access_token_%s';
    protected $type = [
        'expire_time' => Date::class,
        'last_time'   => Date::class,
    ];

    public static function onAfterDelete(self $model): void
    {
        Cache::delete(sprintf(self::TOKEN_KEY, $model->value));
    }

    public function space()
    {
        return $this->belongsTo(Space::class);
    }

    public function isExpired()
    {
        return !empty($this->expire_time) && $this->expire_time->lt(Date::now());
    }

    public static function getByToken($token)
    {
        $key = sprintf(self::TOKEN_KEY, $token);

        /** @var AccessToken $accessToken */
        $accessToken = Cache::remember($key, function () use ($key, $token) {
            $accessToken = AccessToken::where('value', $token)->with(['space'])->find();

            if ($accessToken) {
                $accessToken->save([
                    'last_time' => Date::now(),
                ]);
            }
            return $accessToken;
        }, 24 * 60 * 60);

        if (empty($accessToken) || $accessToken->isExpired()) {
            return null;
        }

        return $accessToken;
    }
}
