<?php

namespace app\model;

use Exception;
use Qdrant\Models\Filter\Condition\MatchInt;
use Qdrant\Models\Filter\Filter;
use Qdrant\Models\PointsStruct;
use Ramsey\Uuid\Uuid;
use think\ai\Client;
use think\Model;

/**
 * Class app\model\BotAnnotation
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $bot_id
 * @property int $id
 * @property int $message_id
 * @property string $answer
 * @property string $question
 * @property-read \app\model\Bot $bot
 */
class BotAnnotation extends Model
{

    public static function onAfterWrite(self $model): void
    {
        $collection = $model->bot->getAnnotationCollection(true);

        //删除旧数据
        $filter = new Filter();
        $filter->addMust(new MatchInt('annotation_id', $model->id));
        $collection->points()->deleteByFilter($filter);

        $result = app(Client::class)->embeddings()->create([
            'model' => $model->bot->annotation['model'],
            'input' => $model->question,
        ]);

        //计费
        $model->bot->space->consumeToken($result['usage']['total_tokens']);

        $embeddings = $result['embeddings'];

        $collection->points()->upsert(PointsStruct::createFromArray([
            [
                'id'      => Uuid::uuid5(Uuid::NAMESPACE_DNS, $model->id)->toString(),
                'vector'  => $embeddings,
                'payload' => [
                    'annotation_id' => $model->id,
                ],
            ],
        ]));
    }

    public static function onAfterDelete(self $model)
    {
        try {
            $collection = $model->bot->getAnnotationCollection();

            $filter = new Filter();
            $filter->addMust(new MatchInt('annotation_id', $model->id));
            $collection->points()->deleteByFilter($filter);

            //TODO 删除最后一条标注时 关闭标注功能，即删除集合 删除bot表的annotation字段
        } catch (Exception) {

        }
    }

    public function bot()
    {
        return $this->belongsTo(Bot::class);
    }

}
