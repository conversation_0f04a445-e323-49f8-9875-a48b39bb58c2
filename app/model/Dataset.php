<?php

namespace app\model;

use app\job\DatasetSyncJob;
use app\lib\Date;
use app\lib\Hashids;
use Exception;
use Qdrant\Http\GuzzleClient;
use Qdrant\Models\Request\CreateCollection;
use Qdrant\Models\Request\CreateIndex;
use Qdrant\Models\Request\SearchRequest;
use Qdrant\Models\Request\VectorParams;
use Qdrant\Models\VectorStruct;
use Qdrant\Qdrant;
use think\ai\Client;
use think\Cache;
use think\helper\Str;
use think\Model;

/**
 * Class app\model\Dataset
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $last_time
 * @property \app\lib\Date $update_time
 * @property int $freq
 * @property int $id
 * @property int $length
 * @property int $space_id
 * @property int $status
 * @property int $type
 * @property int $user_id
 * @property object $config
 * @property string $description
 * @property string $model
 * @property string $name
 * @property-read \app\model\DatasetSource[] $sources
 * @property-read \app\model\Space $space
 * @property-read mixed $hash_id
 * @property-read mixed $size
 */
class Dataset extends Model
{
    const TYPE_COMMON = 0;
    const TYPE_WEB    = 1;

    protected $json = ['config'];

    protected $append = ['hash_id', 'size'];

    protected $type = [
        'last_time' => Date::class,
    ];

    public static function onAfterInsert(self $model): void
    {
        if ($model->isWeb()) {
            queue(new DatasetSyncJob($model));
        }
    }

    public static function onBeforeDelete(self $model)
    {
        $model->sources()->delete();
        $model->collection()->delete();
    }

    public function space()
    {
        return $this->belongsTo(Space::class);
    }

    public function sources()
    {
        return $this->hasMany(DatasetSource::class);
    }

    public function collection($create = false)
    {
        $qdrantHost = env('QDRANT_HOST', 'topthink-qdrant');
        $config     = new \Qdrant\Config($qdrantHost);
        $qdrant     = new Qdrant(new GuzzleClient($config));

        $collection = $qdrant->collections("dataset@{$this->hash_id}");

        if ($create) {
            try {
                //创建集合
                $createCollection = new CreateCollection();

                $vectorParam = new VectorParams($this->length, VectorParams::DISTANCE_COSINE);

                $createCollection->addVector($vectorParam);
                $collection->create($createCollection);

                //创建索引
                $index = $collection->index();
                $index->create(new CreateIndex('source', 'integer'));
            } catch (\Qdrant\Exception\InvalidArgumentException $e) {
                if (!Str::contains($e->getMessage(), 'already exists')) {
                    throw $e;
                }
            }
        }

        return $collection;
    }

    protected function searchByVector($vector, $limit)
    {
        $searchRequest = (new SearchRequest(new VectorStruct($vector, '')))
            ->setLimit($limit)
            ->setWithPayload(true);

        $connection = $this->collection();

        $response = $connection->points()->search($searchRequest);

        return $response['result'];
    }

    public function search($keyword, $limit = 5)
    {
        try {
            return $this->invoke(function (Client $client, Cache $cache) use ($limit, $keyword) {
                $key = "dataset-search-{$this->model}-" . md5($keyword) . '-vector';

                $vector = $cache->remember($key, function () use ($keyword, $client) {
                    $result = $client->embeddings()->create([
                        'model' => $this->model,
                        'input' => $keyword,
                    ]);

                    //计费
                    $this->space->consumeToken($result['usage']['total_tokens']);

                    return $result['embeddings'];
                }, 60);

                return $this->searchByVector($vector, $limit);
            });
        } catch (Exception) {
            return [];
        }
    }

    public function isCommon()
    {
        return $this->getAttr('type') == self::TYPE_COMMON;
    }

    public function isWeb()
    {
        return $this->getAttr('type') == self::TYPE_WEB;
    }

    public function sync()
    {
        if ($this->isWeb()) {
            $this->save(['status' => 0]);
            queue(new DatasetSyncJob($this));
        }
    }

    protected function getStatusAttr($value)
    {
        //最大超时5分钟
        if (($value == 0 || $value == 2) && $this->update_time->lt(Date::now()->subMinutes(5))) {
            $this->save(['status' => -1]);
            return -1;
        }
        return $value;
    }

    protected function getSizeAttr()
    {
        return $this->sources()->where('status', 1)->sum('size');
    }

    protected function getHashIdAttr()
    {
        return Hashids::encode($this->id);
    }
}
