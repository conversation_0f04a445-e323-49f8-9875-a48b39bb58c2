<?php

namespace app\model;

use think\model\Pivot;

/**
 * Class app\model\SpaceMember
 *
 * @property \app\lib\Date $create_time
 * @property int $access_level
 * @property int $id
 * @property int $space_id
 * @property int $user_id
 * @property-read mixed $role
 */
class SpaceMember extends Pivot
{
    const OWNER     = 60;
    const MASTER    = 50;
    const DEVELOPER = 40;
    const MEMBER    = 20;

    protected $append = ['role'];

    protected function getRoleAttr()
    {
        $accessLevel = (int) $this->getAttr('access_level');

        return match ($accessLevel) {
            self::OWNER => '创始人',
            self::MASTER => '管理员',
            self::DEVELOPER => '开发者',
            self::MEMBER => '普通成员',
            default => $accessLevel,
        };
    }
}
