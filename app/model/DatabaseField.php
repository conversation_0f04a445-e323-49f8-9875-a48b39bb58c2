<?php

namespace app\model;

use Phinx\Db\Table;
use think\exception\ValidateException;
use think\migration\db\Column;
use think\Model;

/**
 * Class app\model\DatabaseField
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $database_id
 * @property int $id
 * @property string $name
 * @property string $label
 * @property string $type
 * @property bool $required
 * @property Database $database
 */
class DatabaseField extends Model
{

    public static function onBeforeInsert(self $model): void
    {
        $model->database->runWithTable(function (Table $table) use ($model) {
            $name = $model->getAttr('name');
            if ($table->hasColumn($name)) {
                throw new ValidateException('字段已存在');
            }

            $colum = $model->getColumn();
            $table->addColumn($colum);
            $table->update();
        });
    }

    public static function onBeforeUpdate(self $model): void
    {
        $model->database->runWithTable(function (Table $table) use ($model) {
            $oldName = $model->getOrigin('name');
            $newName = $model->getAttr('name');

            if ($table->hasColumn($newName)) {
                throw new ValidateException('字段已存在');
            }

            $colum = $model->getColumn();
            $table->changeColumn($oldName, $colum);
            $table->update();
        });
    }

    public static function onBeforeDelete(self $model): void
    {
        $model->database->runWithTable(function (Table $table) use ($model) {
            $name = $model->getAttr('name');
            $table->removeColumn($name);
            $table->update();
        });
    }

    public function getColumn()
    {
        $name = $this->getAttr('name');

        $colum = match ($this->getAttr('type')) {
            'string' => Column::string($name),
            'integer' => Column::integer($name),
            'float' => Column::float($name),
            'boolean' => Column::boolean($name),
            'datetime' => Column::datetime($name),
        };

        if (!$this->getAttr('required')) {
            $colum->setNullable();
        }

        $colum->setComment($this->getAttr('label'));

        return $colum;
    }

    public function database()
    {
        return $this->belongsTo(Database::class);
    }
}
