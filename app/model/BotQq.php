<?php

namespace app\model;

use app\lib\QBot;
use think\Cache;
use think\Model;

/**
 * Class app\model\BotQq
 *
 * @property \app\lib\Date $create_time
 * @property int $bot_id
 * @property int $id
 * @property string $app_id
 * @property string $app_secret
 * @property-read \app\model\Bot $bot
 */
class BotQq extends Model
{
    public function bot()
    {
        return $this->belongsTo(Bot::class);
    }

    /**
     * @return QBot
     */
    public function getClient()
    {
        return $this->invoke(function (Cache $cache) {
            return new QBot($this, $cache);
        });
    }
}
