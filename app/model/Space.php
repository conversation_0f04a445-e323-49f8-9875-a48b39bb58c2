<?php

namespace app\model;

use app\exception\QuotaException;
use app\lib\Date;
use app\lib\goods\Subscribe;
use app\lib\Hashids;
use think\Cache;
use think\Model;
use think\model\concern\SoftDelete;

/**
 * Class app\model\Space
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $expire_time
 * @property \app\lib\Date $reset_time
 * @property \app\lib\Date $update_time
 * @property int $id
 * @property int $token
 * @property string $delete_time
 * @property string $name
 * @property string $plan
 * @property-read \app\model\Bot[] $bots
 * @property-read \app\model\Dataset[] $datasets
 * @property-read \app\model\Plugin[] $plugins
 * @property-read \app\model\SpaceMember $pivot
 * @property-read \app\model\User $owner
 * @property-read \app\model\User[] $members
 * @property-read mixed $hash_id
 * @property-read mixed $plan_name
 * @property-read mixed $plan_level
 * @property-read mixed $quota
 * @method static \think\db\Query onlyTrashed()
 * @method static \think\db\Query withTrashed()
 */
class Space extends Model
{
    use SoftDelete;

    const TOKEN_USED_KEY = 'space_token_used_%s';

    protected $append = ['hash_id', 'quota', 'plan_name', 'plan_level', 'reset_time'];

    protected $type = [
        'expire_time' => Date::class,
    ];

    public function members()
    {
        return $this->belongsToMany(User::class, SpaceMember::class, 'user_id', 'space_id');
    }

    public function bots()
    {
        return $this->hasMany(Bot::class);
    }

    public function datasets()
    {
        return $this->hasMany(Dataset::class);
    }

    public function databases()
    {
        return $this->hasMany(Database::class);
    }

    public function plugins()
    {
        return $this->hasMany(Plugin::class);
    }

    public function accessTokens()
    {
        return $this->hasMany(AccessToken::class);
    }

    /**
     * @param User|integer $member
     */
    public function addMember($member, $accessLevel = SpaceMember::MEMBER, $overwrite = true)
    {
        if ($accessLevel != SpaceMember::OWNER) {
            $this->checkQuota('member', '成员数已达上限');
        }

        /** @var \app\model\SpaceMember|null $pivot */
        $pivot = $this->members()->attached($member) ?: null;

        if ($overwrite || !$pivot) {
            $this->members()->attach($member, [
                'access_level' => $accessLevel,
                'create_time'  => $pivot?->create_time ?: Date::now(),
            ]);
        }
    }

    public function hasMember($member, $accessLevel = null)
    {
        /** @var \app\model\SpaceMember|false $pivot */
        $pivot = $this->members()->attached($member);

        if (!$pivot) {
            return false;
        }

        return $accessLevel == null || $pivot->access_level >= $accessLevel;
    }

    /**
     * 创始人
     * @return \app\model\User
     */
    protected function getOwnerAttr()
    {
        return $this->members()->wherePivot('access_level', SpaceMember::OWNER)->find();
    }

    public function getPluginCredentials()
    {
        $credentials = PluginCredentials::where('space_id', $this->id)->select();

        return $credentials->column('credentials', 'plugin_name');
    }

    protected function getHashIdAttr()
    {
        return Hashids::encode($this->id);
    }

    protected function getQuotaUsedKey()
    {
        $id = "s{$this->id}";
        if ($this->plan == 'trial') {
            $id = "u{$this->owner->id}";
        }
        return sprintf(self::TOKEN_USED_KEY, $id);
    }

    public function checkQuota($name, $message = '额度不足')
    {
        $quota = $this->quota;
        if (!empty($quota[$name]) && $quota[$name]['limit'] <= $quota[$name]['used']) {
            throw new QuotaException($message);
        }
    }

    protected function getResetTimeAttr()
    {
        $day = $this->create_time->day;
        if ($this->plan == 'trial') {
            $day = $this->owner->create_time->day;
        }

        $time = Date::now()->setDay($day);
        if ($time->isPast()) {
            $time = $time->addMonth();
        }

        return $time;
    }

    protected function getQuotaAttr()
    {
        return [
            'token'    => [
                'limit' => $this->getPlanRights('token', $this->token * 1000) * 1000,
                'used'  => $this->invoke(function (Cache $cache) {
                    $key = $this->getQuotaUsedKey();
                    //TODO 框架bug？ 存入的数字会变成字符串
                    return (int) $cache->get($key, 0);
                }),
            ],
            'bot'      => [
                'limit' => $this->getPlanRights('bot'),
                'used'  => $this->bots()->count(),
            ],
            'dataset'  => [
                'limit' => $this->getPlanRights('dataset'),
                'used'  => $this->datasets()->count(),
            ],
            'database' => [
                'limit' => $this->getPlanRights('database'),
                'used'  => $this->databases()->count(),
            ],
            'plugin'   => [
                'limit' => $this->getPlanRights('plugin'),
                'used'  => $this->plugins()->count(),
            ],
            'member'   => [
                'limit' => $this->getPlanRights('member'),
                'used'  => $this->members()->count(),
            ],
        ];
    }

    protected function getPlanRights($name, $extra = 0)
    {
        if ($this->isExpired()) {
            return 0;
        }
        return (Subscribe::PLANS[$this->plan]['rights'][$name] ?? 0) + $extra;
    }

    protected function getPlanAttr($plan)
    {
        return $plan ?? 'trial';
    }

    protected function getPlanNameAttr()
    {
        return Subscribe::PLANS[$this->plan]['name'] ?? '体验版';
    }

    protected function getPlanLevelAttr()
    {
        return Subscribe::PLANS[$this->plan]['level'] ?? 0;
    }

    public function isAvailable()
    {
        return !$this->isExpired();
    }

    public function isExpired()
    {
        return !empty($this->expire_time) && $this->expire_time->isPast();
    }

    public function consumeToken($number)
    {
        if ($number > 0) {
            $this->invoke(function (Cache $cache) use ($number) {
                $key = $this->getQuotaUsedKey();
                if (!$cache->has($key)) {
                    $cache->set($key, $number, $this->reset_time);
                } else {
                    $cache->inc($key, $number);
                }
            });
        }
    }
}
