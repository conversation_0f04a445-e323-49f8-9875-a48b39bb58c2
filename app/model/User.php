<?php

namespace app\model;

use app\lib\Hashids;
use Firebase\JWT\JWT;
use think\Model;

/**
 * Class app\model\User
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $id
 * @property int $status
 * @property string $avatar
 * @property string $mobile
 * @property string $name
 * @property string $openid
 * @property-read \app\model\Space[] $spaces
 * @property-read bool $is_admin
 * @property-read mixed $token
 * @property-read mixed $hash_id
 */
class User extends Model
{
    const STATUS_CERTIFIED  = 1;
    const STATUS_ENTERPRISE = 2;
    const STATUS_ADMIN      = 4;

    public function spaces()
    {
        return $this->belongsToMany(Space::class, SpaceMember::class, 'space_id', 'user_id');
    }

    protected function getTokenAttr()
    {
        return JWT::encode([
            'exp' => time() + 60 * 60 * 24 * 3,
            'iat' => time(),
            'id'  => $this->id,
        ], config('app.token'), 'HS256');
    }

    protected function getHashIdAttr()
    {
        return Hashids::encode($this->id);
    }

    public static function getBySocialUser(\yunwuxin\social\User $socialUser)
    {
        $id = "{$socialUser['channel']}#{$socialUser['id']}";

        $user = self::whereIn('openid', [$id, $socialUser['id']])->find();
        if (empty($user)) {
            $user = new self([
                'openid' => $id,
                'name'   => $socialUser['nickname'],
                'avatar' => $socialUser['avatar'],
            ]);
        }

        $exists = $user->isExists();

        if ($socialUser['channel'] == 'topthink') {
            $status = 0;
            $info   = $socialUser->getRaw();
            if ($info['is_certified']) {
                $status |= User::STATUS_CERTIFIED;
            }
            if ($info['is_enterprise']) {
                $status |= User::STATUS_ENTERPRISE;
            }
            if ($info['is_admin']) {
                $status |= User::STATUS_ADMIN;
            }
            $user->status = $status;
        } else {
            if (!$exists && !User::count()) {
                //第一个用户为管理员
                $user->status = User::STATUS_ADMIN;
            }
        }

        $user->save();

        return $user;
    }

    protected function getIsAdminAttr()
    {
        return ($this->status & self::STATUS_ADMIN) > 0 || in_array($this->id, explode(',', config('app.admin', '')));
    }
}
