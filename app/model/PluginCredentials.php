<?php

namespace app\model;

use think\agent\Credentials;
use think\Model;

/**
 * Class app\model\PluginCredentials
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property \think\agent\Credentials $credentials
 * @property int $id
 * @property int $space_id
 * @property string $plugin_name
 */
class PluginCredentials extends Model
{
    protected $type = [
        'credentials' => Credentials::class,
    ];
}
