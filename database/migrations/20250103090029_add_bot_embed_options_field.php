<?php

use think\migration\Migrator;
use think\migration\db\Column;

class AddBotEmbedOptionsField extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up()
    {
        $this->table('bot_embed')
            ->addColumn(Column::json('options')->setNullable()->setAfter('primary_color'))
            ->update();

        $cursor = \app\model\BotEmbed::cursor();

        foreach ($cursor as $embed) {
            try {
                if ($embed->primary_color) {
                    $embed->save([
                        'options' => [
                            'primaryColor' => $embed->primary_color,
                        ],
                    ]);
                }
            } catch (\Throwable) {
            }
        }
    }

    public function down()
    {
        $this->table('bot_embed')->removeColumn('options')->update();
    }
}
