<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateBotEmbedTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('bot_embed')
            ->addColumn(Column::integer('bot_id')->setUnique())
            ->addColumn(Column::string('primary_color')->setNullable())
            ->addColumn(Column::string('domain_whitelist')->setNullable())
            ->create();

        $this->table('bot')
            ->addSoftDelete()
            ->update();
    }
}
