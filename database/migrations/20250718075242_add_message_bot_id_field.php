<?php

use think\migration\Migrator;
use think\migration\db\Column;

class AddMessageBotIdField extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up()
    {
        // 添加 bot_id 字段
        $this->table('message')
            ->addColumn(Column::integer('bot_id')->setDefault(0)->setAfter('conversation_id'))
            ->update();

        // 更新现有数据，从 conversation 表复制 bot_id
        $this->execute("
            UPDATE message m
            INNER JOIN conversation c ON m.conversation_id = c.id
            SET m.bot_id = c.bot_id
        ");
    }

    public function down()
    {
        // 回滚时删除 bot_id 字段
        $this->table('message')
            ->removeColumn('bot_id')
            ->update();
    }
}
