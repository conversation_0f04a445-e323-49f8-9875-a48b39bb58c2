<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateBotWkfTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('bot_wkf')
            ->addColumn(Column::integer('bot_id')->setUnique())
            ->addColumn(Column::string('corp_id'))
            ->addColumn(Column::string('token'))
            ->addColumn(Column::string('aes_key'))
            ->addColumn(Column::string('secret')->setNullable())
            ->addColumn(Column::string('cursor')->setNullable())
            ->addColumn(Column::timestamp('create_time')->setDefault('CURRENT_TIMESTAMP'))
            ->create();

        $this->table('bot_embed')
            ->addColumn(Column::timestamp('create_time')->setDefault('CURRENT_TIMESTAMP'))
            ->update();
    }
}
