<?php

use think\migration\Migrator;
use think\migration\db\Column;

class AddOrderGoodsTypeField extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up()
    {
        // 添加goods_type字段
        $this->table('order')
            ->addColumn(Column::string('goods_type')->setNullable()->setAfter('goods'))
            ->update();

        // 处理旧数据：根据goods字段的序列化数据推断goods_type
        $this->execute("
            UPDATE `order`
            SET goods_type = CASE
                WHEN goods LIKE '%Subscribe%' THEN 'app\\\\lib\\\\goods\\\\Subscribe'
                WHEN goods LIKE '%Recharge%' THEN 'app\\\\lib\\\\goods\\\\Recharge'
                ELSE NULL
            END
            WHERE goods_type IS NULL
        ");
    }

    public function down()
    {
        // 回滚时删除goods_type字段
        $this->table('order')
            ->removeColumn('goods_type')
            ->update();
    }
}
