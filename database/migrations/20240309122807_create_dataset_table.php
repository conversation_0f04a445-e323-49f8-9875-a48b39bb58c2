<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateDatasetTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('dataset')
            ->addColumn(Column::integer('space_id'))
            ->addColumn(Column::integer('user_id'))
            ->addColumn(Column::string('name'))
            ->addColumn(Column::string('model'))
            ->addColumn(Column::integer('length'))
            ->addTimestamps()
            ->addIndex('space_id')
            ->create();

        $this->table('dataset_source')
            ->addColumn(Column::integer('dataset_id'))
            ->addColumn(Column::string('type'))
            ->addColumn(Column::string('title'))
            ->addColumn(Column::longText('content'))
            ->addColumn(Column::integer('size'))
            ->addColumn(Column::tinyInteger('status')->setDefault(0))
            ->addColumn(Column::string('message')->setNullable())
            ->addTimestamps()
            ->create();
    }
}
