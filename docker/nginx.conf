server {
    listen  80;

    error_log /dev/stderr warn;
    access_log /dev/stdout main;
    client_max_body_size 20m;

    root  /opt/htdocs/asset/dist;

    location /uploads/ {
        root  /opt/htdocs/storage;
        try_files $uri =404;
    }

    location /asset/ {
        try_files $uri =404;
    }

    location /chat {
        try_files $uri /chat.html;
    }

    location / {
        try_files $uri /index.html;
    }

    location ~ ^/api/.+ {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_connect_timeout 300;
        proxy_send_timeout 600;
        proxy_read_timeout 600;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_pass_header X-Accel-Buffering;
    }

    location ~ ^/avatar/.+ {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
