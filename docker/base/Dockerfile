FROM registry.cn-shanghai.aliyuncs.com/topthink/php:8.2-swoole-nginx

RUN \
    sed -i "s/archive.ubuntu.com/mirrors.aliyun.com/g" /etc/apt/sources.list && \
    apt-get update && \
    apt-get -y --no-install-recommends install \
    software-properties-common

#安装tesseract-ocr的依赖
COPY libicu66_66.1-2ubuntu2_amd64.deb /tmp/
RUN dpkg -i /tmp/libicu66_66.1-2ubuntu2_amd64.deb
RUN rm /tmp/libicu66_66.1-2ubuntu2_amd64.deb

#安装tesseract
RUN \
    add-apt-repository ppa:alex-p/tesseract-ocr-devel && \
    apt-get update && \
    apt-get -y --no-install-recommends install \
    tesseract-ocr tesseract-ocr-chi-sim

RUN apt-get clean && rm -rf /var/cache/apt/* && rm -rf /var/lib/apt/lists/*
