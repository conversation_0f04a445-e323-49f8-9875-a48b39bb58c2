<?php

return [
    'api' => [
        'newsToutiao'        => [
            'title'       => '新闻头条',
            'description' => '最新新闻头条，各类社会、国内、国际、体育、娱乐、科技等资讯，更新周期5-30分钟',
            'icon'        => 'https://www.topthink.com/uploads/api/********/7b578122c30889962c8e1ed56650f0e6.png',
            'uris'        => [
                'news/toutiao'  => 100,
                'news/general'  => 100,
                'news/internal' => 100,
                'news/world'    => 100,
            ],
        ],
        'barcodeQuery'       => [
            'title'       => '条码查询',
            'description' => '通过条码查询商品信息（名称、价格、图片、厂家、描述等字段），只支持69开头的13或14位的国内商品，进口和国外商品暂不支持查询',
            'icon'        => 'https://www.topthink.com/uploads/api/********/9c216be9d709bb3faf84176304f69a17.png',
            'uris'        => [
                'barcode/query' => 750,
            ],
        ],
        'weatherQuery'       => [
            'title'       => '天气预报',
            'description' => '查询天气情况：温度、湿度、AQI、天气、风向、生活指数等',
            'icon'        => 'https://www.topthink.com/uploads/api/********/d93ad3b20b5ff5f9704988f6d5dba9ff.png',
            'uris'        => [
                'weather/query' => 100,
                'weather/life'  => 100,
                'aqi/search'    => 100,
            ],
        ],
        'expressQuery'       => [
            'title'       => '快递查询',
            'description' => '根据快递单号查询快递状态信息，支持国内常规快递公司和物流公司，信息更新及时',
            'icon'        => 'https://www.topthink.com/uploads/api/********/d4437ac6c853953804747906fbcd25c8.png',
            'uris'        => [
                'express/query' => 400,
            ],
        ],
        'contentVerify'    => [
            'title'       => '内容安全审核',
            'description' => '支持对文本和图片的安全审核',
            'icon'        => 'https://www.topthink.com/uploads/api/20230809/b16f02531ca07208dafc173bc59ab7f2.png',
            'uris'        => [
                'green/text'   => 50,
                'green/image'  => 100,
            ],
        ],        
        'exchangeConvert'    => [
            'title'       => '汇率查询&换算',
            'description' => '实时货币汇率查询换算，数据仅供参考，交易时以银行柜台成交价为准',
            'icon'        => 'https://www.topthink.com/uploads/api/********/272be74b9f0718184e2c8788329f916f.png',
            'uris'        => [
                'exchange/query'   => 50,
                'exchange/convert' => 50,
            ],
        ],
        'airSearch'          => [
            'title'       => '城市空气质量',
            'description' => '城市空气质量、城市空气PM2.5指数、城市辐射指数',
            'icon'        => 'https://www.topthink.com/uploads/api/********/2158570ac15218103ca06f75e01ddf24.png',
            'uris'        => [
                'air/search' => 75,
                'air/pm'     => 75,
            ],
        ],
        'moneyConvert'       => [
            'title'       => '金额大小写',
            'description' => '阿拉伯数字转为中文大写或英文大写的金额数字',
            'icon'        => 'https://www.topthink.com/uploads/api/********/eaf161c30a2d4353e36e9fe4c9725b2c.png',
            'uris'        => [
                'money/convert' => 100,
            ],
        ],
        'almanac'            => [
            'title'       => '老黄历',
            'description' => '提供老黄历查询,黄历每日吉凶宜忌查询，QQ号码凶吉查询',
            'icon'        => 'https://www.topthink.com/uploads/api/********/de14afdd3444ee474e0363ec68567a1b.png',
            'uris'        => [
                'almanac/date' => 100,
                'almanac/hour' => 100,
                'almanac/qq'   => 100,
            ],
        ],
        'constellation'      => [
            'title'       => '星座运势&配对',
            'description' => '十二星座每日、每月、每年运势，星座及生肖配对',
            'icon'        => 'https://www.topthink.com/uploads/api/********/728499b7a9dace5e6908acd7296eaf52.png',
            'uris'        => [
                'constellation/match'  => 100,
                'constellation/zodiac' => 100,
                'constellation/query'  => 100,
            ],
        ],
        'timeWorld'          => [
            'title'       => '世界时间查询',
            'description' => '查询全球主要城市的时间。',
            'icon'        => 'https://www.topthink.com/uploads/api/********/46a3f06efcd4002b9556788fdfd08820.png',
            'uris'        => [
                'time/world' => 100,
            ],
        ],
        'wechatChoice'       => [
            'title'       => '微信文章',
            'description' => '微信文章精选、阅读数',
            'icon'        => 'https://www.topthink.com/uploads/api/********/b603de136ffb19e5b702fd8cdd7adfc9.png',
            'uris'        => [
                'wechat/choice' => 100,
                'wechat/read'   => 3000,
            ],
        ],
        'ocr'                => [
            'title'       => 'OCR识别',
            'description' => '身份证及通用OCR文字识别',
            'icon'        => 'https://www.topthink.com/uploads/api/********/1f6a958056e1bf7ea359da3cc3ed9a1b.png',
            'uris'        => [
                'ocr/idCardText' => 600,
                'ocr/bankcard'   => 2500,
                'ocr/txt'        => 250,
                'ocr/hand'       => 4000,
            ],
        ],
        /*
        'idcard'          => [
            'title'       => '身份证实名认证',
            'description' => '通过传递姓名+身份证号码，校验信息是否一致',
            'icon'        => 'https://www.topthink.com/uploads/api/********/020c94e70289919d091457ddbed39570.png',
            'uris'        => [
                'idcard/auth'      => 5000,
            ],
        ],
        'telecom' => [
            'title'       => '三网实名认证查询',
            'description' => '检验姓名、身份证、手机号码是否一致，支持移动、联通和电信。',
            'icon'        => 'https://www.topthink.com/uploads/api/********/10c27147fc816fa6eb6db406313a6352.png',
            'uris'        => [
                'telecom/query'    =>    13000,
                'telecom/detail'   =>    15000,
            ],
        ],
        'bankcard' => [
            'title'       => '银行卡三四要素',
            'description' => '检测输入的姓名、身份证号码、银行卡号及手机号（如果不传则不验证手机）是否一致。',
            'icon'        => 'https://www.topthink.com/uploads/api/********/971a52bf074ff250e670c1fa36b4443a.png',
            'uris'        => [
                'bankcard/auth'           =>    12500,
                'bankcard/auth_detail'    =>    17500,
            ],
        ],
        'enterprise'          => [
            'title'       => '企业三要素核验',
            'description' => '核验企业名称、社会统一信用代码、法人一致性',
            'icon'        => 'https://www.topthink.com/uploads/api/********/853d42ba2b82b128a75e8abc36c234fd.png',
            'uris'        => [
                'enterprise/verify'        =>    400,
            ],
        ],
        'EmptyPhoneCheck'          => [
            'title'       => '手机空号核验',
            'description' => '根据客户提供的手机号，分类筛选出其中的空号、实号、风险号和沉默号。',
            'icon'        => 'https://www.topthink.com/uploads/api/********/ada532dccf816ba6ed99cad07b441455.png',
            'uris'        => [
                'unn/batch_ucheck'      =>  65,
            ],
        ],
        'phoneQueryRealTime'          => [
            'title'       => '手机号码实时查询',
            'description' => '直连三大运营商接口，实时反馈查询结果 准确率100%。',
            'icon'        => 'https://www.topthink.com/uploads/api/********/4b3b0581eb4fdbae06781cbbdf62e4a9.png',
            'uris'        => [
                'unn/status'            =>  10000,
            ],
        ],
        */
        'charConvert'        => [
            'title'       => '简繁火星文转换',
            'description' => '实现简体、繁体、火星文之间的转换',
            'icon'        => 'https://www.topthink.com/uploads/api/********/dc602b7b60820bba259a7e668c776820.png',
            'uris'        => [
                'char/convert' => 100,
            ],
        ],
        'getWebPageContent'  => [
            'title'       => '获取网页正文内容',
            'description' => '实时获取网页文章/新闻内容，支持获取图片',
            'icon'        => 'https://www.topthink.com/uploads/api/********/2df890c534dfc070445f2c4ce54e86cc.png',
            'uris'        => [
                'website/htmlcontent' => 100,
            ],
        ],
        'tencentDomainCheck' => [
            'title'       => '域名拦截检测',
            'description' => '支持腾讯QQ及微信的域名安全检测',
            'icon'        => 'https://www.topthink.com/uploads/api/20230501/d2a4a8040f44e7f4eafc1672b93a6cd4.png',
            'uris'        => [
                'wechat/check' => 150,
                'website/qq'   => 150,
            ],
        ],
        'ipCheckScene'       => [
            'title'       => 'IP定位及使用场景',
            'description' => '包含IP精准定位及使用场景查询',
            'icon'        => 'https://www.topthink.com/uploads/api/********/27829a94b20d70f0c075039782f0af97.png',
            'uris'        => [
                'website/ip'  => 100,
                //'ip/police'         =>  7500,
                //'ip/street'         =>  2400,
                'ip/district' => 50,
                'ip/city'     => 50,
                //'ip/scene'          =>  7500,
            ],
        ],
        'bookIsbn'           => [
            'title'       => '书籍ISBN查询',
            'description' => '通过请求10位或13位的ISBN码可以反馈给用户相应的书籍信息和推荐指数',
            'icon'        => 'https://www.topthink.com/uploads/api/********/bc88cb3b45654d9f67f6ba73a9332d25.png',
            'uris'        => [
                'book/isbn' => 1000,
            ],
        ],
        'websiteIcp'         => [
            'title'       => '网站备案查询',
            'description' => '网站ICP备案、网安备案查询，并支持查询公司备案',
            'icon'        => 'https://www.topthink.com/uploads/api/********/2f9f45864608a4b9ded217f2929110cd.png',
            'uris'        => [
                'website/wabeian' => 50,
                'website/icp'     => 250,
                'website/company' => 500,
            ],
        ],
        'websiteDomainInfo'  => [
            'title'       => '查询域名解析',
            'description' => '查询域名所有的解析信息和SSL证书',
            'icon'        => 'https://www.topthink.com/uploads/api/********/86a79d1727fa31e8af1a7c77760f99db.png',
            'uris'        => [
                'website/domain' => 100,
                'website/https'  => 50,
            ],
        ],
        'websiteIpv6'        => [
            'title'       => 'IPV6查询',
            'description' => '可检查域名是否支持ipv6网络，并返回ipv6地址。',
            'icon'        => 'https://www.topthink.com/uploads/api/20230501/d62943a9f4d58bd20c19472dfc84a50b.png',
            'uris'        => [
                'website/ipv6' => 100,
            ],
        ],

        'telecomCode' => [
            'title'       => '标准电码查询',
            'description' => '提供的标准中文电码查询程序结果',
            'icon'        => 'https://www.topthink.com/uploads/api/********/4b810600f623f2b15eabc4fa2d7f05c8.png',
            'uris'        => [
                'telecom/codes' => 100,
            ],
        ],

        'dreamQuery'   => [
            'title'       => '周公解梦',
            'description' => '周公解梦大全,周公解梦查询',
            'icon'        => 'https://www.topthink.com/uploads/api/********/ec84b9b1174972a8c365df4b6b4a598b.png',
            'uris'        => [
                'dream/query' => 100,
            ],
        ],
        'lifeTip'      => [
            'title'       => '生活小窍门',
            'description' => '随机返回一条日常生活小技巧、小窍门，字数在210字以内。',
            'icon'        => 'https://www.topthink.com/uploads/api/20230502/2bbddf84976d1c7db9f818f6fcbe3cf8.png',
            'uris'        => [
                'life/tip' => 100,
            ],
        ],
        'lotteryQuery' => [
            'title'       => '彩票查询',
            'description' => '目前支持双色球、大乐透、七乐彩、七星彩、福彩3D、排列3、排列5，数据来源于网络公开，开奖结果获取可能会有一定的延时。',
            'icon'        => 'https://www.topthink.com/uploads/api/********/c12c8bcbd0a888e0cb2f9b48f1993b7a.png',
            'uris'        => [
                'lottery/query' => 100,
            ],
        ],
        'calendar'     => [
            'title'       => '万年历',
            'description' => '根据日期查询当天详细信息、查询近期假期和查询当年假期',
            'icon'        => 'https://www.topthink.com/uploads/api/********/245caed9bad4ef595b9bf652c82ca392.png',
            'uris'        => [
                'calendar/day'   => 100,
                'calendar/month' => 100,
                'calendar/year'  => 100,
            ],
        ],
        'xinhua'       => [
            'title'       => '新华字典',
            'description' => '根据部首和拼音查询汉字详细信息和同义词等',
            'icon'        => 'https://www.topthink.com/uploads/api/********/4059e26e24d2b3d4138154ccb6f75a72.png',
            'uris'        => [
                'xinhua/query'    => 100,
                'xinhua/querybs'  => 100,
                'xinhua/querypy'  => 100,
                'xinhua/resemble' => 100,
            ],
        ],
        'todayEvent'   => [
            'title'       => '历史上的今天',
            'description' => '回顾历史的长河，历史是生活的一面镜子',
            'icon'        => 'https://www.topthink.com/uploads/api/********/de3077dfa169ac26087dc8579ebb59fe.png',
            'uris'        => [
                'today/event' => 100,
            ],
        ],
        'joke'         => [
            'title'       => '笑话大全',
            'description' => '随机获取一条网络幽默、搞笑、内涵段子，不间断更新',
            'icon'        => 'https://www.topthink.com/uploads/api/********/d2d0c9967e980bbf8929255f982dedf4.png',
            'uris'        => [
                'joke/rand' => 100,
            ],
        ],
        'chengyu'      => [
            'title'       => '成语字典',
            'description' => '查询成语解释、典故、出处及同义',
            'icon'        => 'https://www.topthink.com/uploads/api/********/5670c358d1582a7a5f2189bf80d74bc0.png',
            'uris'        => [
                'chengyu/query'    => 100,
                'chengyu/allusion' => 100,
            ],
        ],
        'why'          => [
            'title'       => '十万个为什么',
            'description' => '十万个为什么查询接口，包含十一个分类数据。',
            'icon'        => 'https://www.topthink.com/uploads/api/20230502/cd78aa9d354befe5167c971c3472f6e6.png',
            'uris'        => [
                'wiki/index' => 100,
            ],
        ],
        'wiki'         => [
            'title'       => '汉语精粹',
            'description' => '包含绕口令、神回复、歇后语、顺口溜及故事大全',
            'icon'        => 'https://www.topthink.com/uploads/api/20230502/cd78aa9d354befe5167c971c3472f6e6.png',
            'uris'        => [
                'wiki/godreply' => 100,
                'wiki/xiehou'   => 100,
                'wiki/story'    => 100,
                'wiki/tongue'   => 100,
                'wiki/doggerel' => 100,
            ],
        ],
        'lunar'        => [
            'title'       => '二十四节气',
            'description' => '农历节气数据接口',
            'icon'        => 'https://www.topthink.com/uploads/api/20230502/ce9a6c7f56e7bc97654fee37c59666e3.png',
            'uris'        => [
                'time/lunar' => 100,
            ],
        ],
        'holiday'      => [
            'title'       => '节假日',
            'description' => '查询本年度节假日信息，接口返回假日名称、类型、对应农历、是否上班、调休，假期范围、假期提示、及工作日信息等。',
            'icon'        => 'https://www.topthink.com/uploads/api/20230502/d50fa8ca87b7811a587308d62cf56073.png',
            'uris'        => [
                'time/holiday' => 100,
            ],
        ],
        'trash'        => [
            'title'       => '垃圾分类',
            'description' => '支持查询绝大部分生活中常见或不常见的废弃物垃圾类型，支持精确搜索和模糊搜索，并给出分类建议。',
            'icon'        => 'https://www.topthink.com/uploads/api/********/bb6e2ad30847d7943c024d4d2f7b071a.png',
            'uris'        => [
                'trash/index' => 100,
            ],
        ],
        'foodNutrient' => [
            'title'       => '营养成分表',
            'description' => '查询近两千种食物的具体详细的营养成分及重量在100克情况下的含量、支持食品种类检索和成分含量排序',
            'icon'        => 'https://www.topthink.com/uploads/api/20230502/0c22ef4a8485fa8e1becaeb2e30f7754.png',
            'uris'        => [
                'food/nutrient' => 100,
            ],
        ],
        'starQuery'    => [
            'title'       => '明星百科档案',
            'description' => '查询全球明星档案公开信息，包含姓名、年龄、身高、国籍等。',
            'icon'        => 'https://www.topthink.com/uploads/api/20230502/60e67abe19fc8f3489c2ed2cb8102bbd.png',
            'uris'        => [
                'star/index' => 100,
            ],
        ],
        'bmiQuery'     => [
            'title'       => 'BMI体重指数',
            'description' => '通过国际权威的标准体重指数（BMI）计算身材是否标准以及健康风险，成年人正常标准体重指数为18.5－23.9。',
            'icon'        => 'https://www.topthink.com/uploads/api/20230502/6050afc013c87e791316d438e71fbae8.png',
            'uris'        => [
                'bmi/index' => 100,
            ],
        ],
        'petIndex'     => [
            'title'       => '宠物大全',
            'description' => '包含猫咪、犬类、爬行动物、小动物、水族类等宠物的生活习性、喂养方法、价格、祖籍、体态特点和图片等信息。',
            'icon'        => 'https://www.topthink.com/uploads/api/20230502/73cd94110f35304ca4d328726b1a312f.png',
            'uris'        => [
                'pet/index' => 100,
            ],
        ],
        'brainTeaser'  => [
            'title'       => '脑筋急转弯',
            'description' => '脑机急转弯，高IQ必备',
            'icon'        => 'https://www.topthink.com/uploads/api/20230502/b983ed8634f7dcf8ddf166011e9eb0f8.png',
            'uris'        => [
                'brain_teaser/index' => 100,
            ],
        ],
        'surname'      => [
            'title'       => '姓氏起源',
            'description' => '百家姓的历史起源、包括古今名人、典故、宗教、分布情况等等。',
            'icon'        => 'https://www.topthink.com/uploads/api/20230502/c071abcc91036ab96b7fd64a51a8f02a.png',
            'uris'        => [
                'surname/index' => 100,
            ],
        ],
        'foodMenu'     => [
            'title'       => '菜谱大全',
            'description' => '菜谱查询，生活类厨房类应用必备',
            'icon'        => 'https://www.topthink.com/uploads/api/********/11edf5ce946623789ae540c0418c4c73.png',
            'uris'        => [
                'food/menu' => 100,
            ],
        ],
        'literary'     => [
            'title'       => '古籍文学',
            'description' => '唐诗宋词元曲精选、古籍名句、励志名言',
            'icon'        => 'https://www.topthink.com/uploads/api/********/6574c68d95bfdec1f1537e180b24b7b2.png',
            'uris'        => [
                'literary/poetry' => 100,
                'literary/song'   => 100,
                'literary/yuan'   => 100,
                'literary/tang'   => 100,
                'literary/quote'  => 100,
                'literary/memo'   => 100,
            ],
        ],
        'friendQuan'   => [
            'title'       => '朋友圈文案',
            'description' => '每次请求随机返回一个适合发朋友圈的简单走心的文案句子。',
            'icon'        => 'https://www.topthink.com/uploads/api/********/741dbc7c65a43d026e54b2cf640800c2.png',
            'uris'        => [
                'literary/quan' => 100,
            ],
        ],
        'one'          => [
            'title'       => 'ONE一个',
            'description' => '韩寒主编的ONE一个杂志，本接口返回每日一句及配图。',
            'icon'        => 'https://www.topthink.com/uploads/api/20230502/2acdff94af689e6dbf023e4fd41f8020.png',
            'uris'        => [
                'literary/one' => 100,
            ],
        ],
        'englishDay'   => [
            'title'       => '每日英语',
            'description' => '每日一句美好英语，随机返回一句美好英语，返回英语句子、释义、语音和分享图片。',
            'icon'        => 'https://www.topthink.com/uploads/api/20230502/8945c9290e167f61c016a30830b6723b.png',
            'uris'        => [
                'english/day' => 100,
            ],
        ],
    ],
];
