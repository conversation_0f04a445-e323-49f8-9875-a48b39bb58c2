const path                = require("path");
const WebpackConfigPlugin = require("@topthink/webpack-config-plugin");
const HtmlWebpackPlugin   = require("html-webpack-plugin");

module.exports = async (env) => {
    const isDevelopment = !!env.dev;
    const isServer      = !!env.WEBPACK_SERVE;
    const host          = env.local ? "bot2.topthink.org" : "bot.topthink.org";

    return {
        mode     : isDevelopment ? "development" : "production",
        entry    : {
            app  : "./src/index.tsx",
            chat : "./src/chat/index.tsx",
            embed: "./src/embed/index.ts"
        },
        cache    : {
            type: "filesystem"
        },
        output   : {
            filename     : function (pathData) {
                if (pathData.chunk.name === "embed") {
                    return "[name].min.js";
                }
                return "[name].[contenthash:6].js";
            },
            chunkFilename: "[id].[contenthash:6].js",
            path         : path.resolve(__dirname, "dist/asset"),
            clean        : true,
            publicPath   : isServer ? "/" : "/asset/"
        },
        module   : {
            rules: [
                {
                    test: /\.xlsx$/,
                    use : [
                        {
                            loader : require.resolve("file-loader"),
                            options: {
                                name: "media/[name].[hash:8].[ext]"
                            }
                        }
                    ]
                }
            ]
        },
        externals: isDevelopment ? {
            "react"           : "React",
            "react-dom"       : "ReactDOM",
            "react-dom/client": "ReactDOM"
        } : {},
        plugins  : [
            new WebpackConfigPlugin({
                serve            : isServer,
                html             : false,
                react            : true,
                externalizeLodash: isDevelopment
            }),
            new HtmlWebpackPlugin({
                filename          : isServer ? "index.html" : "../index.html",
                template          : "public/index.ejs",
                inject            : false,
                scriptLoading     : "blocking",
                chunks            : ["app"],
                templateParameters: {
                    isDevelopment
                }
            }),
            new HtmlWebpackPlugin({
                filename          : isServer ? "chat.html" : "../chat.html",
                template          : "public/index.ejs",
                inject            : false,
                scriptLoading     : "blocking",
                chunks            : ["chat"],
                templateParameters: {
                    isDevelopment
                }
            }),
            isServer && new HtmlWebpackPlugin({
                filename          : "embed.html",
                template          : "public/embed.ejs",
                inject            : false,
                scriptLoading     : "blocking",
                chunks            : ["embed"],
                templateParameters: {
                    isDevelopment
                }
            })
        ].filter(Boolean),
        devServer: {
            hot               : true,
            client            : {
                overlay: {
                    errors       : true,
                    warnings     : false,
                    runtimeErrors: false // this eliminates the error.
                }
            },
            historyApiFallback: {
                rewrites: [
                    {from: /^\/chat\//, to: "/chat.html"},
                    {from: /./, to: "/index.html"}
                ]
            },
            proxy             : {
                "/api"    : {
                    target      : `http://${host}`,
                    changeOrigin: true
                },
                "/uploads": {
                    target      : `http://${host}`,
                    changeOrigin: true
                },
                "/avatar" : {
                    target      : `http://${host}`,
                    changeOrigin: true
                }
            },
            compress          : false
        }
    };
};
