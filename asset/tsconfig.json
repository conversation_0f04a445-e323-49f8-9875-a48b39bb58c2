{"compilerOptions": {"target": "esnext", "module": "esnext", "lib": ["dom", "es2022"], "jsx": "react-jsx", "sourceMap": true, "removeComments": false, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "moduleResolution": "node", "baseUrl": "./", "esModuleInterop": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "skipLibCheck": true, "strictNullChecks": true, "resolveJsonModule": true, "paths": {"@/chat/*": ["./src/chat/*"], "@/components/*": ["./src/components/*"], "@/embed/*": ["./src/embed/*"], "@/hooks/*": ["./src/hooks/*"], "@/images/*": ["./src/images/*"], "@/pages/*": ["./src/pages/*"], "@/scss/*": ["./src/scss/*"], "@/utils/*": ["./src/utils/*"]}}, "include": ["./src/**/*"]}