{"name": "@topthink/agent", "version": "0.0.0", "scripts": {"build": "webpack --progress", "build:dev": "webpack --progress --env dev", "serve-cloud": "webpack serve --env dev", "serve-local": "webpack serve --env dev --env local"}, "dependencies": {"@ant-design/plots": "^1.2.5", "@dagrejs/dagre": "^1.1.4", "@fingerprintjs/fingerprintjs": "^4.2.2", "@lexical/react": "^0.27.2", "@lexical/text": "^0.27.2", "@lexical/utils": "^0.27.2", "@topthink/chat": "^1.1.27", "@topthink/common": "^1.5.21", "@xyflow/react": "^12.4.2", "ace-builds": "^1.33.0", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "byte-size": "^8.1.1", "classnames": "^2.5.1", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.10", "lexical": "^0.27.2", "lodash": "^4.17.21", "nanoid": "^5.1.4", "query-string": "^9.0.0", "react": "^18.2.0", "react-ace": "^11.0.1", "react-bootstrap": "^2.10.1", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-infinite-scroller": "^1.2.6", "styled-components": "^5.3.11", "use-deep-compare": "^1.3.0", "write-excel-file": "^1.4.30"}, "devDependencies": {"@topthink/webpack-config-plugin": "^1.0.25", "@types/byte-size": "^8.1.2", "@types/lodash": "^4.14.202", "@types/node": "^20.12.7", "@types/react": "^18.2.63", "@types/react-dom": "^18.2.20", "@types/react-helmet": "^6.1.11", "@types/react-infinite-scroller": "^1.2.5", "@types/styled-components": "^5.1.34", "babel-plugin-styled-components": "^1.13.3", "html-webpack-plugin": "^5.6.0", "typescript": "^5.1.3", "webpack": "^5.90.3", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}}