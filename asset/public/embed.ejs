<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ThinkBot</title>
    <%= htmlWebpackPlugin.tags.headTags %>
</head>
<body>
<% if (isDevelopment){ %>
    <script src="https://jsdelivr.topthink.com/npm/lodash@4.17/lodash.min.js"></script>
    <script src="https://jsdelivr.topthink.com/npm/react@18/umd/react.development.min.js"></script>
    <script src="https://jsdelivr.topthink.com/npm/react-dom@18/umd/react-dom.development.min.js"></script>
<% } %>
<%= htmlWebpackPlugin.tags.bodyTags %>
<script>
    const url    = new URL(window.location.href);
    const params = new URLSearchParams(url.search);

    const id = params.get('id');
    if (id) {
        window.thinkBot.init(id);

        const iframe = document.createElement('iframe');
        iframe.src = `http://localhost:8080/chat/${id}`;
        iframe.style.width = '30vw';
        iframe.style.height = '90vh';
        iframe.style.border = 'none';
        iframe.allow = 'microphone';
        document.body.prepend(iframe);
    }
</script>
</body>
</html>
