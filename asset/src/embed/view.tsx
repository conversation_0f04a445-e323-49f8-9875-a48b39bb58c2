import { useEffect, useState } from 'react';
import styled, { css } from 'styled-components';
import { ReactComponent as ChatIcon } from '../images/chat.svg';
import { ReactComponent as CloseIcon } from '../images/close.svg';
import { ReactComponent as DownIcon } from '../images/down.svg';

interface Props {
    url: string;
    width?: string;
}

export default function View({ url, width }: Props) {

    const [show, setShow] = useState(false);
    const [bootstrapped, setBootstrapped] = useState(false);

    const [iframe, setIframe] = useState<HTMLIFrameElement | null>(null);

    const [primaryColor, setPrimaryColor] = useState<string>();

    useEffect(() => {
        if (iframe && iframe.contentWindow) {
            const listener = (event: MessageEvent) => {
                try {
                    const [type, payload = {}] = event.data;
                    switch (type) {
                        case 'bootstrapped':
                            setPrimaryColor(payload.primaryColor);
                            setBootstrapped(true);
                            break;
                    }
                } catch {

                }
            };

            window.addEventListener('message', listener);

            return () => {
                window.removeEventListener('message', listener);
            };
        }
    }, [iframe]);

    return <Container $primaryColor={primaryColor}>
        <Window $show={show && bootstrapped} $width={width}>
            <iframe ref={setIframe} src={url} />
            <Down onClick={() => setShow(false)}><DownIcon width={18} height={18} /></Down>
        </Window>
        {bootstrapped && <Button onClick={() => setShow(!show)} $show={show}>
            <Open><ChatIcon width={32} height={32} /></Open>
            <Close><CloseIcon width={32} height={32} /></Close>
        </Button>}
    </Container>;
}

const Down = styled.div`
    position: absolute;
    color: #FFFFFF;
    right: 20px;
    top: 20px;
    cursor: pointer;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
`;

const Window = styled.div<{ $show: boolean, $width?: string }>`
    position: fixed;
    z-index: 2147483001;
    height: calc(100% - 20px - 75px - 20px);
    bottom: 95px;
    right: 20px;
    width: ${({ $width = 370 }) => $width}px;
    min-width: 300px;
    max-width: 100%;
    min-height: 250px;
    max-height: 590px;
    box-shadow: 0 5px 40px rgba(0, 0, 0, .16);
    border-radius: 8px;
    overflow: hidden;
    flex-direction: column;
    display: ${props => props.$show ? 'flex' : 'none'};

    iframe {
        flex: 1;
        background: #FFF;
        border: none;
    }

    @media screen and (max-width: 600px) {
        width: 100%;
        bottom: 0;
        left: 0;
        right: 0;
        border-radius: 0;
        height: calc(100% - 75px);
    }
`;

const Icon = styled.div`
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    left: 0;
    transition: transform .16s linear, opacity .08s linear;
`;

const Open = styled(Icon)`
    opacity: 1;
    transform: rotate(0deg) scale(1);
`;

const Close = styled(Icon)`
    opacity: 0;
    transform: rotate(-30deg);
`;

const Button = styled.div<{ $show: boolean }>`
    position: fixed;
    bottom: 20px;
    left: auto;
    right: 20px;
    z-index: 4000;
    background: var(--bs-primary);
    width: 60px;
    height: 60px;
    color: #FFF;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    box-shadow: 0 1px 6px rgba(0, 0, 0, .06), 0 2px 32px rgba(0, 0, 0, .16);

    ${props => props.$show && css`
        ${Open} {
            opacity: 0;
            transform: rotate(30deg) scale(0);
        }

        ${Close} {
            opacity: 1;
            transform: rotate(0deg);
        }
    `};
`;

const Container = styled.div<{ $primaryColor?: string }>`
    --bs-primary: ${props => props.$primaryColor || '#3c60FF'};
`;
