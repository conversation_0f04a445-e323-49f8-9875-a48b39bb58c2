import { createRoot } from 'react-dom/client';
import { StyleSheetManager } from 'styled-components';
import View from '@/embed/view';

interface Options {
    variables?: string;
    width?: string;
}

export default class ThinkBot {

    readonly #host: string;

    constructor(script: HTMLScriptElement) {

        const attr = (name: string) => {
            return script.getAttribute(name) || undefined;
        };

        this.#host = new URL(script.src).origin;

        const id = attr('data-id');

        if (id) {
            const variables = attr('data-variables');
            const width = attr('data-width');
            this.init(id, {
                variables,
                width
            });
        }
    }

    init(botId: string, options: Options = {}) {

        const container = document.createElement('div');

        document.body.appendChild(container);

        const shadow = container.attachShadow({ mode: 'closed' });

        const app = document.createElement('div');
        shadow.appendChild(app);

        let url = `${this.#host}/chat/${botId}`;

        if (options.variables) {
            url += `?${options.variables}`;
        }

        const element = <StyleSheetManager target={shadow}>
            <View url={url} width={options.width} />
        </StyleSheetManager>;

        const root = createRoot(app);

        root.render(element);

    }
}
