import ThinkBot from './think-bot';

declare global {
    interface Window {
        thinkBot: ThinkBot | object;
    }
}

((window) => {
    const { document, } = window;

    const currentScript = document.currentScript as HTMLScriptElement;

    if (!currentScript) {
        return;
    }

    window.thinkBot = new ThinkBot(currentScript);

    document.dispatchEvent(new CustomEvent('think:bot:ready'));
})(window);
