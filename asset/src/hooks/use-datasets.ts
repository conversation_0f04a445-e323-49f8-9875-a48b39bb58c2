import { useCurrentSpace } from '@/components/space-provider';
import { request, useAsync } from '@topthink/common';

export default function useDatasets(id: string[]) {

    const { current: space } = useCurrentSpace();

    const { result: datasets = [] } = useAsync<Dataset[]>(async (id: string) => {
        if (id.length > 0) {
            return await request({
                url: `/space/${space.hash_id}/dataset`,
                params: {
                    id
                }
            });
        }
        return [];
    }, [id], {
        setLoading: (state) => {
            return {
                ...state,
                loading: true
            };
        }
    });

    return datasets;
}
