import { useCurrentSpace } from '@/components/space-provider';
import { request, useAsync } from '@topthink/common';

export default function useDatabases(id: string[]) {

    const { current: space } = useCurrentSpace();

    const { result: databases = [] } = useAsync<Database[]>(async (id: string) => {
        if (id.length > 0) {
            return await request({
                url: `/space/${space.hash_id}/database`,
                params: {
                    id
                }
            });
        }
        return [];
    }, [id], {
        setLoading: (state) => {
            return {
                ...state,
                loading: true
            };
        }
    });

    return databases;
}
