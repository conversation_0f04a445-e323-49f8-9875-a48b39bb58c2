import { useCurrentSpace } from '@/components/space-provider';
import { request, useAsync } from '@topthink/common';

export default function useAgents() {
    const { current: space } = useCurrentSpace();

    const { result: agents = [] } = useAsync<Bot[]>(async () => {
        return await request({
            url: `/space/${space.hash_id}/bot/search`,
            params: {
                type: 'agent',
            }
        });
    }, [], {
        setLoading: (state) => {
            return {
                ...state,
                loading: true
            };
        }
    });

    return agents;

}
