export const DEFAULT_PLUGIN_SCHEMA = `openapi: 3.0.0
servers:
  - url: 'http://petstore.swagger.io/v1' #请求的域名，必须是URL地址格式
paths:
  /pets:
    get:
      operationId: list_pets #【必须】接口标识，名称可由英文和下划线组成，禁止有空格。
      summary: 宠物列表 #【必须】接口名称，显示为工具名称
      description: '获取宠物列表' #【必须】清晰描述接口功能，由LLM 判断调用该接口时机,描述文案长度限制为 200 个字符长度。

    post:
      operationId: create_pets
      summary: 宠物创建
      description: '创建一个宠物'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - name
              properties:
                name:
                  type: string
                  description: 宠物名称
                tag:
                  type: string
                  description: 标签
                  
  /pets/{id}:
    get:
      operationId: get_pet
      summary: 宠物信息
      description: 根据宠物ID获取单个宠物信息
      parameters:
        - name: id #【必须】参数字段名称
          in: path #【必须】参数位置，支持：path(路径参数)/query(追加在url地址之后的参数)/header(请求中使用的自定义请求头)/cookie(特定的cookie值)
          description: 宠物ID #【必须】清晰描述参数含义,描述文案长度限制为 200 个字符长度。
          required: true
          schema:
            type: integer #字段类型，支持：integer/number/string/boolean
            default: null # 参数的默认值
`;

export const SUPPORT_FILE_TYPES = ['.txt', '.md', '.pdf', '.docx', '.xlsx', '.pptx', '.png', '.jpg'];

export const OWNER = 60;
export const MASTER = 50;
export const DEVELOPER = 40;
export const MEMBER = 20;

export const ROLES = {
    [OWNER]: {
        name: '创始人',
        description: '拥有所有权限'
    },
    [MASTER]: {
        name: '管理员',
        description: '拥有空间管理权限'
    },
    [DEVELOPER]: {
        name: '开发者',
        description: '拥有智能体编排权限'
    },
    [MEMBER]: {
        name: '普通成员',
        description: '拥有使用智能体权限'
    },
} as const;
