import dayjs from 'dayjs';

export default function formatPeriodDate(period: string, text: string) {
    const date = dayjs(text);

    switch (period) {
        case '1year':
            return date.format('YYYY-MM');
        case '180days':
        case '6months':
            return date.format('MM-DD');
        case '90days':
        case '30days':
        case 'last-month':
            return date.format('YYYY-MM-DD');
        case '7days':
            return date.format('MM-DD HH:mm');
        case 'yesterday':
        case '24hours':
        default:
            return date.format('HH:mm');
    }
}
