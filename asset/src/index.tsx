import { createRoot } from 'react-dom/client';
import './scss/app.scss';
import { createApplication, request } from '@topthink/common';
import routes from './routes';
import ManifestProvider from './components/manifest-provider';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import relativeTime from 'dayjs/plugin/relativeTime';
import Login from '@/pages/login';

dayjs.locale('zh-cn');
dayjs.extend(relativeTime);

const container = document.getElementById('app');

if (container) {
    const root = createRoot(container);

    const App = createApplication({
        baseURL: '/api',
        implicit: false,
        RootComponent: ManifestProvider,
        routes,
        async userResolver() {
            return await request('/auth/current');
        },
        async onAuthorize(token) {
            const result = await request({
                url: '/auth/register',
                method: 'POST',
                data: token
            });
            return result.token;
        },
        onLogin: Login,
    });

    root.render(<App />);
}
