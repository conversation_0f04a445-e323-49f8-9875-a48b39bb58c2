import { createRoot } from 'react-dom/client';
import '../scss/app.scss';
import Chat<PERSON><PERSON> from '@/chat/chat-box';
import { isRequestError, request, Result, ToastProvider } from '@topthink/common';
import queryString from 'query-string';
import { ReactNode } from 'react';

(async () => {
    const container = document.getElementById('app');

    if (container) {
        const root = createRoot(container);

        const id = location.pathname.replace(/^\/chat\//, '');
        const variables = queryString.parse(location.search) as Record<string, string>;
        const inFrame = window !== window.parent;

        const url = inFrame ? document.referrer : null;

        let clientId = sessionStorage.getItem('client_id');
        if (!clientId) {
            const { default: FingerprintJS } = await import('@fingerprintjs/fingerprintjs');
            const fp = await FingerprintJS.load();

            const result = await fp.get();

            clientId = result.visitorId;
            sessionStorage.setItem('client_id', clientId);
        }

        let app: ReactNode;
        try {
            const { bot, conversation, config, options, token } = await request({
                url: `/chat/${id}`,
                params: {
                    url,
                    client_id: clientId
                },
                authTokenName: ''
            });

            if (inFrame) {
                window.parent.postMessage(['bootstrapped', options || {}], '*');
            }

            const req = request.create({
                baseURL: 'chat',
                headers: {
                    'Authorization': `Token ${token}`
                }
            });

            const { primaryColor, placeholder } = options || {};

            app = <ToastProvider>
                <ChatBox
                    request={req}
                    bot={bot}
                    config={config}
                    conversation={conversation}
                    inFrame={inFrame}
                    variables={variables}
                    primaryColor={primaryColor}
                    placeholder={placeholder}
                />
            </ToastProvider>;
        } catch (error: any) {
            let message = isRequestError(error) ? error.errors : error.message;
            if (typeof message !== 'string') {
                message = Object.values(message).join('\n');
            }
            app = <div className='vh-100 vw-100 d-flex align-items-center justify-content-center'>
                <Result status={'error'} title={message} />
            </div>;
        }

        root.render(app);
    }
})();
