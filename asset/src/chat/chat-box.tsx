import { styled, RequestInstance } from '@topthink/common';
import hexToRgb from '@/utils/hex-to-rgb';
import { Conversation, MessageBox } from '@topthink/chat';
import { useMemo } from 'react';

interface Props {
    request: RequestInstance;
    bot: Bot;
    config: BotFeature;
    conversation?: Conversation;
    inFrame: boolean;
    primaryColor?: string;
    variables?: Record<string, string>;
    placeholder?: string;
}

export default function ChatBox({
    request,
    bot,
    config,
    conversation,
    inFrame,
    primaryColor,
    placeholder,
    variables: vars
}: Props) {

    const variables = useMemo(() => {
        if (config.variable.variables.length > 0) {
            return {
                config: config.variable.variables,
                values: vars ? Object.fromEntries(
                    Object.entries(vars).filter(([key]) => config.variable.variables.find(val => val.key === key))
                ) : undefined
            };
        }
    }, [config, vars]);

    return <Container $responsive={inFrame} $primaryColor={primaryColor || '#3c60FF'}>
        <Header>
            <Avatar>
                <img src={bot.avatar} />
            </Avatar>
            <Info>
                <Nickname>{bot.name || '小智'}</Nickname>
                <Signature>{bot.description || '我是智能客服呢'}</Signature>
            </Info>
            <Action></Action>
        </Header>
        <Body>
            <MessageBox
                request={request}
                bot={bot}
                speech={config.output.speech.enable ? config.output.speech : undefined}
                onboarding={config.onboarding}
                conversation={conversation}
                input={{
                    placeholder,
                    variables: variables,
                    suggestion: config.suggestion.enable,
                    fileTypes: config.input.file.enable ? (config.input.file.types || []) : undefined,
                    speech: config.input.speech.enable ? config.input.speech : undefined,
                    autoFocus: true,
                }}
            />
        </Body>
    </Container>;
}

const Action = styled.div`

`;

const Body = styled.div`
    flex: 1;
    background: #fafafa;
    overflow: hidden;
`;

const Signature = styled.div`
    word-break: keep-all;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 13px;
    color: rgba(255, 255, 255, 0.8);
`;

const Nickname = styled.div`
    font-size: 16px;
    word-break: keep-all;
    overflow: hidden;
    text-overflow: ellipsis;
`;

const Info = styled.div`
    flex: 1;
    font-weight: normal;
    overflow: hidden;
`;

const Avatar = styled.div`
    position: relative;
    background: rgba(255, 255, 255, 0.8);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--bs-primary);

    img, svg {
        border-radius: 50%;
        width: 100%;
        height: 100%;
    }

`;

const Header = styled.div`
    height: 75px;
    background: var(--bs-primary);
    overflow: hidden;
    padding: 0 60px 0 20px;
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.95);
`;

const Container = styled.div<{ $responsive: boolean, $primaryColor: string }>`
    --bs-primary: ${props => props.$primaryColor};
    --bs-primary-rgb: ${props => hexToRgb(props.$primaryColor)};
    max-width: ${props => props.$responsive ? '100%' : '740px'};
    width: 100%;
    margin: 0 auto;
    height: 100vh;
    display: flex;
    flex-direction: column;
`;
