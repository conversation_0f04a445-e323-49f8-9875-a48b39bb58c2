declare module '*.svg' {
    import { ElementType } from 'react';
    const content: any;
    export default content;
    export const ReactComponent: ElementType;
}

declare module '*.png'
declare module '*.xlsx';

interface Manifest {
    website: {
        title: string;
        logo: string | null;
        help: string | null;
        qrcode: string | null;
        scripts: string;
    };
    model: {
        bot: {
            primary: ChatModel[];
            audio: AudioModel[];
        }
    };
    cloud: boolean;
    login: {
        qrcode: boolean;
        channels: { name: string; label: string, autologin?: boolean }[];
    };
}

interface BasicStatistics {
    user: {
        total: number;
        yesterday: number;
    };
    space: {
        total: number;
        yesterday: number;
    };
    bot: {
        total: number;
        yesterday: number;
    };
    license: {
        plan: string;
        tokens: number
    };
}

interface Space {
    id: string;
    plan: string;
    plan_name: string;
    plan_level: number;
    hash_id: string;
    name: string;
    expire_time: string;
    create_time: string;
    reset_time: string;
    token: number;
    quota: {
        token: {
            limit: number;
            used: number;
        },
        bot: {
            limit: number;
            used: number;
        },
        dataset: {
            limit: number;
            used: number;
        },
        database: {
            limit: number;
            used: number;
        },
        plugin: {
            limit: number;
            used: number;
        },
        member: {
            limit: number;
            used: number;
        }
    };

    pivot: {
        access_level: number;
    };

    [index: string]: any;
}

interface Bot<T extends BotConfiguration = BotConfiguration> {
    id: string;
    hash_id: string;
    type: 'agent' | 'flow';
    name: string;
    avatar: string;
    description: string;
    integration: number;
    config: T;
    feature: BotFeature;
}

interface Variable {
    key: string;
    label: string;
    type: 'text' | 'textarea' | 'select';
    options?: string[];
    required?: boolean;
}

interface ModelConfiguration {
    name: string;
    context_tokens?: number;
    tool?: boolean;
    vision?: boolean;
    thinking?: string;
    params?: {
        temperature?: number
        max_tokens?: number
        history_round?: number
    };
}

interface BotFeature {
    variable: {
        variables: Variable[]
    };
    onboarding: {
        enable: boolean
        prologue: string
        questions: string[]
    };
    suggestion: {
        enable: boolean
    };
    input: {
        file: {
            enable: boolean
            types?: string[]
        },
        speech: {
            enable: boolean
            model?: string
        }
    };
    output: {
        speech: {
            enable: boolean
            model?: string
            voice?: string
            autoplay?: boolean
        }
    };
}

interface BotAgentConfiguration extends BotFeature {
    model: ModelConfiguration
    prompt: string
    dataset: {
        datasets: string[]
    }
    database: {
        databases: string[]
    }
    tools: {
        plugin: string
        name: string
        args?: any
        enable?: boolean
    }[],
}

interface BotFlowConfiguration {
    feature: BotFeature;
    nodes: any[];
    edges: any[];
    viewport?: any;
}

type BotConfiguration = BotAgentConfiguration | BotFlowConfiguration;

interface ConfigUpdaterProps<C, T extends (keyof C | undefined) = undefined, V = T extends keyof C ? C[T] : C> {
    value: V;
    onChange: (fn: (value: V) => void | V) => void;
}

interface ConfigUpdater<C> {
    (name?: undefined): ConfigUpdaterProps<C>;

    <T extends keyof C>(name: T): ConfigUpdaterProps<C, T>;
}

type BotAgentConfigurationUpdaterProps<T extends keyof BotAgentConfiguration = undefined> = ConfigUpdaterProps<BotAgentConfiguration, T>;
type BotFlowConfigurationUpdaterProps<T extends keyof BotFlowConfiguration = undefined> = ConfigUpdaterProps<BotFlowConfiguration, T>;

interface Dataset {
    id: string;
    hash_id: string;
    type: number;
    name: string;
    description: string;
    freq: number;
    status: number;
    last_time: string;
    config: {
        sitemap: string
    };
}

interface DatasetSource {
    id: string;
    hash_id: string;
}

interface DatasetSourcePart {
    payload: {
        content: string;
    };
    score: number;
}

interface Database {
    id: string;
    hash_id: string;
    name: string;
    mode: number;
    description: string;
    create_time: string;
    update_time: string;
}

interface DatabaseField {
    id: string;
    hash_id: string;
    name: string;
    label: string;
    type: string;
    description: string;
    required: boolean;
    create_time: string;
    update_time: string;
}

interface Plugin {
    hash_id: string;
    type: number;
    icon: string;
    title: string;
    name: string;
    description: string;
    config: {
        schema: string;
        auth: {
            provider: string;
        } | null;
    };
}

interface Tool {
    name: string;
    title: string;
    description: string;
    fee: number;
    parameters: Record<string, {
        type: any
        title?: string
        description: string
        required?: boolean
        placeholder?: string
        encrypt?: boolean
        default?: string
        enum?: string[]
        enumNames?: string[]
        url?: string
        provider?: 'llm' | 'user'
    }> | null;
}

interface BotPlugin {
    name: string;
    title: string;
    description: string;
    icon: string;
    credentials: Record<string, {
        title: string
        required?: boolean
        placeholder?: string
        encrypt?: boolean
        default?: string
        url?: string
    }> | null;
    tools: Tool[];
}

interface BaseModel {
    type: string;
    label: string;
    code: string;
}

interface ChatModel extends BaseModel {
    factor: string;
    params?: {
        tool?: boolean
        vision?: boolean
        context_tokens?: number
        thinking?: {
            is: boolean;
            closable?: boolean;
            auto?: boolean;
        }
    };
}

interface TextModel extends BaseModel {
    factor: {
        embedding?: string;
        rerank?: string;
    };
    params?: {
        size?: number
    };
}

interface AudioModel extends BaseModel {
    factor: string;
    params?: {
        voice?: { name: string, code: string }[]
    };
}

type Model = ChatModel | TextModel | AudioModel;

interface AccessToken {
    id: number;
    name: string;
    create_time: string;
    last_time?: string;
    expire_time?: string;
    token?: string;
}

interface Plan {
    name: string;
    slogan: string;
    price?: number;
    rights?: {
        token: number;
        bot: number;
        member: number;
        dataset: number;
        database: number;
        plugin: number;
        history: number;
        api: boolean;
    };
}

type Service = {
    name: string;
    price: number;
    unit: string;
    step: number;
    description?: string;
} | {
    name: string;
    price: number;
    description?: string;
}

interface DataItem {
    date: string,
    value: number
}
