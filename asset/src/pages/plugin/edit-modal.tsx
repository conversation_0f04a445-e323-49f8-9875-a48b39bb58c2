import { ModalForm, styled } from '@topthink/common';
import { useCurrentSpace } from '@/components/space-provider';
import { ReactNode } from 'react';

interface Props {
    plugin: Plugin;
    onSuccess: (data: Plugin) => void;
    children: ReactNode;
    variant?: 'light';
    size?: 'sm';
}

export default function EditPluginModal({ plugin, children, onSuccess, variant, size }: Props) {
    const { current } = useCurrentSpace();
    return <ModalForm
        action={`/space/${current.hash_id}/plugin/${plugin.hash_id}`}
        method={'put'}
        buttonProps={{ variant, size }}
        modalProps={{ header: '基本信息' }}
        text={children}
        formData={{
            icon: plugin.icon,
            title: plugin.title,
            description: plugin.description
        }}
        transformData={data => {
            if (data.icon.startsWith('data:image/')) {
                delete data['icon'];
            }
            return data;
        }}
        schema={{
            type: 'object',
            properties: {
                icon: {
                    type: 'string',
                    title: '图标'
                },
                title: {
                    type: 'string',
                    title: '名称'
                },
                description: {
                    type: 'string',
                    title: '描述'
                },

            },
            required: ['title']
        }}
        uiSchema={{
            icon: {
                'ui:widget': 'avatar',
                'ui:options': {
                    as: Avatar,
                    endpoint: '/upload/avatar',
                    label: false
                }
            },
            title: {
                'ui:autofocus': true,
                'ui:placeholder': '输入插件名称'
            },
            description: {
                'ui:widget': 'textarea',
                'ui:placeholder': '输入插件描述',
                'ui:options': {
                    rows: 3
                }
            }
        }}
        onSuccess={onSuccess}
    />;
}

const Avatar = styled.div`
    margin: 0 auto;
`;
