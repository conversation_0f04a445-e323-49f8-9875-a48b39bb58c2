import { Button, Content, RequestButton, Space, Table, TableType } from '@topthink/common';
import { useCurrentSpace } from '@/components/space-provider';
import { Badge, Stack } from 'react-bootstrap';
import CreatePluginModal from '@/components/create-plugin-modal';
import EditPluginModal from '@/pages/plugin/edit-modal';
import ApiConfig from './api-config';
import { useRef } from 'react';
import McpConfig from './mcp-config';

export const Component = function() {
    const { current } = useCurrentSpace();
    const table = useRef<TableType>(null);

    return <Content extra={<CreatePluginModal button={Button} onSuccess={() => table.current?.reload()} />}>
        <Table
            ref={table}
            source={`/space/${current.hash_id}/plugin`}
            emptyText={<span className='text-muted'>暂无插件</span>}
            columns={[
                {
                    dataIndex: 'name',
                    title: '名称',
                    render({ record }) {
                        return <Stack direction={'horizontal'} gap={2}>
                            <img src={record.icon} width={30} height={30} />
                            <Stack gap={1}>
                                <span className='link-dark'>{record.title}</span>
                                <span className='fs-7 text-muted'>{record.description || '暂无描述'}</span>
                            </Stack>
                        </Stack>;
                    }
                },
                {
                    dataIndex: 'type',
                    title: '类型',
                    width: 100,
                    align: 'center',
                    render({ value }) {
                        return value === 3 ? <Badge bg={'success'}>OpenAPI</Badge> :
                            <Badge bg={'secondary'}>MCP</Badge>;
                    }
                },
                {
                    dataIndex: 'create_time',
                    title: '创建时间',
                    width: 150,
                },
                {
                    key: 'action',
                    title: '操作',
                    width: 120,
                    align: 'right',
                    render({ record, action }) {
                        return <Space>
                            {record.type === 3 ? <ApiConfig
                                plugin={record}
                                onSuccess={action.reload}
                            /> : <McpConfig
                                plugin={record}
                                onSuccess={action.reload}
                            />}
                            <EditPluginModal plugin={record} onSuccess={() => action.reload()}>
                                编辑
                            </EditPluginModal>
                            <RequestButton method={'delete'} url={`/space/${current.hash_id}/plugin/${record.hash_id}`} confirm={'确定要删除吗?'} onSuccess={action.reload}>删除</RequestButton>
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
};
