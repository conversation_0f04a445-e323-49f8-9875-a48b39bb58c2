import { useCurrentSpace } from '@/components/space-provider';
import { ModalForm } from '@topthink/common';

export default function McpConfig({ plugin, onSuccess }: { plugin: Plugin, onSuccess?: () => void }) {
    const { current } = useCurrentSpace();
    const { config = {} } = plugin;

    return <ModalForm
        text={'配置'}
        action={`/space/${current.hash_id}/plugin/${plugin.hash_id}/config`}
        modalProps={{
            header: 'MCP配置',
            size: 'lg',
        }}
        formData={config}
        schema={{
            type: 'object',
            properties: {
                url: {
                    type: 'string',
                    title: 'MCP地址'
                },
                transport: {
                    type: 'string',
                    title: '传输方式',
                    enum: ['streamable_http', 'sse',],
                    enumNames: ['HTTP', 'SSE',],
                    default: 'streamable_http'
                },
                headers: {
                    type: 'array',
                    title: '请求头',
                    items: {
                        type: 'object',
                        properties: {
                            key: {
                                type: 'string',
                                title: '名称'
                            },
                            value: {
                                type: 'string',
                                title: '值'
                            }
                        },
                        required: ['key', 'value']
                    }
                }
            }
        }}
        uiSchema={{
            headers: {
                'ui:options': {
                    addable: true,
                    removable: true,
                    orderable: false
                },
                items: {
                    'ui:options': {
                        inline: true
                    },
                    key: {
                        'ui:placeholder': '名称',
                        'ui:label': false,
                        'ui:col': 4
                    },
                    value: {
                        'ui:placeholder': '值',
                        'ui:label': false,
                        'ui:col': 8
                    }
                }
            }
        }}
        onSuccess={onSuccess}
    />;
}
