import { Content, Modal, request, RequestButton, Space, Table, useNavigate, useUser } from '@topthink/common';
import { useCurrentSpace } from '@/components/space-provider';
import InviteModal from '@/pages/member/invite-modal';
import { Dropdown } from 'react-bootstrap';
import { MASTER, OWNER, ROLES } from '@/utils/constants';
import LevelAccess from '@/components/level-access';
import getUserAvatar from '@/utils/get-user-avatar';

export const Component = function() {
    const { current, refresh } = useCurrentSpace();
    const [user] = useUser();
    const navigate = useNavigate();

    const extra = <LevelAccess level={MASTER}>
        <RequestButton
            url={`/space/${current.hash_id}/member/invite`}
            method={'POST'}
            onSuccess={(result) => {
                Modal.show(InviteModal, [current, result]);
            }}
        >
            <i className='bi bi-people me-2' />邀请成员
        </RequestButton>
    </LevelAccess>;

    return <Content extra={extra}>
        <Table
            source={`/space/${current.hash_id}/member`}
            columns={[
                {
                    title: '用户',
                    dataIndex: 'name',
                    render({ record }) {
                        return <Space>
                            <img src={getUserAvatar(record)} width={24} height={24} className={'rounded-circle'} />
                            {record.name}
                        </Space>;
                    }
                },
                {
                    title: '角色',
                    dataIndex: 'role',
                    width: 100,
                    render({ record, action }) {
                        if (current.pivot.access_level >= MASTER && current.pivot.access_level > record.pivot.access_level) {
                            return <Dropdown onSelect={async (eventKey) => {
                                if (eventKey) {
                                    await request({
                                        url: `/space/${current.hash_id}/member/${record.id}`,
                                        method: 'PUT',
                                        data: {
                                            access_level: eventKey
                                        }
                                    });
                                    action.reload();
                                }
                            }}>
                                <Dropdown.Toggle size={'sm'} variant={'light'}>{record.pivot.role}</Dropdown.Toggle>
                                <Dropdown.Menu>
                                    {Object.entries(ROLES).map(([level, role]) => {
                                        if (current.pivot.access_level <= Number(level)) return null;
                                        return <Dropdown.Item key={level} eventKey={level}>
                                            <div className={'py-2'}>
                                                <h6>{role['name']}</h6>
                                                <div className={'text-muted fs-7'}>{role['description']}</div>
                                            </div>
                                        </Dropdown.Item>;
                                    })}
                                </Dropdown.Menu>
                            </Dropdown>;
                        }
                        return record.pivot.role;
                    }
                },
                {
                    title: '加入时间',
                    dataIndex: ['pivot', 'create_time'],
                    width: 150,
                },
                {
                    key: 'action',
                    title: '操作',
                    width: 120,
                    align: 'right',
                    render({ record, action }) {
                        return <Space>
                            {
                                record.id === user.id && current.pivot.access_level != OWNER && <RequestButton
                                    method={'delete'}
                                    url={`/space/${current.hash_id}/member/${record.id}`}
                                    onSuccess={async () => {
                                        await refresh();
                                        navigate('/');
                                    }}
                                    confirm={'确定要退出吗？'}
                                >
                                    退出
                                </RequestButton>
                            }
                            {current.pivot.access_level >= MASTER && current.pivot.access_level > record.pivot.access_level &&
                                <RequestButton
                                    method={'delete'}
                                    url={`/space/${current.hash_id}/member/${record.id}`}
                                    onSuccess={action.reload}
                                    confirm={'确定要删除吗?'}
                                >
                                    删除
                                </RequestButton>}
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
};
