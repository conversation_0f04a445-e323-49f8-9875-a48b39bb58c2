import { MEMBER, ROL<PERSON> } from '@/utils/constants';
import { MessageProps, request, Toast } from '@topthink/common';
import copy from 'copy-to-clipboard';
import { useState } from 'react';
import { Button, Dropdown, Form, InputGroup, Modal } from 'react-bootstrap';

export default function InviteModal({ state, message: [space, { code }] }: MessageProps<[Space, {
    code: string
}]>) {
    const url = location.origin + `/invite/${code}`;
    const [role, setRole] = useState(MEMBER);

    return <Modal {...state}>
        <Modal.Header>
            <Modal.Title as={'h5'}>邀请新成员</Modal.Title>
        </Modal.Header>
        <Modal.Body className={'p-4'}>
            <Form.Label column={false}>成员通过链接注册，可加入空间<span className='text-muted'>（链接24小时有效）</span></Form.Label>
            <InputGroup>
                <Form.Control defaultValue={url} readOnly />
                <Dropdown onSelect={(key) => {
                    if (key) {
                        const level = Number(key);
                        setRole(level);
                        request({
                            url: `/space/${space.hash_id}/member/invite`,
                            method: 'PUT',
                            data: {
                                code,
                                access_level: level
                            }
                        });
                    }
                }}>
                    <Dropdown.Toggle variant={'light'}>{ROLES[role as keyof typeof ROLES]['name']}</Dropdown.Toggle>
                    <Dropdown.Menu>
                        {Object.entries(ROLES).map(([level, role]) => {
                            if (space.pivot.access_level <= Number(level)) return null;
                            return <Dropdown.Item key={level} eventKey={level}>
                                <div className={'py-1'}>
                                    <h6>{role['name']}</h6>
                                    <div className={'text-muted fs-7'}>{role['description']}</div>
                                </div>
                            </Dropdown.Item>;
                        })}
                    </Dropdown.Menu>
                </Dropdown>
                <Button onClick={() => {
                    copy(url);
                    Toast.success('复制成功');
                }}>复制链接</Button>
            </InputGroup>
        </Modal.Body>
    </Modal>;
}
