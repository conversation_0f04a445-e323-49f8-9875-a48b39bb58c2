import { request, styled, useUser } from '@topthink/common';
import { ChangeEvent, useCallback, useRef } from 'react';
import getUserAvatar from '@/utils/get-user-avatar';


export default function Avatar() {
    const { user, refresh } = useUser();
    const fileRef = useRef<HTMLInputElement>(null);

    const handleFileChange = useCallback(async (e: ChangeEvent<HTMLInputElement>) => {
        e.preventDefault();

        const { files } = e.target;
        if (files && files.length > 0) {
            const data = new FormData();
            data.set('avatar', files[0]);
            await request({
                method: 'POST',
                url: '/user/avatar',
                data
            });
            refresh();
        }
        e.target.value = '';
    }, []);

    return <Container>
        <ImageContainer>
            <img src={getUserAvatar(user)} />
            <input onChange={handleFileChange} type='file' ref={fileRef} accept='image/*' />
            <EditButton onClick={() => fileRef.current?.click()}>修改</EditButton>
        </ImageContainer>
    </Container>;
}

const EditButton = styled.div`
    width: 100%;
    height: 31px;
    opacity: 0.75;
    background: rgb(24, 24, 24);
    position: absolute;
    bottom: 0px;
    font-size: 12px;
    color: rgb(255, 255, 255);
    text-align: center;
    line-height: 31px;
    cursor: pointer;
`;

const Container = styled.div`
    padding: 1rem;
    margin-right: 1rem;
`;

const ImageContainer = styled.div`
    border-radius: 50%;
    height: 116px;
    width: 116px;
    overflow: hidden;
    position: relative;

    img {
        width: 100%;
        height: 100%;
    }
`;
