import { FormSchema, FormUiSchema, ModalForm, useUser } from '@topthink/common';


export default function NameModal() {
    const { refresh } = useUser();
    const schema: FormSchema = {
        type: 'object',
        required: [
            'name',
        ],
        properties: {
            name: {
                title: '昵称',
                type: 'string',
            },
        }
    };

    const uiSchema: FormUiSchema = {
        name: {
            'ui:placeholder': '输入新的昵称',
            'ui:options': {
                label: false
            }
        },
    };

    return <ModalForm
        buttonProps={{
            variant: 'link',
        }}
        action={`/user/name`}
        method={'post'}
        schema={schema}
        uiSchema={uiSchema}
        text={'修改昵称'}
        onSuccess={() => {
            refresh();
        }}
    />;
}
