import { ModalButton, useUser } from '@topthink/common';
import { Dropdown } from 'react-bootstrap';
import Avatar from './avatar';
import NameModal from './name-modal';

export default function UserSetting() {
    const [user] = useUser();
    return <ModalButton
        modalProps={{ size: 'lg', footer: false, header: '账号设置' }}
        as={Dropdown.Item} text={<><i className='bi bi-person-circle me-2' />账号设置</>}
    >
        <div className='d-flex align-items-start'>
            <Avatar />
            <table className='table table-borderless mb-0'>
                <tbody>
                <tr>
                    <td width='100'>UID :</td>
                    <td width='200'>{user.id}</td>
                </tr>
                <tr>
                    <td width='100'>昵称 :</td>
                    <td width='200'>{user.name}</td>
                    <td>
                        <NameModal />
                    </td>
                </tr>
                <tr>
                    <td width='100'>注册时间 :</td>
                    <td width='200'>{user.create_time}</td>
                </tr>
                </tbody>
            </table>
        </div>
    </ModalButton>;
}
