import { Content, Loader, request, styled, useRequest, useUser } from '@topthink/common';
import { useBot } from '@/pages/bot/provider';
import { Col, Row } from 'react-bootstrap';
import { MessageBox } from '@topthink/chat';

export const Component = () => {
    const { space, current } = useBot();
    const { user } = useUser();
    const { feature } = current;

    const { result } = useRequest(`/space/${space.hash_id}/bot/${current.hash_id}/chat`, {
        setResult: ({ conversation, token }, state) => {
            const req = request.create({
                baseURL: 'chat',
                headers: {
                    'Authorization': `Token ${token}`
                }
            });

            return {
                ...state,
                result: {
                    conversation,
                    req
                },
            };
        }
    });

    if (result === undefined) {
        return <Loader />;
    }

    const { conversation, req } = result;

    return <Content
        showBack={`/space/${space.hash_id}/bot`}
        header={<div className='d-flex align-items-center gap-3 overflow-hidden'>
            <img width={40} height={40} className={'rounded-circle'} src={current.avatar} />
            <div className={'text-truncate fs-4'}>{current.name}</div>
        </div>}
    >
        <Row>
            <Col xs={12} xl={{ offset: 1, span: 10 }} xxl={{ offset: 2, span: 8 }}>
                <Container>
                    <div className='h-100 shadow-sm bg-white p-3 d-flex flex-column overflow-hidden rounded'>
                        <StyledMessageBox
                            bot={current}
                            user={user}
                            speech={feature.output.speech.enable ? feature.output.speech : undefined}
                            logLevel={'stats'}
                            request={req}
                            onboarding={feature.onboarding}
                            conversation={conversation}
                            input={{
                                autoFocus: true,
                                minRows: 3,
                                variables: {
                                    config: feature.variable.variables,
                                },
                                fileTypes: feature.input.file.enable ? (feature.input.file.types || []) : undefined,
                                speech: feature.input.speech.enable ? feature.input.speech : undefined,
                                suggestion: feature.suggestion.enable,
                                toolbar: true
                            }}
                        />
                    </div>
                </Container>
            </Col>
        </Row>
    </Content>;
};

const Container = styled.div`
    height: calc(var(--100vh, 100vh) - 65px - 48px);
    margin-bottom: 0 !important;

    @media (max-width: 1540px) {
        height: calc(var(--100vh, 100vh) - 65px - 2rem);
    }
`;

const StyledMessageBox = styled(MessageBox)`
    flex: 1;
    overflow: hidden;
    margin: -.75rem -1rem -1rem;
`;
