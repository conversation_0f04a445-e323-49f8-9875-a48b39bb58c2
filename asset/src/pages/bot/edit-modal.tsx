import { ModalForm, styled } from '@topthink/common';
import { useCurrentSpace } from '@/components/space-provider';
import { ReactNode } from 'react';

interface Props {
    bot: Bot;
    onSuccess: (data: Bot) => void;
    children: ReactNode;
    as?: any;
}

export default function EditBotModal({ bot, children, onSuccess, as }: Props) {
    const { current } = useCurrentSpace();
    return <ModalForm
        action={`/space/${current.hash_id}/bot/${bot.hash_id}`}
        method={'put'}
        buttonProps={{ variant: 'light', size: 'sm', as }}
        modalProps={{ header: '基本信息' }}
        text={children}
        formData={{
            avatar: bot.avatar,
            name: bot.name,
            description: bot.description
        }}
        schema={{
            type: 'object',
            properties: {
                avatar: {
                    type: 'string',
                    title: '头像'
                },
                name: {
                    type: 'string',
                    title: '名称'
                },
                description: {
                    type: 'string',
                    title: '描述'
                },

            },
            required: ['name']
        }}
        uiSchema={{
            avatar: {
                'ui:widget': 'avatar',
                'ui:options': {
                    as: Avatar,
                    endpoint: '/upload/avatar',
                    label: false
                }
            },
            name: {
                'ui:autofocus': true,
                'ui:placeholder': '输入智能体名称'
            },
            description: {
                'ui:widget': 'textarea',
                'ui:placeholder': '输入智能体描述',
                'ui:options': {
                    rows: 3
                }
            }
        }}
        onSuccess={onSuccess}
    />;
}

const Avatar = styled.div`
    margin: 0 auto;
`;
