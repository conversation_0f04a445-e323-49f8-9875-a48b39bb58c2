import { ModalForm, ModalFormProps } from '@topthink/common';

interface Props extends Pick<ModalFormProps, 'onSuccess' | 'action' | 'text' | 'method' | 'formData' | 'buttonProps' | 'modalProps'> {
}

export default function Form(props: Props) {
    return <ModalForm
        schema={{
            type: 'object',
            properties: {
                question: {
                    type: 'string',
                    title: '问题'
                },
                answer: {
                    type: 'string',
                    title: '回复'
                },
                message_id: {
                    type: 'string',
                    title: '消息ID'
                }
            }
        }}
        uiSchema={{
            question: {
                'ui:widget': 'textarea',
                'ui:options': {
                    rows: 3
                }
            },
            answer: {
                'ui:widget': 'textarea',
                'ui:options': {
                    rows: 8
                }
            },
            message_id: {
                'ui:widget': 'hidden'
            }
        }}
        omitExtraData={true}
        {...props}
    />;
}
