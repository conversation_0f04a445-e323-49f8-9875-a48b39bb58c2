import { Card, Content, InfiniteScroller, InfiniteScrollerType, RequestButton, Result, Space } from '@topthink/common';
import { useBot } from '@/pages/bot/provider';
import { Col, Row } from 'react-bootstrap';
import PartItem from '@/components/part-item';
import { useRef } from 'react';
import { ActionIcon, RemoveIcon } from '@/components/action';
import Form from './form';

export const Component = () => {
    const { current, space } = useBot();
    const ref = useRef<InfiniteScrollerType>(null);

    return <Content extra={<Space>
        <Form
            text={'添加标注'}
            action={`/space/${space.hash_id}/bot/${current.hash_id}/annotation`}
            method={'POST'}
            onSuccess={() => {
                ref.current?.reload();
            }}
        />
    </Space>}>
        <InfiniteScroller
            ref={ref}
            source={`/space/${space.hash_id}/bot/${current.hash_id}/annotation`}
            render={({ data, setData }) => {
                return <>
                    <Row className={'g-4'}>
                        {data.map((annotation, index) => {
                            return <Col md={4} key={index + 1}>
                                <PartItem index={index} action={<>
                                    <Form
                                        text={<i className='bi bi-pencil' />}
                                        buttonProps={{ as: ActionIcon, className: 'me-2' }}
                                        modalProps={{ header: '编辑标注' }}
                                        action={`/space/${space.hash_id}/bot/${current.hash_id}/annotation/${annotation.id}`}
                                        method={'PUT'}
                                        formData={annotation}
                                        onSuccess={(ann) => {
                                            setData(draft => {
                                                const i = draft.find(t => t.id === ann.id);
                                                if (i) {
                                                    i.question = ann.question;
                                                    i.answer = ann.answer;
                                                }
                                            });
                                        }}
                                    />
                                    <RequestButton
                                        as={RemoveIcon}
                                        confirm={'确定要删除吗？'}
                                        url={`/space/${space.hash_id}/bot/${current.hash_id}/annotation/${annotation.id}`}
                                        method={'delete'}
                                        onSuccess={() => {
                                            setData(draft => {
                                                const index = draft.findIndex(t => t.id === annotation.id);
                                                if (index !== -1) draft.splice(index, 1);
                                            });
                                        }}
                                    />
                                </>}>
                                    <h4 className='mb-3'>{annotation.question}</h4>
                                    {annotation.answer}
                                </PartItem>
                            </Col>;
                        })}
                    </Row>
                    {data.length == 0 && <Card className={'mt-3'}>
                        <Result title={'暂无标注'} />
                    </Card>}
                </>;
            }}
        />
    </Content>;
};
