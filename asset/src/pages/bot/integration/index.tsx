import { <PERSON>, Content, LinkButton, styled } from '@topthink/common';
import { Badge, Col, Row } from 'react-bootstrap';
import { ReactComponent as ApiIcon } from '../../../images/integration/api.svg';
import { ReactComponent as DingIcon } from '../../../images/integration/ding.svg';
import { ReactComponent as WkfIcon } from '../../../images/integration/wkf.svg';
import { ReactComponent as WmpIcon } from '../../../images/integration/wmp.svg';
import { ReactComponent as LarkIcon } from '../../../images/integration/lark.svg';
import { ReactComponent as QQIcon } from '../../../images/integration/qq.svg';
import Switch, {
    INTEGRATION_API,
    INTEGRATION_EMBED,
    INTEGRATION_QQ,
    INTEGRATION_SHARE,
    INTEGRATION_WKF,
    INTEGRATION_WMP
} from './switch';

export const Component = function() {
    return <Content>
        <Container md={3} className='g-3'>
            <Col className='d-flex'>
                <Card className='flex-fill'>
                    <Title>
                        <Icon><ApiIcon /></Icon>
                        <Name>API</Name>
                        <Switch integration={INTEGRATION_API} />
                    </Title>
                    <Desc>通过 API 使用智能体的各项能力</Desc>
                    <LinkButton variant={'link'} to={'key'}>管理<i className='bi bi-arrow-right ms-1' /></LinkButton>
                </Card>
            </Col>
            <Col className='d-flex'>
                <Card className='flex-fill'>
                    <Title>
                        <Icon><i className='bi bi-share-fill' /></Icon>
                        <Name>分享</Name>
                        <Switch integration={INTEGRATION_SHARE} />
                    </Title>
                    <Desc>生成智能体分享链接，并分享给任何人使用。</Desc>
                    <LinkButton variant={'link'} to={'share'}>管理<i className='bi bi-arrow-right ms-1' /></LinkButton>
                </Card>
            </Col>
            <Col className='d-flex'>
                <Card className='flex-fill'>
                    <Title>
                        <Icon><i className='bi bi-window-desktop' /></Icon>
                        <Name>嵌入</Name>
                        <Switch integration={INTEGRATION_EMBED} />
                    </Title>
                    <Desc>以 iframe 或气泡小部件的形式，将智能体对话界面嵌入至其他网页或应用内。</Desc>
                    <LinkButton variant={'link'} to={`embed`}>管理<i className='bi bi-arrow-right ms-1' /></LinkButton>
                </Card>
            </Col>
            <Col className='d-flex'>
                <Card className='flex-fill'>
                    <Title>
                        <Icon><WkfIcon /></Icon>
                        <Name>微信客服</Name>
                        <Switch integration={INTEGRATION_WKF} />
                    </Title>
                    <Desc>在微信中使用</Desc>
                    <LinkButton variant={'link'} to={'wkf'}>管理<i className='bi bi-arrow-right ms-1' /></LinkButton>
                </Card>
            </Col>
            <Col className='d-flex'>
                <Card className='flex-fill'>
                    <Title>
                        <Icon><WmpIcon /></Icon>
                        <Name>微信公众号</Name>
                        <Switch integration={INTEGRATION_WMP} />
                    </Title>
                    <Desc>在微信公众号中使用</Desc>
                    <LinkButton variant={'link'} to={'wmp'}>管理<i className='bi bi-arrow-right ms-1' /></LinkButton>
                </Card>
            </Col>
            <Col className='d-flex'>
                <Card className='flex-fill'>
                    <Title>
                        <Icon><QQIcon /></Icon>
                        <Name>QQ机器人</Name>
                        <Switch integration={INTEGRATION_QQ} />
                    </Title>
                    <Desc>在QQ中使用</Desc>
                    <LinkButton variant={'link'} to={'qq'}>管理<i className='bi bi-arrow-right ms-1' /></LinkButton>
                </Card>
            </Col>
            <Col className='d-flex'>
                <Card className='flex-fill'>
                    <Title>
                        <Icon><DingIcon /></Icon>
                        <Name>钉钉机器人</Name>
                        <Badge bg={'secondary'}>开发中</Badge>
                    </Title>
                    <Desc>将智能体集成至钉钉机器人。钉钉用户可通过单聊或群聊与智能体对话。</Desc>
                    <LinkButton variant={'link'} disabled to={'ding'}>管理<i className='bi bi-arrow-right ms-1' /></LinkButton>
                </Card>
            </Col>
            <Col className='d-flex'>
                <Card className='flex-fill'>
                    <Title>
                        <Icon><LarkIcon /></Icon>
                        <Name>飞书机器人</Name>
                        <Badge bg={'secondary'}>开发中</Badge>
                    </Title>
                    <Desc>在飞书中使用</Desc>
                    <LinkButton variant={'link'} disabled to={'lark'}>管理<i className='bi bi-arrow-right ms-1' /></LinkButton>
                </Card>
            </Col>
        </Container>
    </Content>;
};

const Desc = styled.div`
    height: calc(2rem * 1.5);
    overflow: hidden;
    color: var(--bs-secondary);
    margin-bottom: 1rem;
`;

const Name = styled.div`
    font-size: 1.25rem;
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
`;

const Icon = styled.div`
    width: 2.5rem;
    height: 2.5rem;
    border-radius: .5rem;
    overflow: hidden;
    background: var(--bs-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFFFFF;

    svg {
        width: 1.3rem;
        height: 1.3rem;
    }

    .bi {
        font-size: 1.2rem;
    }
`;

const Title = styled.div`
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
`;

const Container = styled(Row)`
    .card {
        margin-bottom: 0 !important;
    }
`;
