import {
    Card,
    Clipboard,
    Content,
    css,
    Form,
    getAbsoluteUrl,
    Loader,
    styled,
    Toast,
    useRequest
} from '@topthink/common';
import { useState } from 'react';
import IframeSrc from '../../../images/embed/iframe.svg';
import ScriptSrc from '../../../images/embed/script.svg';
import { useBot } from '@/pages/bot/provider';
import Switch, { INTEGRATION_EMBED } from '@/pages/bot/integration/switch';

export const Component = function() {
    const { current, space } = useBot();
    const [type, setType] = useState('iframe');

    const iframeContent = `<iframe src='${getAbsoluteUrl(`/chat/${current.hash_id}`)}' style='width: 100%; height: 100%; min-height: 700px' frameborder='0' allow='microphone'></iframe>`;
    const scriptContent = `<script src="${getAbsoluteUrl(`/asset/embed.min.js`)}" data-id="${current.hash_id}" defer></script>`;

    const { result } = useRequest(`/space/${space.hash_id}/bot/${current.hash_id}/integration/embed`);

    return <Content showBack extra={<Switch integration={INTEGRATION_EMBED} />}>
        <Card title='选择一种方式将对话界面嵌入到你的网站中'>
            <div className={'d-flex mb-3 gap-3'}>
                <IframeIcon onClick={() => setType('iframe')} $selected={type === 'iframe'} />
                <ScriptIcon onClick={() => setType('script')} $selected={type === 'script'} />
            </div>
            {type === 'iframe' && <div className='border rounded bg-light'>
                <div className='d-flex align-items-center p-3 gap-2 justify-content-between'>
                    将以下 iframe 嵌入到你的网站中的目标位置
                    <Clipboard content={iframeContent} />
                </div>
                <div className='border-top p-3'>
                <pre className='mb-0'>
                    <code className={'text-wrap'}>{iframeContent}</code>
                </pre>
                </div>
            </div>}
            {type === 'script' && <div className='border rounded bg-light'>
                <div className='d-flex align-items-center p-3 gap-2 justify-content-between'>
                    将以下代码嵌入到你的网站中
                    <Clipboard content={scriptContent} />
                </div>
                <div className='border-top p-3'>
                <pre className='mb-0'>
                    <code className={'text-wrap'}>{scriptContent}</code>
                </pre>
                </div>
            </div>}
        </Card>
        <Card>
            {result === undefined ? <Loader /> : <Form
                action={`/space/${space.hash_id}/bot/${current.hash_id}/integration/embed`}
                method={'post'}
                submitText={'保存'}
                schema={{
                    type: 'object',
                    properties: {
                        options: {
                            type: 'object',
                            title: '外观设置',
                            properties: {
                                primaryColor: {
                                    type: 'string',
                                    title: '主题色',
                                    default: '#3c60FF'
                                },
                                placeholder: {
                                    type: 'string',
                                    title: '输入框提示文字',
                                }
                            }
                        },
                        domain_whitelist: {
                            type: 'string',
                            title: '域名白名单',
                            description: '多个域名请用英文逗号分隔',
                        },
                    }
                }}
                uiSchema={{
                    domain_whitelist: {
                        'ui:widget': 'textarea'
                    },
                    options: {
                        primaryColor: {
                            'ui:widget': 'color'
                        },
                        placeholder: {
                            'ui:placeholder': '留空则使用系统默认文字'
                        }
                    }
                }}
                formData={result || undefined}
                onSuccess={() => {
                    Toast.success('保存成功');
                }}
            />}
        </Card>
    </Content>;
};

const EmbedIcon = styled.div<{ $selected?: boolean }>`
    width: 188px;
    height: 128px;
    cursor: pointer;
    border-radius: .375rem;
    background-size: auto;
    background-position: 50%;
    background-repeat: no-repeat;
    border-style: solid;
    border-width: 0;

    ${props => props.$selected && css`
        border-width: 1.5px;
        border-color: var(--bs-primary);
    `}
`;

const IframeIcon = styled(EmbedIcon)`
    background-image: url(${IframeSrc});
`;

const ScriptIcon = styled(EmbedIcon)`
    background-image: url(${ScriptSrc});
`;
