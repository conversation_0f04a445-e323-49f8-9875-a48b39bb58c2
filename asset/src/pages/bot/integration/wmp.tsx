import { Button, Card, Content, Form, getAbsoluteUrl, Loader, Toast, useRequest } from '@topthink/common';
import Switch, { INTEGRATION_WMP } from '@/pages/bot/integration/switch';
import { FormControl, FormLabel, FormText, InputGroup } from 'react-bootstrap';
import { useBot } from '@/pages/bot/provider';
import copy from 'copy-to-clipboard';

export const Component = function() {

    const { current, space } = useBot();
    const url = getAbsoluteUrl(`/api/webhook/${current.hash_id}/wmp`);

    const { result: { data, ip } = {} } = useRequest(`/space/${space.hash_id}/bot/${current.hash_id}/integration/wmp`);

    return <Content showBack extra={<Switch integration={INTEGRATION_WMP} />}>
        <Card>
            <div className='mb-3'>
                <FormLabel>服务器地址</FormLabel>
                <InputGroup>
                    <FormControl defaultValue={url} readOnly />
                    <Button variant='outline-secondary' onClick={() => {
                        copy(url);
                        Toast.success('已复制');
                    }}><i className={'bi bi-clipboard'} /></Button>
                </InputGroup>
                <FormText>复制到微信公众号平台“服务器地址(URL)”</FormText>
            </div>
            <div>
                <FormLabel>服务器IP</FormLabel>
                <InputGroup>
                    <FormControl defaultValue={ip} readOnly />
                    <Button variant='outline-secondary' onClick={() => {
                        copy(ip);
                        Toast.success('已复制');
                    }}><i className={'bi bi-clipboard'} /></Button>
                </InputGroup>
                <FormText>复制到微信公众号平台“IP 白名单”</FormText>
            </div>
        </Card>
        <Card>
            {data === undefined ? <Loader wrap /> : <Form
                action={`/space/${space.hash_id}/bot/${current.hash_id}/integration/wmp`}
                method={'post'}
                submitText={'保存'}
                schema={{
                    type: 'object',
                    properties: {
                        token: {
                            type: 'string',
                            title: 'Token',
                            description: '配置过程中，随机获取 Token 和 EncodingAESKey'
                        },
                        aes_key: {
                            type: 'string',
                            title: 'EncodingAESKey'
                        },
                        app_id: {
                            type: 'string',
                            title: 'AppID',
                            description: '在微信公众号平台“公众号开发信息”区域查看'
                        },
                        app_secret: {
                            type: 'string',
                            title: 'AppSecret',
                            description: '在微信公众号平台“公众号开发信息”区域查看'
                        }
                    }
                }}
                formData={data}
                onSuccess={() => {
                    Toast.success('保存成功');
                }}
            />}
        </Card>
    </Content>;
};
