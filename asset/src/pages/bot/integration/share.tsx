import { Button, Content, getAbsoluteUrl, ModalForm, RequestButton, Space, Table, Toast } from '@topthink/common';
import { useBot } from '@/pages/bot/provider';
import copy from 'copy-to-clipboard';
import Switch, { INTEGRATION_SHARE } from '@/pages/bot/integration/switch';

export const Component = function() {
    const { current, space } = useBot();

    return <Content showBack extra={<Switch integration={INTEGRATION_SHARE} />}>
        <Table
            source={`/space/${space.hash_id}/bot/${current.hash_id}/integration/share`}
            toolBarRender={(action) => {
                return <ModalForm
                    action={`/space/${space.hash_id}/bot/${current.hash_id}/integration/share`}
                    text={'创建分享链接'}
                    schema={{
                        type: 'object',
                        properties: {
                            name: {
                                type: 'string',
                                title: '名称'
                            },
                            expire_time: {
                                type: 'string',
                                title: '过期时间',
                                enum: ['1day', '7days', '30days', 'unlimited'],
                                enumNames: ['1天', '7天', '30天', '无限制'],
                                default: '7days'
                            }
                        }
                    }}
                    uiSchema={{
                        expire_time: {
                            'ui:widget': 'radio'
                        }
                    }}
                    onSuccess={action.reload}
                />;
            }}
            columns={[
                {
                    dataIndex: 'name',
                    title: '名称',
                },
                {
                    dataIndex: 'create_time',
                    title: '创建时间',
                    width: 150,
                },
                {
                    dataIndex: 'expire_time',
                    title: '有效期至',
                    width: 150,
                    render({ value }) {
                        return value || '无限制';
                    }
                },
                {
                    dataIndex: 'last_time',
                    title: '最近使用时间',
                    width: 150,
                    render({ value }) {
                        return value || '--';
                    }
                },
                {
                    key: 'action',
                    title: '操作',
                    width: 150,
                    align: 'right',
                    render({ record, action }) {
                        return <Space>
                            <Button onClick={() => {
                                copy(getAbsoluteUrl(`/chat/s-${record.token}`));
                                Toast.success('复制成功');
                            }}>复制链接</Button>
                            <RequestButton method={'delete'} confirm={'确定要删除吗？'} url={`/space/${space.hash_id}/bot/${current.hash_id}/integration/share/${record.id}`} onSuccess={action.reload}>删除</RequestButton>
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
};
