import { Button, Content, ModalForm, RequestButton, Space, Table, Toast } from '@topthink/common';
import { useBot } from '@/pages/bot/provider';
import copy from 'copy-to-clipboard';
import Switch, { INTEGRATION_API } from '@/pages/bot/integration/switch';

export const Component = function() {
    const { current, space } = useBot();

    return <Content showBack extra={<Switch integration={INTEGRATION_API} />}>
        <Table
            source={`/space/${space.hash_id}/bot/${current.hash_id}/integration/key`}
            toolBarRender={(action) => {
                return <ModalForm
                    action={`/space/${space.hash_id}/bot/${current.hash_id}/integration/key`}
                    text={'创建 API Key'}
                    schema={{
                        type: 'object',
                        properties: {
                            name: {
                                type: 'string',
                                title: '名称'
                            },
                        }
                    }}
                    onSuccess={action.reload}
                />;
            }}
            columns={[
                {
                    dataIndex: 'name',
                    title: '名称',
                },
                {
                    dataIndex: 'token',
                    title: 'Key',
                    width: 220,
                    render({ value }) {
                        return <Space>
                            {value}
                            <Button onClick={() => {
                                copy(value);
                                Toast.success('复制成功');
                            }}>复制</Button>
                        </Space>;
                    }
                },
                {
                    dataIndex: 'create_time',
                    title: '创建时间',
                    width: 150,
                },
                {
                    dataIndex: 'last_time',
                    title: '最近使用时间',
                    width: 150,
                    render({ value }) {
                        return value || '--';
                    }
                },
                {
                    key: 'action',
                    title: '操作',
                    width: 150,
                    align: 'right',
                    render({ record, action }) {
                        return <Space>
                            <RequestButton method={'delete'} confirm={'确定要删除吗？'} url={`/space/${space.hash_id}/bot/${current.hash_id}/integration/key/${record.id}`} onSuccess={action.reload}>删除</RequestButton>
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
};
