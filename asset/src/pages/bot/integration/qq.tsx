import { useBot } from '@/pages/bot/provider';
import { Button, Card, Content, Form, getAbsoluteUrl, Loader, Toast, useRequest } from '@topthink/common';
import Switch, { INTEGRATION_QQ } from '@/pages/bot/integration/switch';
import { FormControl, FormLabel, FormText, InputGroup } from 'react-bootstrap';
import copy from 'copy-to-clipboard';

export const Component = () => {
    const { current, space } = useBot();
    const url = getAbsoluteUrl(`/api/webhook/${current.hash_id}/qq`);

    const { result: { data, ip } = {} } = useRequest(`/space/${space.hash_id}/bot/${current.hash_id}/integration/qq`);

    return <Content showBack extra={<Switch integration={INTEGRATION_QQ} />}>
        <Card>
            <div className={'mb-3'}>
                <FormLabel>回调地址</FormLabel>
                <InputGroup>
                    <FormControl defaultValue={url} readOnly />
                    <Button variant='outline-secondary' onClick={() => {
                        copy(url);
                        Toast.success('已复制');
                    }}><i className={'bi bi-clipboard'} /></Button>
                </InputGroup>
                <FormText>复制到QQ机器人平台“开发-&gt;回调配置-&gt;请求地址”</FormText>
            </div>
            <div>
                <FormLabel>服务器IP</FormLabel>
                <InputGroup>
                    <FormControl defaultValue={ip} readOnly />
                    <Button variant='outline-secondary' onClick={() => {
                        copy(ip);
                        Toast.success('已复制');
                    }}><i className={'bi bi-clipboard'} /></Button>
                </InputGroup>
                <FormText>复制到QQ机器人平台“开发-&gt;开发设置-&gt;IP白名单”</FormText>
            </div>
        </Card>
        <Card>
            {data === undefined ? <Loader wrap /> : <Form
                action={`/space/${space.hash_id}/bot/${current.hash_id}/integration/qq`}
                method={'post'}
                submitText={'保存'}
                schema={{
                    type: 'object',
                    properties: {
                        app_id: {
                            type: 'string',
                            title: 'AppID',
                            description: '在QQ机器人平台“开发->开发设置”区域查看'
                        },
                        app_secret: {
                            type: 'string',
                            title: 'AppSecret',
                            description: '在QQ机器人平台“开发->开发设置”区域查看'
                        }
                    }
                }}
                formData={data}
                onSuccess={() => {
                    Toast.success('保存成功');
                }}
            />}
        </Card>
    </Content>;
};
