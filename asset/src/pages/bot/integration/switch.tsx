import { request, styled } from '@topthink/common';
import { Form } from 'react-bootstrap';
import { useBot } from '@/pages/bot/provider';

export const INTEGRATION_API = 1;
export const INTEGRATION_SHARE = 2;
export const INTEGRATION_EMBED = 4;
export const INTEGRATION_WKF = 8;
export const INTEGRATION_WMP = 16;
export const INTEGRATION_CHAT = 32;
export const INTEGRATION_QQ = 64;

interface Props {
    integration?: number;
}

export default function Switch({ integration = 0 }: Props) {
    const { current, space, update } = useBot();

    return <Container>
        <Form.Check
            type='switch'
            checked={!!(current.integration & integration)}
            onChange={async (e) => {
                try {
                    const result = await request({
                        url: `/space/${space.hash_id}/bot/${current.hash_id}`,
                        method: 'put',
                        data: {
                            integration: e.target.checked ? (current.integration | integration) : (current.integration & (~integration))
                        }
                    });
                    update(result);
                } catch {
                }
            }}
        />
    </Container>;
}

const Container = styled.div`
    display: flex;
    align-items: center;

    .form-check-input {
        cursor: pointer;
        width: 2.5em;
        height: 1.2em;
        margin-top: 0;
    }
`;
