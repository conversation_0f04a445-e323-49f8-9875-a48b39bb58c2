import { Button, Card, Content, Form, getAbsoluteUrl, Loader, Toast, useRequest } from '@topthink/common';
import Switch, { INTEGRATION_WKF } from '@/pages/bot/integration/switch';
import { FormControl, FormLabel, FormText, InputGroup } from 'react-bootstrap';
import { useBot } from '@/pages/bot/provider';
import copy from 'copy-to-clipboard';

export const Component = function() {

    const { current, space } = useBot();
    const url = getAbsoluteUrl(`/api/webhook/${current.hash_id}/wkf`);

    const { result: { data } = {} } = useRequest(`/space/${space.hash_id}/bot/${current.hash_id}/integration/wkf`);

    return <Content showBack extra={<Switch integration={INTEGRATION_WKF} />}>
        <Card>
            <FormLabel>回调地址</FormLabel>
            <InputGroup>
                <FormControl defaultValue={url} readOnly />
                <Button variant='outline-secondary' onClick={() => {
                    copy(url);
                    Toast.success('已复制');
                }}><i className={'bi bi-clipboard'} /></Button>
            </InputGroup>
            <FormText>复制到微信客服平台“回调 URL”</FormText>
        </Card>
        <Card>
            {data === undefined ? <Loader wrap /> : <Form
                action={`/space/${space.hash_id}/bot/${current.hash_id}/integration/wkf`}
                method={'post'}
                submitText={'保存'}
                schema={{
                    type: 'object',
                    properties: {
                        token: {
                            type: 'string',
                            title: 'Token',
                            description: '配置过程中，随机获取 Token 和 EncodingAESKey'
                        },
                        aes_key: {
                            type: 'string',
                            title: 'EncodingAESKey'
                        },
                        corp_id: {
                            type: 'string',
                            title: '企业 ID',
                            description: '在微信客服平台“企业信息”复制企业ID'
                        },
                        secret: {
                            type: 'string',
                            title: 'Secret',
                            description: '在微信客服“开发配置”区域查看 Secret'
                        }
                    }
                }}
                formData={data}
                onSuccess={() => {
                    Toast.success('保存成功');
                }}
            />}
        </Card>
    </Content>;
};
