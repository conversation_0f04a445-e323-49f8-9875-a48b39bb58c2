import { MessageBox, MessageBoxType, VariableForm } from '@topthink/chat';
import { Button, Space, styled, Tooltip } from '@topthink/common';
import { Fragment, useRef } from 'react';
import { Dropdown, Stack } from 'react-bootstrap';
import { useBot } from '../../../provider';
import useRequest from '../../components/use-request';
import useVariables from '../../components/use-variables';
import { useFlow } from '../context';
import Pane from '../pane';
import Icon from '../workflow/nodes/icon';
import { isNodeEnum } from '../workflow/utils';

export default function ChatPane() {
    const { setPanel, config } = useFlow();
    const { variable, onboarding, suggestion, output, input } = config.feature;
    const { current } = useBot();
    const box = useRef<MessageBoxType>(null);

    const variables = useVariables(variable.variables);
    const request = useRequest(config);

    const header = <Stack direction='horizontal' className={'flex-fill'}>
        <div className='fs-5 me-auto'>调试与预览</div>
        <Space>
            {variables && <Dropdown>
                <Tooltip tooltip={'设置变量'}>
                    <Dropdown.Toggle className={'no-caret'} variant='light' size='sm'>
                        <i className='bi bi-braces-asterisk' />
                    </Dropdown.Toggle>
                </Tooltip>
                <Variable className={'p-3'}>
                    <VariableForm
                        values={variables.values}
                        variables={variables.config}
                        onChange={({ formData }) => {
                            variables.setValues(formData);
                        }}
                    >
                        <Fragment />
                    </VariableForm>
                </Variable>
            </Dropdown>}
            <Button size='sm' onClick={() => {
                box.current?.reset();
            }} variant={'light'} tooltip={'重新开始'}>
                <i className='bi bi-arrow-clockwise' />
            </Button>
        </Space>
    </Stack>;

    return <Pane header={header} onHide={() => setPanel(null)}>
        <StyledMessagesBox
            bot={current}
            logLevel={'all'}
            onboarding={onboarding}
            ref={box}
            speech={output.speech.enable ? output.speech : undefined}
            request={request}
            nodeIconResolver={(type) => {
                if (isNodeEnum(type)) {
                    return <Icon type={type} bg={false} color />;
                }
                return null;
            }}
            input={{
                variables,
                suggestion: suggestion.enable,
                fileTypes: input.file.enable ? (input.file.types || []) : undefined,
                speech: input.speech.enable ? input.speech : undefined
            }}
        />
    </Pane>;
}

const StyledMessagesBox = styled(MessageBox)`
    flex: 1;
    overflow: hidden;
    margin: -.75rem -1rem -1rem;
`;

const Variable = styled(Dropdown.Menu)`
    width: 300px;
`;
