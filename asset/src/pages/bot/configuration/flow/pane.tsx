import { styled } from '@topthink/common';
import { Accordion, CloseButton } from 'react-bootstrap';
import { createContext, Dispatch, ReactNode, SetStateAction, useCallback, useContext, useState } from 'react';

const Context = createContext<[Set<string>, Dispatch<SetStateAction<Set<string>>>] | undefined>(undefined);

interface Props {
    title?: string;
    header?: ReactNode;
    children: ReactNode;
    onHide: () => void;
}

export default function Pane({ title, header = <h5>{title}</h5>, children, onHide }: Props) {
    const [activeKey, setActiveKey] = useState<Set<string>>(new Set);
    const onSelect = useCallback((key: string | string[] | null | undefined) => {
        if (key && typeof key === 'string') {
            key = [key];
        }
        setActiveKey(new Set(key));
    }, [setActiveKey]);

    return <Context.Provider value={[activeKey, setActiveKey]}>
        <Container activeKey={Array.from(activeKey)} onSelect={onSelect} alwaysOpen>
            <Header>
                {header}
                <CloseButton onClick={onHide} />
            </Header>
            <Body>
                {children}
            </Body>
        </Container>
    </Context.Provider>;
}

export const useKeys = () => {
    const context = useContext(Context);
    if (!context) {
        throw new Error('useKeys must be used within a Panel');
    }
    return context;
};

const Body = styled.div`
    flex-grow: 1;
    overflow-y: auto;
    display: flex;
    gap: .75rem;
    flex-direction: column;
    padding: 0 1rem 1rem;
`;

const Header = styled.div`
    display: flex;
    align-items: center;
    padding: 1rem;
    gap: .5rem;

    h5 {
        line-height: 1.5;
        margin-bottom: 0;
    }

    .btn-close {
        padding: 0.5rem;
        margin: -0.5rem -0.5rem -0.5rem auto;
    }
`;


const Container = styled(Accordion)`
    width: 400px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    background: #FFF;

    .react-flow + & {
        border-left: 1px solid #e3e3e3;
    }
`;
