import { useBot } from '@/pages/bot/provider';
import { Content, RequestButton, styled, Toast } from '@topthink/common';
import { ReactFlowProvider } from '@xyflow/react';
import isEqual from 'lodash/isEqual';
import merge from 'lodash/merge';
import { Stack } from 'react-bootstrap';
import useConfig from '../components/use-config';
import Chat from './chat';
import { FlowProvider } from './context';
import Feature from './feature';
import Workflow from './workflow';

export default function Flow() {
    const { current, update, space } = useBot<BotFlowConfiguration>();

    const [config, updater] = useConfig<BotFlowConfiguration>(() => {
        return merge({
            feature: {
                variable: {
                    variables: []
                },
                onboarding: {
                    enable: false,
                    prologue: '',
                    questions: []
                },
                suggestion: {
                    enable: false
                },
                input: {
                    speech: {
                        enable: false
                    },
                    file: {
                        enable: false,
                    },
                },
                output: {
                    speech: {
                        enable: false
                    },
                }
            },
            nodes: [],
            edges: []
        }, current.config);
    });

    const extra = <Stack direction={'horizontal'} gap={2}>
        <Chat />
        <Feature {...updater('feature')} />
        <a className={'btn btn-light'} target={'_blank'} href={'https://doc.topthink.com/think-bot/flow.html'}><i className='bi bi-question-circle me-2' />帮助</a>
        <div className='d-flex align-items-center'>
            <div className='vr fs-3' />
        </div>
        <RequestButton
            url={{
                method: 'post',
                url: `/space/${space.hash_id}/bot/${current.hash_id}/config`,
                data: {
                    config,
                }
            }}
            onSuccess={() => {
                update({
                    ...current,
                    config
                });
                Toast.success('保存成功');
            }}
            disabled={isEqual(config, current.config)}
            className='px-4'
        >保存</RequestButton>
    </Stack>;

    return <ReactFlowProvider>
        <FlowProvider config={config}>
            <Content bodyAs={Container} extra={extra}>
                <Workflow {...updater()} />
            </Content>
        </FlowProvider>
    </ReactFlowProvider>;
}

const Container = styled.div`
    width: 100%;
    max-width: 100%;
    height: calc(var(--100vh, 100vh) - 65px);
    margin: 0;
    padding: 0;
    display: flex;
`;
