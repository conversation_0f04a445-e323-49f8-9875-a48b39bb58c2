import type { Node as ReactFlowNode, NodeProps as ReactFlowNodeProps, } from '@xyflow/react';
import { ComponentType, ElementType } from 'react';
import Edge from './edge';
import AgentNode from './nodes/agent/node';
import AnswerNode from './nodes/answer/node';
import Data from './nodes/data';
import IntentClassifier from './nodes/intent-classifier/node';
import KnowledgeRetrievalNode from './nodes/knowledge-retrieval/node';
import LLMNode from './nodes/llm/node';
import StartNode from './nodes/start/node';

export enum NodeEnum {
    Start = 'start',
    Answer = 'answer',
    LLM = 'llm',
    Agent = 'agent',
    KnowledgeRetrieval = 'knowledge-retrieval',
    IntentClassifier = 'intent-classifier',
}

export type NodeType<T extends Data = Data> = Omit<ReactFlowNode<T>, 'type'> & {
    type: NodeEnum;
};

export type NodeProps<T extends Data = Data> = ReactFlowNodeProps<NodeType<T>>

export type NodeComponent<T extends Data> = ComponentType<NodeProps<T>> & {
    title: string;
    description: string;
    icon: {
        bg: string;
        Component: ElementType
    };
    Pane?: ElementType;
    Data?: typeof Data;
}

export type NodeTypes = {
    [key in NodeEnum]: NodeComponent<any>;
}

export const nodeTypes: NodeTypes = {
    [NodeEnum.Start]: StartNode,
    [NodeEnum.LLM]: LLMNode,
    [NodeEnum.Agent]: AgentNode,
    [NodeEnum.KnowledgeRetrieval]: KnowledgeRetrievalNode,
    [NodeEnum.IntentClassifier]: IntentClassifier,
    [NodeEnum.Answer]: AnswerNode,
};

export const edgeTypes = {
    default: Edge
};
