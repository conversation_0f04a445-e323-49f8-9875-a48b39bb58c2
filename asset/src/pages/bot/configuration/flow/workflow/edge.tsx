import { BaseEdge, EdgeLabelRenderer, EdgeProps, getBezierPath, useReactFlow } from '@xyflow/react';
import { styled } from '@topthink/common';

export default function Edge(props: EdgeProps) {

    const {
        id,
        sourceX,
        targetX,
        selected,
        markerEnd,
    } = props;

    const { deleteElements } = useReactFlow();

    const [
        edgePath,
        labelX,
        labelY,
    ] = getBezierPath({
        ...props,
        sourceX: sourceX - 8,
        targetX: targetX + 8,
        curvature: 0.16,
    });

    return <>
        <BaseEdge
            id={id}
            path={edgePath}
            style={{
                strokeWidth: 2,
                opacity: 1,
            }}
            markerEnd={markerEnd}
        />
        {selected && <EdgeLabelRenderer>
            <RemoveButton style={{
                transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
            }}>
                <i role={'button'} className={'bi bi-x'} onClick={() => {
                    deleteElements({
                        edges: [{ id }]
                    });
                }} />
            </RemoveButton>
        </EdgeLabelRenderer>}
    </>;
}

const RemoveButton = styled.div`
    position: absolute;
    pointer-events: all;
    transform-origin: center;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    background: rgba(173, 181, 189, 0.8);
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFF;
    transition: all 0.2s ease;
    cursor: pointer;

    &:hover {
        background: #dc3545;
        transform: scale(1.1);
    }

    .bi {
        line-height: 1;
    }
`;
