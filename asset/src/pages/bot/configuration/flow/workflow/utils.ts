import { Edge, Node, Position, useEdges, useNodes, useReactFlow } from '@xyflow/react';
import { ReactFlowJsonObject } from '@xyflow/react/dist/esm/types/instance';
import { cloneDeep, cloneDeepWith, toPlainObject } from 'lodash';
import { customAlphabet } from 'nanoid';
import { useCallback, useEffect, useRef, useState } from 'react';
import { START_INITIAL_POSITION } from './constants';
import Data from './nodes/data';
import BaseData from './nodes/data';
import { NodeEnum, NodeType, nodeTypes } from './types';

export function isNodeEnum(type?: string): type is NodeEnum {
    return !!type && (type in nodeTypes);
}

export function isNodeType(node?: Node): node is NodeType {
    return isNodeEnum(node?.type);
}

export const getNanoid = (size = 12) => {
    const firstChar = customAlphabet('abcdefghijklmnopqrstuvwxyz', 1)();

    if (size === 1) return firstChar;

    const randomsStr = customAlphabet(
        'abcdefghijklmnopqrstuvwxyz1234567890',
        size - 1
    )();

    return `${firstChar}${randomsStr}`;
};

export function generateNewNode({ position, id, zIndex, type, ...rest }: Omit<NodeType, 'id' | 'data'> & {
    id?: string;
    title?: string;
}): NodeType {
    const { Data = BaseData, title } = nodeTypes[type];
    const data = new Data(rest.title || title);

    return {
        id: id || getNanoid(8),
        type: type,
        data,
        position,
        targetPosition: Position.Left,
        sourcePosition: Position.Right,
        zIndex,
        ...rest,
    };
}

export function initialNodesData(nodes: NodeType[]) {
    nodes.forEach(function(node) {
        const { Data = BaseData } = nodeTypes[node.type];
        const data = new Data();
        Object.assign(data, node.data);
        data.validate();
        node.data = data;
    });
    return nodes;
}

export function initialNodes(nodes: NodeType[]) {
    if (nodes.length === 0) {
        // Add a start node if there is no node
        nodes.push(generateNewNode({
            id: NodeEnum.Start,
            type: NodeEnum.Start,
            position: START_INITIAL_POSITION,
            deletable: false
        }));
    } else {
        nodes.forEach(function(node) {
            if (node.type === NodeEnum.Start) {
                node.id = NodeEnum.Start;
                node.deletable = false;
            }
            if (node.selected) {
                delete node.selected;
            }
        });
        initialNodesData(nodes);
    }

    return nodes;
}

export const getLayoutByDagre = async (nodes: Node[], edges: Edge[]) => {
    const dagre = await import('@dagrejs/dagre');
    const dagreGraph = new dagre.graphlib.Graph();
    dagreGraph.setDefaultEdgeLabel(() => ({}));

    dagreGraph.setGraph({
        rankdir: 'LR',
        align: 'UL',
        nodesep: 40,
        ranksep: 60,
        ranker: 'tight-tree',
        marginx: 30,
        marginy: 200,
    });

    const nodesMap: Record<string, Node> = {};

    nodes.forEach((node) => {
        nodesMap[node.id] = node;
    });

    const addNode = (node?: Node) => {
        if (node) {
            dagreGraph.setNode(node.id, {
                width: node.measured?.width,
                height: node.measured?.height,
            });
            delete nodesMap[node.id];
        }
    };

    edges.sort((a, b) => {
        if (a.source === b.source) {
            return Number(a.sourceHandle || 0) - Number(b.sourceHandle || 0);
        } else {
            return a.source.localeCompare(b.source);
        }
    });

    edges.forEach((edge) => {
        dagreGraph.setEdge(edge.source, edge.target);
        const sourceNode = nodesMap[edge.source];
        const targetNode = nodesMap[edge.target];
        addNode(sourceNode);
        addNode(targetNode);
    });

    Object.keys(nodesMap).forEach((key) => {
        addNode(nodesMap[key]);
    });

    dagre.layout(dagreGraph);

    return dagreGraph;
};

function useHotkeys(keys: { [key: string]: () => void }) {
    useEffect(() => {
        const handler = (e: KeyboardEvent) => {
            if (e.ctrlKey && e.key === 'z' && !e.shiftKey && keys['ctrl+z']) {
                e.preventDefault();
                keys['ctrl+z']();
            }
            if (((e.ctrlKey && e.key === 'y') || (e.ctrlKey && e.shiftKey && e.key === 'Z')) && keys['ctrl+y']) {
                e.preventDefault();
                keys['ctrl+y']();
            }
        };

        document.addEventListener('keydown', handler);
        return () => {
            document.removeEventListener('keydown', handler);
        };
    }, [keys]);
}

export function useHistory() {
    const MAX_HISTORY_LENGTH = 50;
    const nodes = useNodes();
    const edges = useEdges();
    const { toObject, setNodes, setEdges } = useReactFlow();
    const [past, setPast] = useState<Pick<ReactFlowJsonObject<NodeType>, 'nodes' | 'edges'>[]>([]);
    const [future, setFuture] = useState<Pick<ReactFlowJsonObject<NodeType>, 'nodes' | 'edges'>[]>([]);
    const isHistoryAction = useRef(false);
    const timeoutRef = useRef<number>();

    useEffect(() => {
        if (isHistoryAction.current) {
            isHistoryAction.current = false;
            return;
        }

        if (timeoutRef.current) {
            window.clearTimeout(timeoutRef.current);
        }

        timeoutRef.current = window.setTimeout(() => {
            const { nodes, edges } = toObject();
            setPast(past => {
                const lastState = past[past.length - 1];

                const pastNodes = cloneDeepWith(nodes, value => {
                    if (value instanceof Data) {
                        return cloneDeep(toPlainObject(value));
                    }
                });

                const pastEdges = cloneDeep(edges);

                if (lastState &&
                    JSON.stringify(lastState.nodes) === JSON.stringify(pastNodes) &&
                    JSON.stringify(lastState.edges) === JSON.stringify(pastEdges)
                ) {
                    return past;
                }

                // Keep history within the maximum length
                const newPast = [...past, { nodes: pastNodes, edges: pastEdges }];
                if (newPast.length > MAX_HISTORY_LENGTH) {
                    return newPast.slice(-MAX_HISTORY_LENGTH);
                }
                return newPast;
            });
            if (!isHistoryAction.current) {
                setFuture([]);
            }
        }, 300);

        return () => {
            if (timeoutRef.current) {
                window.clearTimeout(timeoutRef.current);
            }
        };
    }, [nodes, edges]);

    const updateState = useCallback((nodes: NodeType[], edges: Edge[]) => {
        setNodes(initialNodesData(cloneDeep(nodes)));
        setEdges(cloneDeep(edges));
    }, []);

    const undo = useCallback(() => {
        if (past.length <= 1) return;

        isHistoryAction.current = true;
        const previous = past[past.length - 2];
        const current = past[past.length - 1];

        setPast(past.slice(0, -1));
        setFuture([current, ...future]);

        updateState(previous.nodes, previous.edges);
    }, [past, future]);

    const redo = useCallback(() => {
        if (future.length === 0) return;

        isHistoryAction.current = true;
        const next = future[0];

        setFuture(future.slice(1));
        setPast(past => [...past, next]);

        updateState(next.nodes, next.edges);
    }, [past, future]);

    useHotkeys({
        'ctrl+z': undo,
        'ctrl+y': redo,
    });

    return {
        undo,
        redo,
        canUndo: past.length > 1,
        canRedo: future.length > 0
    };
}
