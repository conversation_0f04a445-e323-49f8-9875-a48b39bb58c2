import { ActionIcon, RemoveIcon } from '@/components/action';
import { produce } from '@topthink/common';
import { Col, FormControl, Row } from 'react-bootstrap';
import { useFlow } from '../../context';
import Panel from '../../panel';
import { NodeType } from '../types';
import VariableSelector from './variable-selector';

interface Props {
    node: NodeType;
}

export default function Input({ node }: Props) {
    const { updateNodeData } = useFlow<NodeType>();
    const { id, data } = node;

    const input = data.getInput();
    if (!input) return null;
    const { properties, additional } = input;

    const action = additional ? <ActionIcon onClick={() => {
        updateNodeData(id, ({ data }) => {
            data.input = {
                ...data.input,
                [data.getAvailableInputKey('key')]: ''
            };

            return data;
        });
    }}>
        <i className='bi bi-plus-circle-fill' />
    </ActionIcon> : null;

    return <Panel title={'输入参数'} action={action}>
        {properties.length > 0 && <Row className={'g-2'}>
            <Col md={12}>
                <Row className={'fs-7 text-muted text-opacity-25'}>
                    <Col md={3}>参数名</Col>
                    <Col md={9}>参数值</Col>
                </Row>
            </Col>
            {properties.map(({ key, title, deletable, placeholder }) => {
                return <Col md={12} key={key} className={'d-flex align-items-center gap-2'}>
                    <Row className={'flex-fill'}>
                        <Col md={3} className={'d-flex gap-1 align-items-center'}>
                            {deletable ? <FormControl size={'sm'} defaultValue={key} onBlur={(e) => {
                                updateNodeData(id, ({ data }) => {
                                    const newKey = e.target.value;
                                    if (newKey && newKey !== key) {
                                        const keys = Object.keys(data.input);
                                        const input: Record<string, string> = {};

                                        keys.forEach((item) => {
                                            if (item === key) {
                                                input[data.getAvailableInputKey(newKey)] = data.input[item];
                                            } else {
                                                input[item] = data.input[item];
                                            }
                                        });

                                        data.input = input;
                                    }

                                    return data;
                                });
                            }} /> : (title || key)}
                        </Col>
                        <Col md={9}>
                            <VariableSelector
                                node={node}
                                placeholder={placeholder}
                                value={data.input[key]}
                                onChange={(value) => {
                                    updateNodeData(id, ({ data }) => {
                                        data.input = {
                                            ...data.input,
                                            [key]: value
                                        };
                                        return data;
                                    });
                                }}
                            />
                        </Col>
                    </Row>
                    {deletable ? <RemoveIcon onClick={() => {
                        updateNodeData(id, ({ data }) => {
                            data.input = produce(data.input, input => {
                                delete input[key];
                            });

                            return data;
                        });
                    }} /> : <RemoveIcon className={'opacity-0'} />}
                </Col>;
            })}
        </Row>}
    </Panel>;
}
