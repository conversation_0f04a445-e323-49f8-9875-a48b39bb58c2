import { Tooltip } from '@topthink/common';
import { ChangeEventHandler, useCallback } from 'react';
import { Col, Form, FormControl, FormGroup, FormLabel, Row, Stack } from 'react-bootstrap';
import { useFlow } from '../../context';
import Panel from '../../panel';
import { NodeType } from '../types';
import Data from '../nodes/data';

interface HistoryData extends Data {
    history: {
        enable: boolean;
        round: number;
    };
}

interface Props {
    node: NodeType<HistoryData>;
}

export default function History({ node }: Props) {
    const { id, data } = node;
    const { updateNodeData } = useFlow<NodeType<HistoryData>>();

    const historyAction = <Form.Check
        className={'fs-6 lh-1'}
        type='switch'
        checked={data.history.enable}
        onChange={(e) => updateNodeData(id, ({ data }) => {
            data.history.enable = e.target.checked;
            return data;
        })}
    />;

    const onHistoryRoundChange = useCallback<ChangeEventHandler<HTMLInputElement>>((e) => {
        updateNodeData(id, ({ data }) => {
            data.history.round = Math.min(Math.max(Number(e.target.value), 0), 30);
            return data;
        });
    }, []);

    return <Panel title={'记忆'} eventKey={'history'} open={data.history.enable} action={historyAction}>
        <FormGroup as={Row} className={'align-items-center'}>
            <FormLabel column={true} md={4}>
                <Stack direction={'horizontal'} gap={2}>
                    上下文轮数
                    <Tooltip tooltip={'设置带入模型上下文的对话历史轮数。轮数越多，多轮对话的相关性越高，但消耗的 Token 也越多'}>
                        <i className='bi bi-info-circle' />
                    </Tooltip>
                </Stack>
            </FormLabel>
            <Col md={8}>
                <Row>
                    <Col md={8} className='d-flex align-items-center'>
                        <Form.Range min={0} max={30} step={1} value={data.history.round} onChange={onHistoryRoundChange} />
                    </Col>
                    <Col md={4}>
                        <FormControl type='number' min={0} max={30} step={1} size={'sm'} value={data.history.round} onChange={onHistoryRoundChange} />
                    </Col>
                </Row>
            </Col>
        </FormGroup>
    </Panel>;
}
