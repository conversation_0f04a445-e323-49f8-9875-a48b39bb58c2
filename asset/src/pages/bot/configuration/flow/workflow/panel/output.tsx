import { Col, Row } from 'react-bootstrap';
import Panel from '../../panel';
import { NodeType } from '../types';
import { styled } from '@topthink/common';

interface Props {
    node: NodeType;
}

export default function Output({ node }: Props) {
    const { data } = node;

    const output = data.getOutput();
    if (!output) return null;

    return <Panel title={'输出参数'}>
        <Row className={'g-2'}>
            {Object.entries(output.properties).map(([key, { type, title }]) => {
                return <Col md={12} key={key}>
                    <Key>{key}<span>{type}</span></Key>
                    <Title>{title}</Title>
                </Col>;
            })}
        </Row>
    </Panel>;
}

const Key = styled.div`
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;

    span {
        font-size: 12px;
        font-weight: 400;
        color: #777777;
    }
`;

const Title = styled.div`
    font-size: 12px;
    font-weight: 400;
    color: #777777;
    letter-spacing: 0em;
    line-height: 24px;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    word-wrap: break-word;
    white-space: normal;
    overflow: hidden;
    text-overflow: ellipsis;
`;
