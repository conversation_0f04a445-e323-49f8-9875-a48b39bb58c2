import { ReactFlowState, useStore } from '@xyflow/react';
import { useMemo } from 'react';
import { NodeEnum, NodeType } from '../types';
import { isNodeType } from '../utils';

function getAncestors(id: string, lookup: ReactFlowState['connectionLookup']): Set<string> {
    const ancestorIds = new Set<string>();

    const connections = lookup.get(`${id}-target`)?.values();
    if (connections) {
        const sourceIds = Array.from(connections).map(connection => connection.source);
        for (const sourceId of sourceIds) {
            ancestorIds.add(sourceId);
        }
        for (const sourceId of sourceIds) {
            const ancestors = getAncestors(sourceId, lookup);
            ancestors.forEach(ancestorId => ancestorIds.add(ancestorId));
        }
    }
    return ancestorIds;
}

export interface Output {
    id: string;
    title: string;
    type: NodeEnum;
    properties: {
        [key: string]: {
            type: string
            title: string
        };
    };
}

export default function useOutputs(node: NodeType): Output[] {
    const { connectionLookup, nodeLookup, edges } = useStore(({ edges, connectionLookup, nodeLookup }) => ({
        connectionLookup,
        nodeLookup,
        edges
    }));

    return useMemo(() => {
        const nodeIds = Array.from(getAncestors(node.id, connectionLookup)).reverse();
        const outputs = [];

        for (const nodeId of nodeIds) {
            const node = nodeLookup.get(nodeId);
            if (isNodeType(node)) {
                const { id, type, data } = node;
                const output = data.getOutput();
                if (output) {
                    outputs.push({
                        id,
                        type,
                        title: data.title,
                        properties: output.properties,
                    });
                }
            }
        }

        return outputs;
    }, [node.id, edges]);
}
