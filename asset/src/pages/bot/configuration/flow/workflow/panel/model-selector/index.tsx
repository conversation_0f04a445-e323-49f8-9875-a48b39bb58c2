import useModels from '@/hooks/use-models';
import { format<PERSON><PERSON><PERSON><PERSON><PERSON>, Space, styled, Tooltip } from '@topthink/common';
import { useCallback, useEffect, useMemo } from 'react';
import { Col, Dropdown, FormGroup, OverlayTrigger, Popover, Row } from 'react-bootstrap';
import Param from './param';
import Panel from '../../../panel';

interface Props {
    value: ModelConfiguration;
    onChange: (fn: (value: ModelConfiguration) => void | ModelConfiguration) => void;
    tool?: boolean;
    vision?: boolean;
}

export default function ModelSelector({ value, onChange, tool = true, vision = true }: Props) {
    const models = useModels<ChatModel>('chat');

    useEffect(() => {
        if (models.length > 0) {
            if (value.name) {
                //更新
                onChange(draft => {
                    const model = models.find((model) => model.code === value.name) || models[0];
                    if (model) {
                        draft.name = model.code;
                        draft.context_tokens = model.params?.context_tokens || 0;
                        draft.tool = model.params?.tool || false;
                        draft.vision = model.params?.vision || false;
                    }
                });
            } else {
                const defaultModel = models[0];
                onChange(draft => {
                    draft.name = defaultModel.code;
                    draft.context_tokens = defaultModel.params?.context_tokens || 0;
                    draft.tool = defaultModel.params?.tool || false;
                    draft.vision = defaultModel.params?.vision || false;
                });
            }
        }
    }, [models]);

    const current = useMemo(() => {
        const name = value.name;
        return models.find((model) => model.code === name);
    }, [models, value]);

    const onModelChange = useCallback((model: ChatModel) => {
        onChange(draft => {
            draft.name = model.code;
            draft.context_tokens = model.params?.context_tokens || 0;
            draft.tool = model.params?.tool || false;
            draft.vision = model.params?.vision || false;
        });
    }, [onChange]);

    const onParamChange = useCallback(function(name: keyof Required<ModelConfiguration>['params']) {
        return (value: number) => {
            onChange(draft => {
                if (!draft.params) {
                    draft.params = {};
                }
                draft.params[name] = value;
            });
        };
    }, [onChange]);

    return <Panel title={'模型'}>
        <Row className={'d-grid'}>
            <Col xs={12} className={'mb-2'}>
                <FormGroup as={Row}>
                    <Col xs={12}>
                        <Dropdown>
                            <Dropdown.Toggle variant={'light'} className={'w-100 d-flex align-items-center justify-content-between'}>
                                <span className='d-flex align-items-center gap-1'>
                                    <i className='bi bi-x-diamond me-1' />
                                    <span className='lh-1'>{current?.label}</span>
                                    {current?.params?.tool && tool && <Tooltip tooltip={'支持工具调用'}>
                                        <Feature><i className='bi bi-tools' /></Feature>
                                    </Tooltip>}
                                    {current?.params?.vision && vision && <Tooltip tooltip={'支持视觉功能'}>
                                        <Feature><i className='bi bi-eye' /></Feature>
                                    </Tooltip>}
                                </span>
                            </Dropdown.Toggle>
                            <Dropdown.Menu className='w-100'>
                                {models.map((model) => {
                                    const popover = (
                                        <Popover id={`popover-${model.code}`}>
                                            <Popover.Body>
                                                <h5>{model.label}</h5>
                                                <Space>
                                                    <Feature>上下文长度：{formatLongNumber(model.params?.context_tokens || 0, 0)}</Feature>
                                                    <Feature>费率：{model.factor}</Feature>
                                                    {model?.params?.tool && tool && <Tooltip tooltip={'支持工具调用'}>
                                                        <Feature><i className='bi bi-tools' /></Feature>
                                                    </Tooltip>}
                                                    {model?.params?.vision && vision && <Tooltip tooltip={'支持视觉'}>
                                                        <Feature><i className='bi bi-eye' /></Feature>
                                                    </Tooltip>}
                                                </Space>
                                            </Popover.Body>
                                        </Popover>
                                    );

                                    return <OverlayTrigger
                                        key={model.code}
                                        delay={{
                                            show: 200,
                                            hide: 0
                                        }}
                                        trigger={['hover', 'focus']}
                                        placement='auto'
                                        overlay={popover}
                                    >
                                        <Dropdown.Item className='d-flex align-items-center' onClick={() => {
                                            onModelChange(model);
                                            return false;
                                        }}>
                                            {model.label}
                                        </Dropdown.Item>
                                    </OverlayTrigger>;
                                })}
                            </Dropdown.Menu>
                        </Dropdown>
                    </Col>
                </FormGroup>
            </Col>
            <Col xs={12}>
                <Param
                    label={'温度'}
                    description={'采样温度，控制输出的随机性，值越大，会使输出更随机，更具创造性；值越小，输出会更加稳定或确定'}
                    value={value.params?.temperature ?? 0.8}
                    onChange={onParamChange('temperature')}
                    min={0}
                    max={1}
                    step={0.1}
                />
            </Col>
        </Row>
    </Panel>;
}


const Feature = styled.div`
    font-size: 10px;
    line-height: 1;
    background-color: hsla(0, 0%, 100%, .48);
    border: 1px solid var(--bs-border-color);
    border-radius: 5px;
    padding-left: .25rem;
    padding-right: .25rem;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgb(107 114 128);
`;
