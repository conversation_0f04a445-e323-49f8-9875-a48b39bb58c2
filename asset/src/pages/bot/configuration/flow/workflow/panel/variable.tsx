import { ActionIcon } from '@/components/action';
import { ReactComponent as Slash } from '@/images/slash.svg';
import { styled, Tooltip } from '@topthink/common';
import { groupBy, keyBy } from 'lodash';
import { Fragment, useCallback, useRef } from 'react';
import { Dropdown } from 'react-bootstrap';
import { createPortal } from 'react-dom';
import { useDeepCompareMemo } from 'use-deep-compare';
import VariableEditor, { MenuRenderFn, VariableEditorType } from '../../../components/variable-editor';
import Panel from '../../panel';
import Icon from '../nodes/icon';
import { NodeType } from '../types';
import useOutputs from './use-outputs';


interface Props {
    node: NodeType;
    title: string;
    value: string;
    onChange: (value: string) => void;
    placeholder: string;
}

export default function Variable({ node, title, value, onChange, placeholder }: Props) {

    const outputs = useOutputs(node);
    const variables = useDeepCompareMemo(() => {
        return outputs.reverse().flatMap(({ properties, ...output }) => {
            return Object.entries(properties).map(([key, { title }]) => {
                return {
                    key: `${output.id}@${key}`,
                    label: `${title}`,
                    node: output,
                    element: <>
                        <Icon type={output.type} bg={false} className={'me-1'} />
                        {output.title}
                        <Slash />
                        {title}
                    </>
                };
            });
        });
    }, [outputs]);

    const menuRenderFn = useCallback<MenuRenderFn>((anchorElementRef, {
        options,
        selectedIndex,
        selectOptionAndCleanUp
    }) => {
        if (anchorElementRef.current == null || options.length === 0) {
            return null;
        }

        const nodes = keyBy(options.map(option => option.node), node => node.id);
        const groups = groupBy(options, option => option.node.id);
        let index = -1;
        const menu = <StyledMenu show>
            {Object.entries(groups).map(([id, options]) => {
                return <Fragment key={id}>
                    <Dropdown.Header className={'d-flex align-items-center ps-2'}>
                        <Icon type={nodes[id].type} bg={false} className={'me-2'} />{nodes[id].title}
                    </Dropdown.Header>
                    {options.map(option => {
                        index++;
                        return <Dropdown.Item
                            key={option.key}
                            active={index === selectedIndex}
                            onClick={() => {
                                selectOptionAndCleanUp(option);
                            }}
                        >{option.label || option.key}</Dropdown.Item>;
                    })}
                </Fragment>;
            })}
        </StyledMenu>;

        return createPortal(menu, anchorElementRef.current);
    }, []);

    const editor = useRef<VariableEditorType>(null);

    const action = <Tooltip tooltip={'插入变量'}>
        <ActionIcon onMouseDown={(e) => {
            e.preventDefault();
            editor.current?.insertVariable();
        }}>
            <i className='bi bi-braces-asterisk' />
        </ActionIcon>
    </Tooltip>;

    return <Panel title={title} action={action}>
        <VariableEditor
            ref={editor}
            variables={variables}
            value={value}
            onChange={onChange}
            placeholder={placeholder}
            menuRenderFn={menuRenderFn}
        />
    </Panel>;
}

const StyledMenu = styled(Dropdown.Menu)`
    max-height: 300px;
    width: 200px;
    overflow-y: auto;

    .dropdown-item {
        padding-left: 1.875rem;
        font-size: 0.9rem;
    }
`;
