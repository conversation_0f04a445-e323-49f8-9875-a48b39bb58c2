import { Col, Form, FormControl, FormGroup, FormLabel, Row, Stack } from 'react-bootstrap';
import { Tooltip } from '@topthink/common';
import { ChangeEvent, useCallback } from 'react';

interface Props {
    label: string;
    description?: string;
    value: number;
    onChange: (value: number) => void;
    min: number;
    max: number;
    step?: number;
}

export default function Param({ label, description, value, onChange, min, max, step = 1 }: Props) {

    const onChangeValue = useCallback((e: ChangeEvent<HTMLInputElement>) => {
        const value = Math.min(Math.max(Number(e.target.value), min), max);
        onChange(value);
    }, [onChange, min, max, step]);

    return <FormGroup as={Row} className={'align-items-center'}>
        <FormLabel column={true} md={4}>
            <Stack direction={'horizontal'} gap={2}>
                {label}
                {description && <Tooltip tooltip={description}>
                    <i className='bi bi-info-circle' />
                </Tooltip>}
            </Stack>
        </FormLabel>
        <Col md={8}>
            <Row>
                <Col md={8} className='d-flex align-items-center'>
                    <Form.Range min={min} max={max} step={step} value={value} onChange={onChangeValue} />
                </Col>
                <Col md={4}>
                    <FormControl type='number' min={min} max={max} step={step} size={'sm'} value={value} onChange={onChangeValue} />
                </Col>
            </Row>
        </Col>
    </FormGroup>;
}
