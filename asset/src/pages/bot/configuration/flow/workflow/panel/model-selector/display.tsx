import useModels from '@/hooks/use-models';
import { styled, Tooltip } from '@topthink/common';
import { useMemo } from 'react';
import PanelItem from '../../../../components/panel-item';

interface Props {
    value?: ModelConfiguration;
    tool?: boolean;
    vision?: boolean;
}

export default function Display({ value, tool = true, vision = true }: Props) {
    const models = useModels<ChatModel>('chat');

    const current = useMemo(() => {
        if (!value) {
            return null;
        }
        const name = value.name;
        return models.find((model) => model.code === name);
    }, [models, value]);

    if (!current) {
        return null;
    }

    return <PanelItem className={'gap-1'}>
        <i className='bi bi-x-diamond me-1' />
        <span className='lh-1'>{current.label}</span>
        {current?.params?.tool && tool && <Tooltip tooltip={'支持工具调用'}>
            <Feature><i className='bi bi-tools' /></Feature>
        </Tooltip>}
        {current?.params?.vision && vision && <Tooltip tooltip={'支持视觉功能'}>
            <Feature><i className='bi bi-eye' /></Feature>
        </Tooltip>}
    </PanelItem>;
}

const Feature = styled.div`
    font-size: 10px;
    line-height: 1;
    background-color: hsla(0, 0%, 100%, .48);
    border: 1px solid var(--bs-border-color);
    border-radius: 5px;
    padding-left: .25rem;
    padding-right: .25rem;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgb(107 114 128);
`;
