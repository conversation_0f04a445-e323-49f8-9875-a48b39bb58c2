import { NodeEnum, NodeType } from '../types';
import useOutputs from './use-outputs';

export interface Variable {
    label: string;
    value: string;
    type: NodeEnum;
    children: {
        label: string;
        value: string;
    }[];
}

export default function useVariables(node: NodeType): Variable[] {

    const outputs = useOutputs(node);

    return outputs.map(variable => {
        return {
            label: variable.title,
            value: variable.id,
            type: variable.type,
            children: Object.entries(variable.properties).map(([name, property]) => {
                return {
                    label: property.title,
                    value: name,
                };
            })
        };
    });
}
