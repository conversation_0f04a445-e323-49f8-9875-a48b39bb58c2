import { ReactComponent as Slash } from '@/images/slash.svg';
import { Cascader, styled } from '@topthink/common';
import { Fragment } from 'react';
import Icon from '../nodes/icon';
import { NodeType } from '../types';
import { isNodeEnum } from '../utils';
import useVariables from './use-variables';

interface Props {
    node: NodeType;
    value?: string;
    onChange?: (value: string) => void;
    placeholder?: string;
}

export default function VariableSelector({ node, value, onChange, placeholder }: Props) {

    const variables = useVariables(node);

    return <StyledCascader
        value={value ? value.split('@') : undefined}
        onChange={(value) => {
            onChange?.(value?.join('@'));
        }}
        dropdownMatchSelectWidth
        expandTrigger={'hover'}
        options={variables}
        size={'sm'}
        allowClear
        placeholder={placeholder ? `请选择，默认为 ${placeholder}` : '请选择'}
        optionRender={(option) => {
            if (isNodeEnum(option.type)) {
                return <>
                    <Icon type={option.type} bg={false} className={'me-1'} />
                    {option.label}
                </>;
            }
            return option.label;
        }}
        displayRender={(_, selectedOptions) => {
            if (!selectedOptions) {
                return null;
            }
            selectedOptions = selectedOptions.filter(Boolean);
            if (selectedOptions.length === 0) {
                return <Label className={'text-danger'}>变量不存在</Label>;
            }
            return <Label>
                {selectedOptions.map((option, index) => {
                    if (isNodeEnum(option.type)) {
                        return <Fragment key={index}>
                            <Icon type={option.type} bg={false} className={'me-1'} />
                            {option.label}
                            <Slash />
                        </Fragment>;
                    } else {
                        return <Fragment key={index}>{option.label}</Fragment>;
                    }
                })}
            </Label>;
        }}
    />;
}

const StyledCascader = styled(Cascader)`
    .rc-cascader-dropdown {
        .rc-cascader-menu {
            width: 50%;

            .rc-cascader-menu-item {
                .rc-cascader-menu-item-content {
                    overflow: hidden;
                    margin-right: .5rem;
                    text-overflow: ellipsis;
                }
            }

            &:first-child {
                .rc-cascader-menu-item {
                    padding-left: 0.5rem;

                    .rc-cascader-menu-item-content {
                        svg {
                            margin-bottom: 3px;
                        }
                    }
                }
            }
        }
    }
`;

const Label = styled.div`
    border-radius: 5px;
    background: var(--bs-gray-200);
    display: flex;
    align-items: center;
    padding: 0 0.375rem;
    height: calc(100% - 0.5rem);
    margin: 0.25rem -0.25rem;
`;
