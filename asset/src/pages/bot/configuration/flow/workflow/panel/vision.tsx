import { Col, Form, FormGroup, FormLabel, Row } from 'react-bootstrap';
import { useFlow } from '../../context';
import Panel from '../../panel';
import Data from '../nodes/data';
import { NodeType } from '../types';
import VariableSelector from './variable-selector';

interface VisionData extends Data {
    model: ModelConfiguration;
    vision: {
        enable: boolean;
        files: string;
    };
}

interface Props {
    node: NodeType<VisionData>;
}

export default function Vision({ node }: Props) {
    const { id, data } = node;
    const { updateNodeData } = useFlow<NodeType<VisionData>>();

    if (!data.model.vision) {
        return null;
    }

    const visionAction = <Form.Check
        className={'fs-6 lh-1'}
        type='switch'
        checked={data.vision.enable}
        onChange={(e) => updateNodeData(id, ({ data }) => {
            data.vision.enable = e.target.checked;
            return data;
        })}
    />;

    return <Panel title={'视觉'} eventKey={'vision'} open={data.vision.enable} action={visionAction}>
        <FormGroup as={Row} className={'align-items-center'}>
            <FormLabel column={true} md={3}>
                图片文件
            </FormLabel>
            <Col md={9}>
                <VariableSelector
                    node={node}
                    placeholder={'上传文件'}
                    value={data.vision.files}
                    onChange={(value) => {
                        updateNodeData(id, ({ data }) => {
                            data.vision.files = value;
                            return data;
                        });
                    }}
                />
            </Col>
        </FormGroup>
    </Panel>;
}
