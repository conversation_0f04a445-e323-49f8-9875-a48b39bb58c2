import { styled } from '@topthink/common';
import { NodeType, nodeTypes } from './types';

interface Props {
    node: NodeType;
}

export default function Pane({ node }: Props) {
    const { type } = node;
    const Component = nodeTypes[type].Pane;
    if (!Component) {
        return null;
    }

    return <Container key={node.id}>
        <Component node={node} />
    </Container>;
}

const Container = styled.div`
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    display: flex;
    z-index: 6;
`;
