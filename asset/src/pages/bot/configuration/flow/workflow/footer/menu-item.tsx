import { NodeEnum, nodeTypes } from '../types';
import { Dropdown } from 'react-bootstrap';
import Icon from '../nodes/icon';
import { generateNewNode } from '../utils';
import { useReactFlow, useStore } from '@xyflow/react';
import { NODE_WIDTH, Y_OFFSET } from '../constants';
import { Tooltip } from '@topthink/common';

interface Props {
    type: NodeEnum;
}

export default function MenuItem({ type }: Props) {
    const { title, description } = nodeTypes[type];
    const { addNodes, getViewport, getNodes, setNodes } = useReactFlow();
    const { width, height } = useStore(state => ({ width: state.width, height: state.height }));

    return <Tooltip tooltip={description} placement={'right'}>
        <Dropdown.Item
            onClick={() => {
                const viewport = getViewport();
                const nodes = getNodes();

                const count = nodes.filter(node => node.type == type).length;

                const node = generateNewNode({
                    type,
                    title: count > 0 ? `${title}#${count + 1}` : title,
                    position: {
                        x: (width / 2 - viewport.x - NODE_WIDTH / 2) / viewport.zoom,
                        y: (height / 2 - viewport.y - Y_OFFSET) / viewport.zoom,
                    }
                });

                addNodes(node);
                setNodes(nodes => nodes.map(n => ({
                    ...n,
                    selected: n.id === node.id
                })));
            }}
            className={'d-flex align-items-center'}
        >
            <Icon type={type} />{title}
        </Dropdown.Item>
    </Tooltip>;
}
