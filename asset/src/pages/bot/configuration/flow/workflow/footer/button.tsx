import { styled } from '@topthink/common';

interface ButtonProps {
    $disabled?: boolean;
}

const Button = styled.div<ButtonProps>`
    padding-left: .375rem;
    padding-right: .375rem;
    border-radius: .375rem;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    width: 2rem;
    height: 2rem;
    display: flex;
    font-size: 1.125rem;

    &:hover {
        background-color: #c8ceda33;
        color: #354052;
    }

    ${props => props.$disabled && `
        cursor: not-allowed;
        background-color: #f5f6f7;
        opacity: 0.6;
        
        &:hover {
            background-color: #f5f6f7;
            color: inherit;
        }
    `}
`;

export default Button;
