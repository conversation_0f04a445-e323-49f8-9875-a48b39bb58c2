import { styled } from '@topthink/common';
import { useReactFlow, Node } from '@xyflow/react';
import { useCallback } from 'react';
import { Dropdown } from 'react-bootstrap';
import { NodeEnum } from '../types';
import { getLayoutByDagre } from '../utils';
import Button from './button';
import Divider from './divider';
import Group from './group';
import MenuItem from './menu-item';

export default function Control() {
    const { setViewport, setNodes, getNodes, getEdges } = useReactFlow();
    const handleLayout = useCallback(async () => {
        const nodes = getNodes();
        const edges = getEdges();
        const layout = await getLayoutByDagre(nodes, edges);

        const rankMap: Record<number, Node> = {};

        nodes.forEach((node) => {
            const rank = layout.node(node.id).rank;
            if (rank !== undefined) {
                if (!rankMap[rank]) {
                    rankMap[rank] = node;
                } else {
                    if (rankMap[rank].position.y > node.position.y)
                        rankMap[rank] = node;
                }
            }
        });

        setNodes(nodes => nodes.map(n => {
            const nodeWithPosition = layout.node(n.id);
            let offset = 0;
            if (nodeWithPosition.rank !== undefined) {
                offset = (rankMap[nodeWithPosition.rank]?.measured?.height || 0) / 2;
            }
            return {
                ...n,
                position: {
                    x: nodeWithPosition.x - (n.measured?.width || 0) / 2,
                    y: nodeWithPosition.y - (n.measured?.height || 0) / 2 + offset,
                }
            };
        }));

        await setViewport({
            x: 0,
            y: 0,
            zoom: 0.7,
        });
    }, [getNodes, getEdges, setNodes, setViewport]);

    return <Group>
        <Dropdown>
            <Dropdown.Toggle className={'no-caret'} as={Button}><i className='bi bi-plus-circle-fill' /></Dropdown.Toggle>
            <StyledMenu>
                <MenuItem type={NodeEnum.LLM} />
                <MenuItem type={NodeEnum.Agent} />
                <MenuItem type={NodeEnum.IntentClassifier} />
                <MenuItem type={NodeEnum.KnowledgeRetrieval} />
                <MenuItem type={NodeEnum.Answer} />
            </StyledMenu>
        </Dropdown>
        <Divider />
        <Button onClick={handleLayout}><i className='bi bi-grid' /></Button>
    </Group>;
}

const StyledMenu = styled(Dropdown.Menu)`
    width: 240px;
`;
