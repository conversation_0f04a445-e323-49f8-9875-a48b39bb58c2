import { useReactFlow, useViewport } from '@xyflow/react';
import { useHistory } from '../utils';
import Button from './button';
import Divider from './divider';
import Group from './group';
import { MouseEvent } from 'react';

export default function Operator() {
    const { zoomIn, zoomOut } = useReactFlow();
    const { zoom } = useViewport();

    const { undo, redo, canUndo, canRedo } = useHistory();

    const handleZoomOut = (e: MouseEvent<HTMLDivElement>) => {
        e.preventDefault();
        if (zoom > 0.5) zoomOut();
    };

    const handleZoomIn = (e: MouseEvent<HTMLDivElement>) => {
        e.preventDefault();
        if (zoom < 2) zoomIn();
    };

    const handleUndo = (e: MouseEvent<HTMLDivElement>) => {
        e.preventDefault();
        if (canUndo) undo();
    };
    const handleRedo = (e: MouseEvent<HTMLDivElement>) => {
        e.preventDefault();
        if (canRedo) redo();
    };

    return <Group>
        <Button onMouseDown={handleZoomOut} $disabled={zoom <= 0.5}><i className={'bi bi-dash-lg'} /></Button>
        <Button onMouseDown={handleZoomIn} $disabled={zoom >= 2}><i className={'bi bi-plus-lg'} /></Button>
        <Divider />
        <Button onMouseDown={handleUndo} $disabled={!canUndo}><i className={'bi bi-arrow-counterclockwise'} /></Button>
        <Button onMouseDown={handleRedo} $disabled={!canRedo}><i className={'bi bi-arrow-clockwise'} /></Button>
    </Group>;
}
