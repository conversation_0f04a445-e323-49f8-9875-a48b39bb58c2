import { styled } from '@topthink/common';
import { ReactNode } from 'react';

interface Props {
    children: ReactNode;
}

export default function Group({ children }: Props) {
    return <Container>
        {children}
    </Container>;
}

const Container = styled.div`
    display: flex;
    box-shadow: var(--bs-box-shadow-sm);
    padding: .25rem;
    gap: .25rem;
    background: #FFFFFF;
    border-radius: var(--bs-border-radius);
    align-items: center;
`;
