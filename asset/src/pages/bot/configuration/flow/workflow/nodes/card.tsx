import { css, styled } from '@topthink/common';
import { ReactNode } from 'react';
import { NodeProps } from '../types';
import Icon from './icon';
import { SourceHandle, TargetHandle } from './handle';
import { useReactFlow } from '@xyflow/react';

interface Props extends NodeProps {
    children?: ReactNode;
    targetable?: boolean;
    sourceable?: boolean;
}

export default function Card(props: Props) {
    const {
        id,
        data,
        type,
        selected,
        targetable = true,
        sourceable = true,
        deletable,
        children
    } = props;

    const { deleteElements } = useReactFlow();

    return <>
        <Container $selected={selected}>
            <Body>
                <Header>
                    <Icon type={type} />
                    <Title>{data.title}</Title>
                    {targetable && <TargetHandle />}
                    {sourceable && <SourceHandle />}
                </Header>
                <Content>
                    {children}
                </Content>
            </Body>
            {selected && <ToolBar>
                {deletable && <Tool onClick={() => {
                    deleteElements({
                        nodes: [{ id }]
                    });
                }}>
                    <i className={'bi bi-trash'} />
                </Tool>}
            </ToolBar>}
        </Container>
    </>;
}

const Tool = styled.div<{ $disabled?: boolean }>`
    align-items: center;
    justify-content: center;
    border-radius: .375rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--bs-secondary);
    display: flex;
    background: #FFF;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    padding: .125rem;
    width: 1.875rem;
    height: 1.875rem;
    overflow: hidden;

    ${props => props.$disabled && css`
        color: var(--bs-gray-400) !important;
    `};

    .bi {
        width: 1.75rem;
        height: 1.75rem;
        border-radius: .375rem;
        line-height: 1.75rem;
        text-align: center;

        ${props => !props.$disabled && css`
            &:hover {
                color: var(--bs-dark);
                background-color: var(--bs-gray-200);
            }
        `}

    }
`;

const ToolBar = styled.div`
    position: absolute;
    right: 0;
    top: -2.125rem;
    display: flex;
    gap: .5rem;
    box-shadow: var(--bs-box-shadow-sm);
`;

const Content = styled.div`
    position: relative;

    &:not(:empty) {
        padding: .25rem .75rem;
        margin-bottom: .5rem;
    }
`;

const Body = styled.div`
    box-shadow: var(--bs-box-shadow-sm);
    background: #FFF;
    width: 240px;
    position: relative;
    border-radius: var(--bs-border-radius-lg);
`;

const Container = styled.div<{ $selected: boolean }>`
    display: flex;
    border-radius: var(--bs-border-radius-lg);
    border: 2px solid transparent;
    ${props => props.$selected && css`
        border-color: var(--bs-primary);
    `};

    &:hover {
        ${Body} {
            box-shadow: var(--bs-box-shadow);
        }
    }
`;

const Header = styled.div`
    padding: .75rem .75rem .75rem;
    display: flex;
    align-items: center;
    position: relative;
`;

const Title = styled.div`
    margin-right: .25rem;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 13px;
    font-weight: 600;
    line-height: 16px;
`;
