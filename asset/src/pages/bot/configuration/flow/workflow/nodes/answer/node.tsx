import { NodeProps } from '../../types';
import Card from '../card';
import Data from './data';
import { ReactComponent as Icon } from './icon.svg';
import Pane from './pane';

const AnswerNode = (props: NodeProps) => {
    return <Card {...props} />;
};

AnswerNode.icon = {
    bg: '#f79009',
    Component: Icon,
};

AnswerNode.title = '回复';
AnswerNode.description = '定义一个聊天对话的回复内容';
AnswerNode.Pane = Pane;
AnswerNode.Data = Data;

export default AnswerNode;
