import { useFlow } from '../../../context';
import Variable from '../../panel/variable';
import { NodeType } from '../../types';
import Pane from '../pane';
import Data from './data';

interface Props {
    node: NodeType<Data>;
}

export default function({ node }: Props) {
    const { id, data } = node;
    const { updateNodeData } = useFlow<NodeType<Data>>();

    return <Pane node={node}>
        <Variable
            node={node}
            title={'回复内容'}
            value={data.content}
            onChange={(value) => updateNodeData(id, ({ data }) => {
                data.content = value;
                return data;
            })}
            placeholder={'输入回复内容'}
        />
    </Pane>;
}
