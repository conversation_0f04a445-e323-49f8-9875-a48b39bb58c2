export interface Input {
    properties?: {
        [key: string]: {
            title: string
            placeholder?: string
        };
    };
    additional?: boolean;
}

export interface Output {
    properties: {
        [key: string]: {
            type: string
            title: string
        };
    };
}

export default class Data implements Record<string, unknown> {
    [x: string]: any;

    static input?: Input;

    static output?: Output;

    input: Record<string, string> = {};

    constructor(public title: string = '') {
    }

    validate() {
        // this.input = {};
    }

    getAvailableInputKey(key: string): string {
        const input = this.getStaticInput();
        if (input?.properties?.hasOwnProperty(key) || this.input.hasOwnProperty(key)) {
            const match = key.match(/^(.*)-(\d+)$/);
            if (match) {
                key = `${match[1]}-${parseInt(match[2]) + 1}`;
            } else {
                key += `-1`;
            }

            return this.getAvailableInputKey(key);
        }
        return key;
    }

    getStaticInput() {
        return (this.constructor as typeof Data).input;
    }

    getInput() {
        const input = this.getStaticInput();
        if (!input) {
            return undefined;
        }
        const properties: {
            key: string;
            title?: string;
            placeholder?: string;
            deletable: boolean;
        }[] = [];

        if (input.properties) {
            Object.entries(input.properties).forEach(([key, value]) => {
                properties.push({
                    key,
                    title: value.title,
                    placeholder: value.placeholder,
                    deletable: false,
                });
            });
        }

        Object.keys(this.input).forEach((key) => {
            if (!input.properties?.hasOwnProperty(key)) {
                properties.push({
                    key,
                    deletable: true,
                });
            }
        });

        return {
            properties,
            additional: !!input?.additional
        };
    }

    getOutput() {
        const { output } = this.constructor as typeof Data;
        return output;
    }

}
