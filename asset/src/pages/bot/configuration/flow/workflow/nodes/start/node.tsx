import Pane from '../pane';
import Card from '../card';
import { NodeProps } from '../../types';
import Data from './data';
import { ReactComponent as Icon } from './icon.svg';

const StartNode = (props: NodeProps) => {
    return <Card {...props} targetable={false} />;
};

StartNode.icon = {
    bg: '#296dff',
    Component: Icon,
};

StartNode.title = '开始';
StartNode.description = '定义一个流程启动的初始参数';
StartNode.Data = Data;
StartNode.Pane = Pane;
export default StartNode;
