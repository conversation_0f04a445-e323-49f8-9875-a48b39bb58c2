import { styled } from '@topthink/common';
import { SourceHandle } from '../handle';

interface Props {
    classes: string[];
}

export default function Classes({ classes }: Props) {

    if (!classes.length) return null;

    return <Container>
        {classes.map((text, index) => {
            return <Item key={index}>
                <Text>{text}</Text>
                <SourceHandle id={String(index)} />
            </Item>;
        })}
    </Container>;
}

const Container = styled.div`
    display: flex;
    gap: 0.25rem;
    flex-direction: column;

    &:not(:empty) {
        margin-top: 0.5rem;
    }
`;

const Text = styled.div`
    background: #f1f3f5;
    font-size: 1rem;
    opacity: 0.65;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
`;

const Item = styled.div`
    position: relative;
    margin: 0 -.75rem;
    padding: 0 .75rem;
`;
