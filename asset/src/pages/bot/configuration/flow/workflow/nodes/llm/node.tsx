import Display from '../../panel/model-selector/display';
import { NodeProps } from '../../types';
import Card from '../card';
import Data from './data';
import { ReactComponent as Icon } from './icon.svg';
import Pane from './pane';

const LLMNode = (props: NodeProps<Data>) => {
    const { data } = props;

    return <Card {...props}>
        <Display value={data.model} tool={false} />
    </Card>;
};

LLMNode.icon = {
    bg: '#6172f3',
    Component: Icon,
};
LLMNode.title = 'LLM';
LLMNode.description = '调用大语言模型回答问题或者对自然语言进行处理';
LLMNode.Pane = Pane;
LLMNode.Data = Data;

export default LLMNode;
