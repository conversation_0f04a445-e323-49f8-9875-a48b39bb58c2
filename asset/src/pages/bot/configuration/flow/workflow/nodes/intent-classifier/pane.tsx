import { RemoveIcon } from '@/components/action';
import { produce, styled } from '@topthink/common';
import { useMemo } from 'react';
import { Col, Row } from 'react-bootstrap';
import { useFlow } from '../../../context';
import Panel from '../../../panel';
import History from '../../panel/history';
import Vision from '../../panel/vision';
import { NodeType } from '../../types';
import Pane from '../pane';
import Data from './data';
import ModelSelector from '../../panel/model-selector';

interface Props {
    node: NodeType<Data>;
}

export default function({ node }: Props) {
    const { id, data } = node;
    const { updateNodeData } = useFlow<NodeType<Data>>();

    const classes = useMemo(() => {
        const classes = [...data.classes];
        if (classes.length === 0 || classes[classes.length - 1]) {
            classes.push('');
        }

        return classes;
    }, [data.classes]);

    return <Pane node={node}>
        <ModelSelector
            tool={false}
            value={data.model}
            onChange={(updater) => {
                updateNodeData(id, ({ data }) => {
                    data.model = produce(data.model, updater);
                    return data;
                });
            }}
        />
        <Panel title={'分类'}>
            <Row className={'g-2'}>
                {classes.map((text, index) => {
                    return <Col md={12} key={index}>
                        <ClassItem>
                            <input
                                value={text}
                                onChange={e => updateNodeData(id, ({ data }) => {
                                    data.classes = produce(data.classes, classes => {
                                        if (e.target.value || index < classes.length - 1) {
                                            classes[index] = e.target.value;
                                        } else {
                                            classes.splice(index, 1);
                                        }
                                    });
                                    return data;
                                })}
                                onBlur={e => {
                                    updateNodeData(id, ({ data }) => {
                                        data.classes = produce(data.classes, classes => {
                                            if (!e.target.value.trim() && index < classes.length - 1) {
                                                classes.splice(index, 1);
                                            }
                                        });
                                        return data;
                                    });
                                }}
                                className='form-control'
                                placeholder='输入意图内容'
                            />
                            {index < classes.length - 1 && <RemoveIcon onClick={() => {
                                updateNodeData(id, ({ data }) => {
                                    data.classes = produce(data.classes, classes => {
                                        classes.splice(index, 1);
                                    });
                                    return data;
                                });
                            }} />}
                        </ClassItem>
                    </Col>;
                })}
            </Row>
        </Panel>
        <History node={node} />
        <Vision node={node} />
    </Pane>;
}

const ClassItem = styled.div`
    position: relative;

    ${RemoveIcon} {
        position: absolute;
        right: .375rem;
        top: 50%;
        transform: translateY(-50%);
        visibility: hidden;
    }

    &:hover {
        ${RemoveIcon} {
            visibility: visible;
        }
    }
`;
