import useAgents from '@/hooks/use-agents';
import PanelItem from '../../../../components/panel-item';
import { NodeProps } from '../../types';
import Card from '../card';
import Data from './data';
import { ReactComponent as Icon } from './icon.svg';
import Pane from './pane';

const AgentNode = (props: NodeProps<Data>) => {
    const { data } = props;
    const agents = useAgents();
    const current = agents.find((v) => v.id === data.agent);

    return <Card {...props} >
        {current && <PanelItem className={'gap-2'}>
            <img src={current.avatar} className={'rounded-2'} width={21} height={21} />
            <div>{current.name}</div>
        </PanelItem>}
    </Card>;
};

AgentNode.icon = {
    bg: '#198754',
    Component: Icon,
};

AgentNode.title = 'Agent';
AgentNode.description = '定义一个Agent类型的智能体来回复用户';

AgentNode.Data = Data;
AgentNode.Pane = Pane;

export default AgentNode;
