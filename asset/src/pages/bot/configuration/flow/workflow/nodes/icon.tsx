import { NodeEnum, nodeTypes } from '../types';
import { styled } from '@topthink/common';
import { memo } from 'react';

interface Props {
    className?: string;
    type: NodeEnum;
    bg?: boolean;
    size?: number | string;
    color?: boolean;
}

const Icon = ({ type, className, bg = true, size = '1em', color: showColor }: Props) => {

    const { bg: color, Component } = nodeTypes[type].icon;

    if (!bg) {
        return <Component className={className} width={size} height={size} color={showColor ? color : undefined} />;
    }

    return <Container $bg={color} className={className}>
        <Component width={size} height={size} />
    </Container>;
};

export default memo(Icon);

const Container = styled.div<{ $bg: string }>`
    box-shadow: var(--bs-box-shadow-sm);
    border-radius: var(--bs-border-radius);
    width: 1.75rem;
    height: 1.75rem;
    margin-right: .5rem;
    background: ${props => props.$bg};
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    color: #FFFFFF;
`;
