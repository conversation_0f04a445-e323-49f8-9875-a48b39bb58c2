import { RemoveIcon } from '@/components/action';
import DatasetSelector from '@/components/dataset-selector';
import useDatasets from '@/hooks/use-datasets';
import { produce } from '@topthink/common';
import { Row } from 'react-bootstrap';
import PanelItem from '../../../../components/panel-item';
import { useFlow } from '../../../context';
import Panel from '../../../panel';
import { NodeType } from '../../types';
import Pane from '../pane';
import Data from './data';

interface Props {
    node: NodeType<Data>;
}

export default function({ node }: Props) {
    const { id, data } = node;

    const datasets = useDatasets(data.datasets);
    const { updateNodeData } = useFlow<NodeType<Data>>();

    return <Pane node={node}>
        <Panel title={'知识库'}>
            <Row className='g-2'>
                {datasets.map((dataset) => (
                    <PanelItem key={dataset.hash_id}>
                        <i className='bi bi-journal-text text-muted me-2' />
                        <span className='me-auto'>{dataset.name}</span>
                        <RemoveIcon onClick={() => {
                            updateNodeData(id, ({ data }) => {
                                data.datasets = produce(data.datasets, draft => {
                                    const index = draft.indexOf(dataset.hash_id);
                                    if (index !== -1) draft.splice(index, 1);
                                });
                                return data;
                            });
                        }} />
                    </PanelItem>
                ))}
                <div className={'d-grid'}>
                    <DatasetSelector value={data.datasets} onChange={(updater) => {
                        updateNodeData(id, ({ data }) => {
                            data.datasets = produce(data.datasets, updater);
                            return data;
                        });
                    }} text={'添加知识库'} />
                </div>
            </Row>
        </Panel>
    </Pane>;
}
