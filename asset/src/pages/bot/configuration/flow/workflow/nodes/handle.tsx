import { css, styled } from '@topthink/common';
import { Handle as React<PERSON>lowHandle, Position, useConnection, useNodeConnections, useNodeId, } from '@xyflow/react';

const LinkIcon = styled.div`
    width: 1rem;
    height: 1rem;
    align-items: center;
    justify-content: center;
    background-color: var(--bs-primary);
    border-radius: 50%;
    color: #FFF;
    z-index: 10;
    position: absolute;
    top: 0;
    left: 0;
    display: none;
    pointer-events: none;
`;

const Handle = styled(ReactFlowHandle)<{ $connected: boolean }>`
    && {
        width: 1rem;
        height: 1rem;
        z-index: 1;
        background-color: transparent;
        border: none;
        line-height: 1;

        ${props => props.$connected && css`
            &:after {
                content: '';
                width: .125rem;
                height: .5rem;
                top: 3px;
                position: absolute;
                background-color: var(--bs-primary);
            }
        `};

        &.react-flow__handle-left {
            left: -1px;

            &:after {
                left: 6px;
            }
        }

        &.react-flow__handle-right {
            right: -1px;

            &:after {
                right: 6px;
            }
        }
    }

    .react-flow__node:hover &, .react-flow__node.selected & {
        ${LinkIcon} {
            display: flex;
        }
    }
`;

interface Props {
    id?: string | null;
}

export const SourceHandle = ({ id = null }: Props) => {

    const nodeId = useNodeId();
    const connections = useNodeConnections();
    const inProgress = useConnection(({
        inProgress,
        fromNode,
        fromPosition,
        fromHandle
    }) => inProgress && fromPosition === Position.Right && fromNode.id === nodeId && fromHandle.id === id);

    const connected = inProgress || !!connections.find(c => c.source === nodeId);

    return <Handle id={id} $connected={connected} type='source' position={Position.Right}>
        <LinkIcon><i className={'bi bi-plus'} /></LinkIcon>
    </Handle>;
};

export const TargetHandle = ({ id = null }: Props) => {

    const nodeId = useNodeId();
    const connections = useNodeConnections();
    const inProgress = useConnection(({
        inProgress,
        fromNode,
        fromPosition,
        fromHandle
    }) => inProgress && fromPosition === Position.Left && fromNode.id === nodeId && fromHandle.id === id);

    const connected = inProgress || !!connections.find(c => c.target === nodeId);

    return <Handle id={id} $connected={connected} type='target' position={Position.Left}>
        <LinkIcon><i className={'bi bi-plus'} /></LinkIcon>
    </Handle>;
};
