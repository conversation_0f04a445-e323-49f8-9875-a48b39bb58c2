import { useReactFlow } from '@xyflow/react';
import { useEffect } from 'react';
import Display from '../../panel/model-selector/display';
import { NodeProps } from '../../types';
import Card from '../card';
import Classes from './classes';
import Data from './data';
import { ReactComponent as Icon } from './icon.svg';
import Pane from './pane';

const IntentClassifierNode = (props: NodeProps<Data>) => {
    const { id, data } = props;
    const { getEdges, deleteElements } = useReactFlow();

    //清理不存在的edge
    useEffect(() => {
        const edges = getEdges();
        const deletedEdges = edges.filter(edge => {
            return edge.source == id && Number(edge.sourceHandle || 0) > data.classes.length - 1;
        });
        if (deletedEdges.length > 0) {
            deleteElements({
                edges: deletedEdges,
            });
        }
    }, [data.classes]);

    return <Card sourceable={false} {...props} >
        <Display value={data.model} tool={false} />
        <Classes classes={data.classes} />
    </Card>;
};

IntentClassifierNode.icon = {
    bg: '#03A4EE',
    Component: Icon,
};
IntentClassifierNode.title = '意图分类';
IntentClassifierNode.description = '定义用户问题的分类条件，LLM 能够根据分类描述定义对话的进展方式';
IntentClassifierNode.Pane = Pane;
IntentClassifierNode.Data = Data;

export default IntentClassifierNode;
