import { produce } from '@topthink/common';
import { useFlow } from '../../../context';
import History from '../../panel/history';
import { NodeType } from '../../types';
import Pane from '../pane';
import Data from './data';
import Variable from '../../panel/variable';
import ModelSelector from '../../panel/model-selector';
import Vision from '../../panel/vision';

interface Props {
    node: NodeType<Data>;
}

export default function({ node }: Props) {
    const { id, data } = node;
    const { updateNodeData } = useFlow<NodeType<Data>>();

    return <Pane node={node}>
        <ModelSelector
            value={data.model}
            tool={false}
            onChange={(updater) => {
                updateNodeData(id, ({ data }) => {
                    data.model = produce(data.model, updater);
                    return data;
                });
            }}
        />
        <Variable
            node={node}
            title={'系统提示词'}
            value={data.prompt}
            onChange={(value) => updateNodeData(id, ({ data }) => {
                data.prompt = value;
                return data;
            })}
            placeholder={'输入提示词'}
        />
        <History node={node} />
        <Variable
            node={node}
            title={'用户提示词'}
            value={data.query}
            onChange={(value) => updateNodeData(id, ({ data }) => {
                data.query = value;
                return data;
            })}
            placeholder={'输入提示词，默认为 用户问题'}
        />
        <Vision node={node} />
    </Pane>;
}
