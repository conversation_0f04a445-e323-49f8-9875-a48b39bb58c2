import Data, { Output } from '../data';

export default class StartData extends Data {

    static variables: Variable[] = [];

    getOutput() {
        const properties: Output['properties'] = {};

        for (const variable of StartData.variables) {
            properties[variable.key] = {
                type: 'string',
                title: variable.label
            };
        }

        return {
            properties: {
                ...properties,
                'sys.query': {
                    type: 'string',
                    title: '用户问题'
                },
                'sys.files': {
                    type: 'array',
                    title: '上传文件'
                },
                'sys.time': {
                    type: 'string',
                    title: '当前时间'
                },
                'sys.ip': {
                    type: 'string',
                    title: '用户IP'
                },
                'sys.source': {
                    type: 'string',
                    title: '对话来源'
                }
            }
        };
    }
}
