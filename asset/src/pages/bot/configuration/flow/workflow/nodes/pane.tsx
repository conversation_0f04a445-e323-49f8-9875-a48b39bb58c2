import { styled } from '@topthink/common';
import { ChangeEvent, ReactNode, useCallback } from 'react';
import { useFlow } from '../../context';
import Pane from '../../pane';
import { NodeType } from '../types';
import Icon from './icon';
import Input from '../panel/input';
import Output from '../panel/output';

interface Props {
    node: NodeType;
    children?: ReactNode;
}

export default function({ node, children }: Props) {
    const { id } = node;
    const { updateNode, updateNodeData } = useFlow();

    const onHide = useCallback(() => {
        updateNode(id, node => {
            node.selected = false;
            return node;
        });
    }, [id]);

    const onTitleChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
        updateNodeData(id, ({ data }) => {
            data.title = e.target.value;

            return data;
        });
    }, [id]);

    return <Pane
        onHide={onHide}
        header={<>
            <Icon type={node.type} />
            <TitleInput
                defaultValue={node.data.title}
                onBlur={onTitleChange}
            />
        </>}>
        <Input node={node} />
        {children}
        <Output node={node} />
    </Pane>;
}

const TitleInput = styled.input`
    width: 100%;
    border: none;
    outline: none;
    background: transparent;
`;
