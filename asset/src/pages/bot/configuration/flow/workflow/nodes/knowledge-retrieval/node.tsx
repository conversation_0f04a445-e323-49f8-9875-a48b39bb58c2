import Card from '../card';
import { NodeProps } from '../../types';
import Data from './data';
import { ReactComponent as Icon } from './icon.svg';
import { Row } from 'react-bootstrap';
import useDatasets from '@/hooks/use-datasets';
import PanelItem from '../../../../components/panel-item';
import Pane from './pane';

const KnowledgeRetrievalNode = (props: NodeProps<Data>) => {
    const { data } = props;
    const datasets = useDatasets(data.datasets);

    return <Card {...props} >
        {datasets.length > 0 && <Row className='g-2'>
            {datasets.map((dataset) => (
                <PanelItem key={dataset.hash_id}>
                    <i className='bi bi-journal-text text-muted me-2' />
                    <span className='me-auto'>{dataset.name}</span>
                </PanelItem>
            ))}
        </Row>}
    </Card>;
};

KnowledgeRetrievalNode.icon = {
    bg: '#8957E5',
    Component: Icon,
};
KnowledgeRetrievalNode.title = '知识检索';
KnowledgeRetrievalNode.description = '允许你从知识库中查询与用户问题相关的文本内容';
KnowledgeRetrievalNode.Data = Data;
KnowledgeRetrievalNode.Pane = Pane;

export default KnowledgeRetrievalNode;
