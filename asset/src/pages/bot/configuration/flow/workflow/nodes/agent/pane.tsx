import { RemoveIcon } from '@/components/action';
import useAgents from '@/hooks/use-agents';
import { Select } from '@topthink/common';
import { useEffect, useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import { useFlow } from '../../../context';
import Panel from '../../../panel';
import VariableSelector from '../../panel/variable-selector';
import { NodeType } from '../../types';
import Pane from '../pane';
import Data from './data';

interface Props {
    node: NodeType<Data>;
}

export default function({ node }: Props) {
    const { id, data } = node;
    const { updateNodeData } = useFlow<NodeType<Data>>();
    const [variables, setVariables] = useState<Variable[]>([]);
    const agents = useAgents();

    useEffect(() => {
        setVariables(agents.find((v) => v.id === data.agent)?.feature.variable.variables || []);
    }, [data.agent, agents]);

    return <Pane node={node}>
        <Panel title={'智能体'}>
            <Select
                placeholder={'请选择智能体'}
                options={agents.map((agent) => ({
                    value: agent.id,
                    label: <div className={'d-flex align-items-center gap-2'}>
                        <img src={agent.avatar} className={'rounded-2'} width={21} height={21} />
                        <div>{agent.name}</div>
                    </div>
                }))}
                value={(data.agent || undefined)}
                onChange={(value) => {
                    updateNodeData(id, ({ data }) => {
                        data.agent = value;
                        data.variables = {};
                        return data;
                    });
                }}
                allowClear
            />
        </Panel>
        {variables.length > 0 && <Panel title={'变量设置'}>
            <Row className={'g-2'}>
                {variables.map(({ key, label }) => {
                    return <Col md={12} key={key} className={'d-flex align-items-center gap-2'}>
                        <Row className={'flex-fill'}>
                            <Col md={3} className={'d-flex gap-1 align-items-center'}>
                                {label || key}
                            </Col>
                            <Col md={9}>
                                <VariableSelector
                                    node={node}
                                    value={data.variables[key]}
                                    onChange={(value) => {
                                        updateNodeData(id, ({ data }) => {
                                            data.variables = {
                                                ...data.variables,
                                                [key]: value
                                            };
                                            return data;
                                        });
                                    }}
                                />
                            </Col>
                        </Row>
                        <RemoveIcon className={'opacity-0'} />
                    </Col>;
                })}
            </Row>
        </Panel>}
    </Pane>;
}
