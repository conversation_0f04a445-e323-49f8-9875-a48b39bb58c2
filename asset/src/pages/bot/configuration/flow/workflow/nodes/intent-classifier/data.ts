import Data from '../data';

export default class extends Data {

    static input = {
        properties: {
            query: { title: '查询内容', placeholder: '用户问题' },
        },
        additional: false,
    };

    static output = {
        properties: {
            class: {
                type: 'string',
                title: '分类结果'
            }
        }
    };

    model: ModelConfiguration = {
        name: ''
    };

    history = {
        enable: false,
        round: 5
    };

    vision = {
        enable: false,
        files: ''
    };

    classes: string[] = [];

}
