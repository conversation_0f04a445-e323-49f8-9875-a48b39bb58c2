export const MAX_ITERATION_PARALLEL_NUM = 10;
export const MIN_ITERATION_PARALLEL_NUM = 1;
export const DEFAULT_ITER_TIMES = 1;
export const NODE_WIDTH = 240;
export const X_OFFSET = 60;
export const NODE_WIDTH_X_OFFSET = NODE_WIDTH + X_OFFSET;
export const Y_OFFSET = 39;
export const MAX_TREE_DEPTH = 50;
export const START_INITIAL_POSITION = { x: 80, y: 282 };
export const AUTO_LAYOUT_OFFSET = {
    x: -42,
    y: 243,
};
export const ITERATION_NODE_Z_INDEX = 1;
export const ITERATION_CHILDREN_Z_INDEX = 1002;
export const ITERATION_PADDING = {
    top: 65,
    right: 16,
    bottom: 20,
    left: 16,
};

export const DEFAULT_RETRY_MAX = 3;
export const DEFAULT_RETRY_INTERVAL = 100;
