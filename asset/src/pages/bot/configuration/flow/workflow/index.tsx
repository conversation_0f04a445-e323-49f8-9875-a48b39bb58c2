import { styled } from '@topthink/common';
import {
    addEdge,
    applyEdgeChanges,
    applyNodeChanges,
    Background,
    getOutgoers,
    IsValidConnection,
    MiniMap,
    Node,
    OnConnect,
    OnEdgesChange,
    OnNodesChange,
    ReactFlow,
    SelectionMode,
    useOnViewportChange,
    useReactFlow
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { cloneDeep, cloneDeepWith, toPlainObject } from 'lodash';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useFlow } from '../context';
import Footer from './footer';
import Line from './line';
import Data from './nodes/data';
import StartData from './nodes/start/data';
import Pane from './pane';
import { edgeTypes, NodeType, nodeTypes } from './types';
import { initialNodes, isNodeType } from './utils';

interface Props extends BotFlowConfigurationUpdaterProps {

}

export default function Workflow({ value, onChange }: Props) {
    const { panel } = useFlow();
    const { getNodes, getEdges } = useReactFlow();

    const [nodes, setNodes] = useState(() => initialNodes(cloneDeep(value.nodes)));
    const [edges, setEdges] = useState(() => cloneDeep(value.edges));

    const onNodesChange = useCallback<OnNodesChange<NodeType>>((changes) => {
        //TODO 使用这个Change来做undo-redo
        setNodes((nds) => applyNodeChanges(changes, nds));
    }, []);
    const onEdgesChange = useCallback<OnEdgesChange>((changes) => {
        //TODO 使用这个Change来做undo-redo
        setEdges((eds) => applyEdgeChanges(changes, eds));
    }, []);
    const onConnect = useCallback<OnConnect>((params) => setEdges((eds) => addEdge({
        type: 'default',
        ...params
    }, eds)), []);

    const isValidConnection = useCallback<IsValidConnection>((connection) => {
        const nodes = getNodes();
        const edges = getEdges();
        const target = nodes.find((node) => node.id === connection.target);
        const hasCycle = (node: Node, visited = new Set()) => {
            if (visited.has(node.id)) return false;

            visited.add(node.id);

            for (const outgoer of getOutgoers(node, nodes, edges)) {
                if (outgoer.id === connection.source) return true;
                if (hasCycle(outgoer, visited)) return true;
            }
        };

        if (!target || target.id === connection.source) return false;
        return !hasCycle(target);
    }, [getNodes, getEdges]);

    useEffect(() => {
        onChange(value => {
            value.nodes = cloneDeepWith(nodes, value => {
                if (value instanceof Data) {
                    return cloneDeep(toPlainObject(value));
                }
            });
            value.edges = cloneDeep(edges);
        });
    }, [nodes, edges]);

    useOnViewportChange({
        onEnd(viewport) {
            onChange(value => {
                value.viewport = viewport;
            });
        }
    });

    const selected = useMemo(() => {
        const node = nodes.find(node => node.selected);
        if (node && isNodeType(node)) {
            return node;
        }
        return undefined;
    }, [nodes]);

    //更新start节点的output
    useEffect(() => {
        StartData.variables = value.feature.variable.variables;
    }, [value.feature]);

    return <>
        <StyledReactFlow
            nodeTypes={nodeTypes}
            edgeTypes={edgeTypes}
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            isValidConnection={isValidConnection}
            connectionLineComponent={Line}
            multiSelectionKeyCode={null}
            selectionKeyCode={null}
            deleteKeyCode={null}
            selectionMode={SelectionMode.Partial}
            defaultViewport={value.viewport}
            minZoom={0.5}
        >
            <StyledMiniMap style={{
                width: 102,
                height: 72,
            }} />
            <Footer />
            <Background />
            {selected && <Pane node={selected} />}
        </StyledReactFlow>
        {panel}
    </>;
}

const StyledReactFlow = styled(ReactFlow<NodeType>)`
    --xy-edge-stroke-default: #d0d5dc !important;
    --xy-edge-stroke-selected-default: var(--bs-primary) !important;
`;

const StyledMiniMap = styled(MiniMap)`
    left: 1rem !important;
    bottom: 4.5rem !important;
    right: auto !important;
    margin: 0 !important;
    border-radius: var(--bs-border-radius);
`;
