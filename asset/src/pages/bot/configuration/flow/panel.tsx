import { styled } from '@topthink/common';
import { ReactNode, useEffect } from 'react';
import { useKeys } from './pane';
import { Accordion } from 'react-bootstrap';

interface Props {
    title: string;
    children?: ReactNode;
    action?: ReactNode;
    eventKey?: string;
    open?: boolean;
}

export default function Panel({ title, action, children, open, eventKey }: Props) {

    children = <Body>{children}</Body>;

    if (eventKey) {
        const [, setKey] = useKeys();
        useEffect(() => {
            if (open !== undefined) {
                setKey(keys => open ? new Set([...keys, eventKey]) : new Set([...keys].filter(key => key !== eventKey)));
            }
        }, [open]);

        children = <Accordion.Collapse eventKey={eventKey}>
            <>{children}</>
        </Accordion.Collapse>;
    }

    return <Container>
        <Header>
            {title}
            {action}
        </Header>
        {children}
    </Container>;
}

const Header = styled.div`
    font-weight: bold;
    font-size: 1rem;
    line-height: 1.75rem;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .form-check-input {
        margin-top: 0;
    }
`;

const Body = styled.div`
    &:not(:empty) {
        margin-top: .5rem;
    }
`;

const Container = styled.div`
    background: var(--bs-gray-120);
    padding: .75rem;
    border-radius: .5rem;
`;
