import { useReactFlow } from '@xyflow/react';
import { createContext, ReactNode, useCallback, useContext, useState } from 'react';
import { NodeType } from './workflow/types';

interface ContextTypes {
    panel: ReactNode;
    setPanel: (panel: ReactNode) => void;
    config: BotFlowConfiguration;
}

const Context = createContext<ContextTypes | undefined>(undefined);

interface Props {
    children: ReactNode;
}

interface Props {
    config: BotFlowConfiguration;
}

export const FlowProvider = ({ children, config }: Props) => {
    const [panel, setPanel] = useState<ReactNode>(null);

    return <Context.Provider value={{
        config,
        panel,
        setPanel,
    }}>
        {children}
    </Context.Provider>;
};

export const useFlow = <N extends NodeType = NodeType>() => {
    const context = useContext(Context);
    if (!context) {
        throw new Error();
    }

    const { updateNodeData, updateNode } = useReactFlow<N>();

    return {
        ...context,
        updateNodeData: useCallback((id: string, updater: Partial<N['data']> | ((node: N) => Partial<N['data']>)) => {
            return updateNodeData(id, updater, { replace: true });
        }, [updateNodeData]),
        updateNode
    };
};
