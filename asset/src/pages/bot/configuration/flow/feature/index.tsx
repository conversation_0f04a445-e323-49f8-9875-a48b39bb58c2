import { Button } from 'react-bootstrap';
import { useFlow } from '../context';
import FeaturePane from './pane';


export default function Feature({ value, onChange }: ConfigUpdaterProps<BotFeature>) {
    const { setPanel } = useFlow();

    return <Button variant={'light'} onClick={() => setPanel(<FeaturePane value={value} onChange={onChange} />)}>
        <i className='bi bi-grid me-2' />功能
    </Button>;
}
