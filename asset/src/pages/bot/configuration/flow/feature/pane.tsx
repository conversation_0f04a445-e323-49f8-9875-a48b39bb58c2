import Input from '../../components/input';
import Onboarding from '../../components/onboarding';
import Output from '../../components/output';
import Suggestion from '../../components/suggestion';
import Variable from '../../components/variable';
import Pane from '../pane';
import useConfig from '../../components/use-config';
import { useEffect } from 'react';
import { useFlow } from '../context';

export default function FeaturePane({ value: defaultValue, onChange }: ConfigUpdaterProps<BotFeature>) {
    const { setPanel } = useFlow();
    const [value, updater] = useConfig(defaultValue);

    useEffect(() => {
        onChange(() => value);
    }, [value]);

    return <Pane title={'功能设置'} onHide={() => setPanel(null)}>
        <Variable {...updater('variable')} />
        <Onboarding {...updater('onboarding')} />
        <Suggestion {...updater('suggestion')} />
        <Input {...updater('input')} />
        <Output {...updater('output')} />
    </Pane>;
}
