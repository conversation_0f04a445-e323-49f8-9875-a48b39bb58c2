import { ActionIcon } from '@/components/action';
import { FormSchema, FormUiSchema, ModalForm } from '@topthink/common';

interface Props {
    tool: Tool;
    value?: any;
    onChange?: (value: any) => void;
}

export default function Setting({ tool, onChange, value }: Props) {

    if (tool.parameters) {
        const parameters = Object.entries(tool.parameters).filter(([, parameter]) => parameter.provider === 'user');

        if (parameters.length > 0) {
            const uiSchema: FormUiSchema = {};
            const properties: FormSchema['properties'] = {};
            const required: string[] = [];

            for (const [key, value] of parameters) {
                properties[key] = {
                    type: value.type,
                    title: value.title || value.description,
                    enum: value.enum,
                    enumNames: value.enumNames,
                    default: value.default
                };

                if (value.required) {
                    required.push(key);
                }

                uiSchema[key] = {
                    'ui:placeholder': value.placeholder,
                    'ui:help': value.url && `<a class='link-primary' href='${value.url}' target='_blank'>如何获取<i class="bi bi-box-arrow-up-right ms-1"></i></a>`
                };
            }

            const formSchema: FormSchema = {
                type: 'object',
                properties,
                required
            };

            return <ModalForm
                text={<i className='bi bi-sliders' />}
                modalProps={{ header: '设置' }}
                buttonProps={{ as: ActionIcon, className: 'ms-1 visible' }}
                formData={value}
                schema={formSchema}
                onSubmit={({ formData }) => {
                    onChange?.(formData);
                    return true;
                }}
                uiSchema={uiSchema}
            />;
        }
    }

    return null;
}
