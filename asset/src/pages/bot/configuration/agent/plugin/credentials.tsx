import { FormSchema, FormUiSchema, ModalForm, ModalType, RequestButton } from '@topthink/common';
import { useRef } from 'react';
import { useBot } from '../../../provider';

interface Props {
    plugin: BotPlugin;
    schema: NonNullable<BotPlugin['credentials']>;
    credentials?: object;
    onChange?: () => void;
}

export default function Credentials({ credentials, schema, plugin, onChange }: Props) {
    const { space, current } = useBot();
    const uiSchema: FormUiSchema = {};
    const properties: FormSchema['properties'] = {};
    const required: string[] = [];

    for (const [key, value] of Object.entries(schema)) {
        properties[key] = {
            type: 'string',
            title: value.title,
            default: value.default
        };

        if (value.required) {
            required.push(key);
        }

        uiSchema[key] = {
            'ui:placeholder': value.placeholder,
            'ui:help': value.url && `<a class='link-primary' href='${value.url}' target='_blank'>如何获取<i class="bi bi-box-arrow-up-right ms-1"></i></a>`
        };
    }

    const formSchema: FormSchema = {
        type: 'object',
        properties,
        required
    };

    const ref = useRef<ModalType>(null);

    return <span onClick={e => e.stopPropagation()}>
        <ModalForm
            method={'post'}
            action={`/space/${space.hash_id}/bot/${current.hash_id}/credentials`}
            transformData={data => {
                return {
                    plugin: plugin.name,
                    credentials: data
                };
            }}
            schema={formSchema}
            ref={ref}
            formData={credentials}
            uiSchema={uiSchema}
            buttonProps={{
                size: 'sm',
                variant: 'light',
                className: 'd-flex align-items-center gap-2',
            }}
            modalProps={{
                header: '设置授权',
                footer: ({ okButton, cancelButton }) => {
                    return <>
                        {credentials && <RequestButton
                            className='me-auto'
                            variant={'outline-danger'}
                            method={'delete'}
                            confirm={'确定要移除吗？'}
                            url={{
                                url: `/space/${space.hash_id}/bot/${current.hash_id}/credentials`,
                                data: {
                                    plugin: plugin.name
                                }
                            }}
                            onSuccess={() => {
                                ref.current?.close();
                                onChange?.();
                            }}
                        >移除</RequestButton>}
                        {cancelButton}
                        {okButton}
                    </>;
                }
            }}
            onSuccess={onChange}
            text={credentials ? <>
                <span className='p-1 bg-success border border-light rounded-circle' />
                已授权
            </> : <>
                <span className='p-1 bg-secondary border border-light rounded-circle' />
                去授权
            </>}
        />
    </span>;
}
