import { Accordion } from 'react-bootstrap';
import { css, Loader, Result, styled, useWindowSize } from '@topthink/common';
import { useState } from 'react';
import PluginItem from './item';
import usePlugins from '../use-plugins';
import useCredentials from '../use-credentials';

interface Props extends BotAgentConfigurationUpdaterProps<'tools'> {
    max?: number;
}

export default function Modal({ onChange, value, max }: Props) {
    const { isMobile } = useWindowSize();
    const [cate, setCate] = useState<'all' | 'installed'>('all');
    const [showSider, setShowSider] = useState(false);
    const { credentials, refresh } = useCredentials();

    const { plugins: allPlugins, loading } = usePlugins({ tools: cate == 'installed' ? value : undefined });

    // 对于"已安装"列表，只显示能够找到的插件工具
    const list = cate === 'installed'
        ? allPlugins.map(plugin => ({
            ...plugin,
            tools: plugin.tools.filter(tool =>
                value.some(v => v.plugin === plugin.name && v.name === tool.name)
            )
        })).filter(plugin => plugin.tools.length > 0)
        : allPlugins;

    const disabled = !!(max && (value.length >= max));

    return <Container>
        <SiderWrap $show={showSider || !isMobile} onClick={(e) => {
            if (e.target === e.currentTarget) {
                setShowSider(!showSider);
            }
        }}>
            <Sider>
                {showSider || !isMobile ?
                    <Categories>
                        <CategoryItem $active={cate === 'all'} onClick={() => {
                            setCate('all');
                            setShowSider(false);
                        }}>
                            全部
                        </CategoryItem>
                        <CategoryItem $active={cate === 'installed'} onClick={() => {
                            setCate('installed');
                            setShowSider(false);
                        }}>
                            已安装
                        </CategoryItem>
                    </Categories> :
                    <Handler onClick={() => setShowSider(true)}><i className='bi bi-chevron-double-right' /></Handler>
                }
            </Sider>
        </SiderWrap>
        <Body>
            <Accordion key={cate}>
                <Loader loading={loading} />
                {list.length === 0 && !loading && <Result status={'info'} title={'暂无插件'} />}
                {list.map(plugin => {
                    return <PluginItem
                        key={plugin.name}
                        plugin={plugin}
                        value={value}
                        onChange={onChange}
                        disabled={disabled}
                        credentials={credentials[plugin.name]}
                        onCredentialsChange={refresh}
                    />;
                })}
            </Accordion>
        </Body>
    </Container>;
}

const CategoryItem = styled.div<{ $active: boolean }>`
    display: flex;
    gap: 1rem;
    align-items: center;
    cursor: pointer;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;

    &:hover {
        background-color: var(--bs-gray-200);
    }

    ${({ $active }) => $active && css`
        background-color: var(--bs-gray-400) !important;
    `};

`;

const Categories = styled.div`
    overflow-y: auto;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 2px;
`;

const Body = styled.div`
    display: flex;
    flex-direction: column;
    overflow: auto;
    height: 100%;
    margin-left: 220px;
    position: relative;
    @media (max-width: 992px) {
        margin-left: 28px;
    }
`;

const Sider = styled.div`
    width: 220px;
    max-width: calc(100vw - 5rem);
    display: flex;
    flex-direction: column;
    border-right: 1px solid var(--bs-gray-300);
    background: rgb(245, 245, 245);
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    transition: transform 0.5s ease;
    // transform: translateX(calc(-100% + 2rem));

`;

const Handler = styled.div`
    width: 28px;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: end;
    justify-content: center;
    padding-bottom: 10px;
    cursor: pointer;
`;

const SiderWrap = styled.div<{ $show: boolean }>`
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);

    @media (max-width: 992px) {
        ${({ $show }) => $show ? css`
            right: 0;

            ${Sider} {
                transform: translateX(0);
            }
        ` : css`
            ${Sider} {
                transform: translateX(calc(-100% + 2rem));
            }
        `};
    }
`;

const Container = styled.div`
    margin: -1rem;
    height: 70vh;
    position: relative;
    overflow: hidden;
    border-bottom-left-radius: var(--bs-modal-border-radius);
    border-bottom-right-radius: var(--bs-modal-border-radius);
`;
