import { Button, styled, Tooltip } from '@topthink/common';
import { useContext } from 'react';
import { Accordion, AccordionContext, Badge, useAccordionButton } from 'react-bootstrap';
import Credentials from './credentials';
import Setting from './setting';

interface Props extends BotAgentConfigurationUpdaterProps<'tools'> {
    plugin: BotPlugin;
    disabled: boolean;
    credentials?: object;
    onCredentialsChange?: () => void;
}

export default function PluginItem({ plugin, credentials, disabled, value, onChange, onCredentialsChange }: Props) {
    const { activeEventKey } = useContext(AccordionContext);
    const decoratedOnClick = useAccordionButton(plugin.name);

    if (plugin.tools.length === 0) {
        return null;
    }

    return <Container>
        <PluginHeader onClick={decoratedOnClick}>
            <PluginAvatar>
                <img src={plugin.icon} width={24} height={24} />
            </PluginAvatar>
            <PluginInfo>
                <h3 className={'d-flex gap-2 align-items-center'}>
                    {plugin.title}
                    {plugin.credentials &&
                        <Credentials
                            onChange={onCredentialsChange}
                            plugin={plugin}
                            credentials={credentials}
                            schema={plugin.credentials}
                        />}
                </h3>
                <p>{plugin.description}</p>
            </PluginInfo>
            {activeEventKey?.includes(plugin.name) ? <i className='bi bi-chevron-down text-muted' /> :
                <i className='bi bi-chevron-right text-muted' />}
        </PluginHeader>
        <Accordion.Collapse eventKey={plugin.name}>
            <ToolList>
                <h4>包含 {plugin.tools.length} 个工具</h4>
                <div className='d-flex border rounded bg-light flex-column mb-3'>
                    {plugin.tools.map((tool) => {
                        const installed = value.find((t) => t.plugin === plugin.name && t.name === tool.name);
                        return <ToolItem key={tool.name}>
                            <PluginInfo>
                                <h3 className={'d-flex gap-2 align-items-center'}>
                                    {tool.title}
                                    <Badge bg={'secondary'}>{tool.fee < 0 ? '动态计费' : tool.fee > 0 ? `${tool.fee} Token / 次` : '免费'}</Badge>
                                </h3>
                                <p>{tool.description}</p>
                            </PluginInfo>
                            {installed ?
                                <>
                                    <Setting
                                        tool={tool}
                                        value={installed.args}
                                        onChange={(value) => {
                                            onChange(draft => {
                                                const i = draft.find(t => t.plugin === plugin.name && t.name === tool.name);
                                                if (i) {
                                                    i.args = value;
                                                }
                                            });
                                        }}
                                    />
                                    <Button
                                        size={'sm'}
                                        variant={'secondary'}
                                        className={'px-3'}
                                        onClick={() => {
                                            onChange(draft => {
                                                const index = draft.findIndex(t => t.plugin === plugin.name && t.name === tool.name);
                                                if (index !== -1) draft.splice(index, 1);
                                            });
                                        }}
                                    >卸载</Button>
                                </> :
                                (disabled ? <Tooltip tooltip={'最多安装5个工具'}>
                                    <span><Button
                                        disabled
                                        size={'sm'}
                                        variant={'primary'}
                                        className={'px-3'}
                                    >安装</Button></span>
                                </Tooltip> : <Button
                                    onClick={() => {
                                        onChange(draft => {
                                            draft.push({
                                                plugin: plugin.name,
                                                name: tool.name
                                            });
                                        });
                                    }}
                                    size={'sm'}
                                    variant={'primary'}
                                    className={'px-3'}
                                >安装</Button>)
                            }
                        </ToolItem>;
                    })}
                </div>
            </ToolList>
        </Accordion.Collapse>
    </Container>;
}

const ToolItem = styled.div`
    padding: .75rem 1rem;
    display: flex;
    gap: 1rem;
    align-items: center;

    &:not(:last-child) {
        border-bottom: 1px solid var(--bs-border-color);
    }
`;

const ToolList = styled.div`
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-bottom: 1px solid var(--bs-gray-300);
    background: rgb(245, 245, 245);
    padding: 0 1rem;

    h4 {
        font-size: .9rem;
        line-height: 2.5rem;
        color: var(--bs-secondary);
        margin-bottom: 0;
    }
`;

const PluginInfo = styled.div`
    flex: 1;

    h3 {
        font-size: 1.15rem;
        line-height: 1.5rem;
        font-weight: 600;
        margin-bottom: .25rem;

        .btn {
            --bs-btn-padding-y: 0;
            --bs-btn-padding-x: 0.5rem;
            --bs-btn-font-size: 11px;
            line-height: 19px;
        }

        .badge {
            --bs-badge-padding-x: 0.5em;
            --bs-badge-padding-y: 0.25em;
            --bs-badge-font-weight: 500;
        }
    }

    p {
        margin-bottom: 0;
        color: var(--bs-secondary);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        overflow: hidden;
    }
`;

const PluginAvatar = styled.div`
    padding: .6rem;
    border: 1px solid var(--bs-gray-200);
    border-radius: .375rem;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
        border-radius: 0.375rem;
    }
`;

const PluginHeader = styled.div`
    padding: 1rem;
    border-bottom: 1px solid var(--bs-gray-300);
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
`;

const Container = styled.div`
`;
