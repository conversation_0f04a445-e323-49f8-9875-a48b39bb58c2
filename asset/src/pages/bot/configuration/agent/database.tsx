import { RemoveIcon } from '@/components/action';
import useDatabases from '@/hooks/use-databases';
import { useBot } from '@/pages/bot/provider';
import { Button, ModalButton, Panel, Table, Tooltip } from '@topthink/common';
import { Row, Stack } from 'react-bootstrap';
import { PanelItem } from './components';

export default function Database({ value, onChange }: BotAgentConfigurationUpdaterProps<'database'>) {
    const { space } = useBot();

    const databases = useDatabases(value.databases);

    // 找出未查询到的数据库ID
    const foundDatabaseIds = databases.map(db => db.hash_id);
    const missingDatabaseIds = value.databases.filter(id => !foundDatabaseIds.includes(id));

    const action = <ModalButton
        size='sm'
        variant='light'
        modalProps={{ footer: false, size: 'lg', header: '选择数据库' }}
        text={<><i className='bi bi-plus me-1' />添加</>}
    >
        <Table
            card={false}
            toolBarRender={false}
            source={`/space/${space.hash_id}/database`}
            columns={[
                {
                    dataIndex: 'name',
                    title: '名称',
                    render({ record }) {
                        return <Stack gap={1}>
                            {record.name}
                            <span className='fs-7 text-muted'>{record.description || '暂无描述'}</span>
                        </Stack>;
                    }
                },
                {
                    dataIndex: 'create_time',
                    title: '创建时间',
                    width: 150,
                },
                {
                    key: 'action',
                    title: '操作',
                    width: 120,
                    align: 'right',
                    render({ record }) {
                        if (value.databases.includes(record.hash_id)) {
                            return <Button
                                onClick={() => {
                                    onChange(draft => {
                                        const index = draft.databases.indexOf(record.hash_id);
                                        if (index !== -1) draft.databases.splice(index, 1);
                                    });
                                }}
                                className={'link-danger'}
                            >移除</Button>;
                        } else {
                            return <Button
                                onClick={() => {
                                    onChange(draft => {
                                        draft.databases.push(record.hash_id);
                                    });
                                }}
                            >添加</Button>;
                        }
                    }
                }
            ]} />
    </ModalButton>;

    return <Panel title='数据库' action={action}>
        {(databases.length > 0 || missingDatabaseIds.length > 0) ?
            <Row className='g-2'>
                {/* 显示正常的数据库 */}
                {databases.map((database) => (
                    <PanelItem key={database.hash_id}>
                        <i className='bi bi-database text-muted me-2' />
                        <span className='me-auto'>{database.name}</span>
                        <RemoveIcon onClick={() => {
                            onChange(draft => {
                                const index = draft.databases.indexOf(database.hash_id);
                                if (index !== -1) draft.databases.splice(index, 1);
                            });
                        }} />
                    </PanelItem>
                ))}
                {/* 显示未查询到的数据库，标红 */}
                {missingDatabaseIds.map((databaseId) => (
                    <PanelItem key={databaseId}>
                        <Tooltip tooltip={'该数据库未找到或已被删除'}>
                            <i className={'bi bi-info-circle text-danger me-2'} />
                        </Tooltip>
                        <span className='me-auto text-danger'>
                            [{databaseId}]
                        </span>
                        <RemoveIcon onClick={() => {
                            onChange(draft => {
                                const index = draft.databases.indexOf(databaseId);
                                if (index !== -1) draft.databases.splice(index, 1);
                            });
                        }} />
                    </PanelItem>
                ))}
            </Row> :
            <div className='text-muted'>添加数据库后，用户发送消息时，智能体能够引用数据库中的内容回答用户问题。</div>
        }
    </Panel>;
}
