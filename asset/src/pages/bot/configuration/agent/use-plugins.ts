import { request, useAsync } from '@topthink/common';
import { useBot } from '@/pages/bot/provider';

interface Options {
    tools?: BotAgentConfiguration['tools'];
    onSuccess?: (plugins: BotPlugin[]) => void;
}

export default function usePlugins({ tools, onSuccess }: Options) {
    const { current, space } = useBot();
    const name = tools ? Array.from(new Set(tools.map(item => item.plugin))).join(',') : undefined;

    const { result: plugins = [], loading } = useAsync<BotPlugin[]>(async (name: string) => {
        const params: Record<string, any> = {};

        if (name != undefined) {
            if (!name) {
                return [];
            }
            params.name = name;
        }

        const plugins = await request<BotPlugin[]>({
            url: `/space/${space.hash_id}/bot/${current.hash_id}/plugin`,
            params,
        });

        if (onSuccess) {
            onSuccess(plugins);
        }

        return plugins;
    }, [name]);

    return { plugins, loading };
}
