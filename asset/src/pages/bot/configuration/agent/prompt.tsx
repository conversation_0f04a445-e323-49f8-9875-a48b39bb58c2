import { useCurrentSpace } from '@/components/space-provider';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Panel, request, showRequestError, Space } from '@topthink/common';
import { useMemo, useRef, useState } from 'react';
import { Dropdown } from 'react-bootstrap';
import { useBot } from '../../provider';
import VariableEditor, { VariableEditorType } from '../components/variable-editor';
import presets from './presets';

const systemVariables = [
    {
        key: 'time',
        label: '当前时间'
    },
    {
        key: 'ip',
        label: '用户IP'
    },
    {
        key: 'source',
        label: '对话来源'
    }
];

export default function({ value, onChange }: BotAgentConfigurationUpdaterProps) {
    const editor = useRef<VariableEditorType>(null);
    const { current } = useCurrentSpace();
    const { current: bot } = useBot();

    const [generating, setGenerating] = useState(false);

    const generatePrompt = async function() {
        if (await Mo<PERSON>.confirm({
            title: '覆盖提示',
            message: '是否使用AI生成的内容覆盖原有内容？'
        })) {
            setGenerating(true);
            onChange(draft => {
                draft.prompt = '';
            });

            try {
                await request({
                    url: `/space/${current.hash_id}/generate/prompt`,
                    method: 'post',
                    data: {
                        query: `${bot.name}\n${bot.description}`
                    },
                    onMessage: (message) => {
                        if (message.data !== '[DONE]') {
                            onChange(draft => {
                                draft.prompt += JSON.parse(message.data);
                            });
                        }
                    }
                });
            } catch (e) {
                showRequestError(e);
            }

            setGenerating(false);
        }
    };

    const action = <Space>
        <Button tooltip={'插入变量'} disabled={generating} variant='light' size='sm' onMouseDown={(e) => {
            e.preventDefault();
            editor.current?.insertVariable();
        }}>
            <i className='bi bi-braces-asterisk' />
        </Button>
        <Dropdown>
            <Dropdown.Toggle variant='light' size='sm' disabled={generating}>
                <i className='bi bi-text-left me-1' />加载预设
            </Dropdown.Toggle>
            <Dropdown.Menu>
                {presets.map(preset => (
                    <Dropdown.Item key={preset.title} onClick={() => {
                        onChange(draft => {
                            draft.prompt = preset.config.prompt || '';
                            draft.tools = preset.config.tools || [];
                        });
                    }}>{preset.title}</Dropdown.Item>
                ))}
            </Dropdown.Menu>
        </Dropdown>
        <Button tooltip={'根据名称和简介AI自动生成提示词内容'} variant='light' size='sm' onClick={generatePrompt} disabled={generating}>
            <i className='bi bi-magic me-1' />AI生成
        </Button>
    </Space>;

    const variables = useMemo(() => {
        return [...value.variable.variables, ...systemVariables];
    }, [value.variable.variables]);

    return <Panel title={'提示词'} action={action}>
        <div className={'position-relative'}>
            {generating && <Loader />}
            <VariableEditor
                ref={editor}
                variables={variables}
                value={value.prompt}
                onChange={value => onChange(draft => {
                    draft.prompt = value;
                })}
                placeholder='使用自然语言填写智能体的人物设定、功能和工作流程'
            />
        </div>
    </Panel>;
}
