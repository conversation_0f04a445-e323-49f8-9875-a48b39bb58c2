import { styled } from '@topthink/common';
import { Col } from 'react-bootstrap';
import { PropsWithChildren } from 'react';
import { ActionIcon } from '@/components/action';

export const PanelItem = function({ children, span = 12 }: PropsWithChildren<{ span?: number }>) {
    return <Col md={span}>
        <PanelItemInner>{children}</PanelItemInner>
    </Col>;
};

const PanelItemInner = styled.div`
    padding: 0.375rem 0.375rem 0.375rem 0.75rem;
    line-height: 1.5;
    background-color: var(--bs-gray-100);
    display: flex;
    align-items: center;
    border: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color);
    border-radius: var(--bs-border-radius);

    ${ActionIcon} {
        visibility: hidden;
    }

    &:hover {
        ${ActionIcon} {
            visibility: visible;
        }
    }
`;
