import useModels from '@/hooks/use-models';
import { formatLong<PERSON><PERSON><PERSON>, styled, Tooltip } from '@topthink/common';
import { Fragment, useCallback, useEffect, useMemo, useRef } from 'react';
import {
    Button,
    Col,
    Dropdown,
    Form,
    FormControl,
    FormGroup,
    FormLabel,
    OverlayTrigger,
    Row,
    FormSelect,
    Stack
} from 'react-bootstrap';
import Param from './param';

export default function Model({ value, onChange }: BotAgentConfigurationUpdaterProps<'model'>) {

    const models = useModels<ChatModel>('chat');

    useEffect(() => {
        if (models.length > 0) {
            if (value.name) {
                //更新
                onChange(draft => {
                    const model = models.find((model) => model.code === value.name) || models[0];
                    if (model) {
                        draft.name = model.code;
                        draft.context_tokens = model.params?.context_tokens || 0;
                        draft.tool = model.params?.tool || false;
                    }
                });
            } else {
                const defaultModel = models[0];
                onChange(draft => {
                    draft.name = defaultModel.code;
                    draft.context_tokens = defaultModel.params?.context_tokens || 0;
                    draft.tool = defaultModel.params?.tool || false;
                });
            }
        }
    }, [models]);

    const current = useMemo(() => {
        const name = value.name;
        return models.find((model) => model.code === name);
    }, [models, value]);

    const onModelChange = useCallback((model: ChatModel) => {
        onChange(draft => {
            draft.name = model.code;
            draft.context_tokens = model.params?.context_tokens || 0;
            draft.tool = model.params?.tool || false;
            draft.vision = model.params?.vision || false;
            draft.thinking = model.params?.thinking?.is ? 'enabled' : 'disabled';
        });
    }, []);

    const onParamChange = useCallback(function(name: keyof Required<BotAgentConfiguration['model']>['params']) {
        return (value: number) => {
            onChange(draft => {
                if (!draft.params) {
                    draft.params = {};
                }
                draft.params[name] = value;
            });
        };
    }, []);

    const target = useRef<HTMLDivElement>(null);

    return <div ref={target}>
        <OverlayTrigger
            rootClose
            container={target}
            trigger={'click'}
            placement='bottom-end'
            overlay={(props) => {
                return <Modal
                    ref={props.ref}
                    style={props.style}
                    className='shadow-sm p-3 rounded bg-white'
                >
                    <Row>
                        <Col xs={12} className={'mb-2'}>
                            <FormGroup as={Row}>
                                <Header column xs={4}>模型</Header>
                                <Col xs={8}>
                                    <Dropdown>
                                        <Dropdown.Toggle variant={'light'} className={'w-100 d-flex align-items-center justify-content-between'}>
                                            <span className='d-flex align-items-center'>
                                                <i className='bi bi-x-diamond me-2' />
                                                <span className='lh-1'>{current?.label}</span>
                                                {current?.params?.tool && <Tooltip tooltip={'支持工具调用'}>
                                                    <Feature><i className='bi bi-tools' /></Feature>
                                                </Tooltip>}
                                                {current?.params?.vision && <Tooltip tooltip={'支持视觉功能'}>
                                                    <Feature><i className='bi bi-eye' /></Feature>
                                                </Tooltip>}
                                                {current?.params?.thinking?.is && <Tooltip tooltip={'支持深度思考'}>
                                                    <Feature><i className='bi bi-lightbulb' /></Feature>
                                                </Tooltip>}
                                            </span>
                                        </Dropdown.Toggle>
                                        <Dropdown.Menu className='w-100'>
                                            {models.map((model) => (
                                                <Dropdown.Item className='d-flex align-items-center' key={model.code} onClick={() => {
                                                    onModelChange(model);
                                                    return false;
                                                }}>
                                                    <span className='lh-1'>{model.label}</span>
                                                    {model.params?.tool && <Tooltip tooltip={'支持工具调用'}>
                                                        <Feature><i className='bi bi-tools' /></Feature>
                                                    </Tooltip>}
                                                    {model?.params?.vision && <Tooltip tooltip={'支持视觉功能'}>
                                                        <Feature><i className='bi bi-eye' /></Feature>
                                                    </Tooltip>}
                                                    {model?.params?.thinking?.is && <Tooltip tooltip={'支持深度思考'}>
                                                        <Feature><i className='bi bi-lightbulb' /></Feature>
                                                    </Tooltip>}
                                                </Dropdown.Item>
                                            ))}
                                        </Dropdown.Menu>
                                    </Dropdown>
                                </Col>
                            </FormGroup>
                        </Col>
                        {current && <Fragment key={current.code}>
                            <Col xs={12}>
                                <FormGroup as={Row} className={'align-items-center'}>
                                    <FormLabel column={true} xs={4}>
                                        <Stack direction={'horizontal'} gap={2}>
                                            费率
                                            <Tooltip tooltip={'实际消耗的 Token 数量将乘以此倍率，以`/`分割时则区分输入输出'}>
                                                <i className='bi bi-info-circle' />
                                            </Tooltip>
                                        </Stack>
                                    </FormLabel>
                                    <Col xs={8}>
                                        <FormControl plaintext readOnly defaultValue={current.factor} />
                                    </Col>
                                </FormGroup>
                            </Col>
                            <Col xs={12}>
                                <FormGroup as={Row} className={'align-items-center'}>
                                    <FormLabel column={true} xs={4}>上下文长度</FormLabel>
                                    <Col xs={8}>
                                        <FormControl plaintext readOnly defaultValue={formatLongNumber(current.params?.context_tokens || 0, 0)} />
                                    </Col>
                                </FormGroup>
                            </Col>
                            {current?.params?.vision && <Col xs={12}>
                                <FormGroup as={Row} className={'align-items-center'}>
                                    <FormLabel column={true} md={4}>
                                        <Stack direction={'horizontal'} gap={2}>
                                            视觉
                                            <Tooltip tooltip={'是否启用大模型的视觉功能。启用后，用户上传的图片文件将直接由大模型处理'}>
                                                <i className='bi bi-info-circle' />
                                            </Tooltip>
                                        </Stack>
                                    </FormLabel>
                                    <Col xs={8}>
                                        <Form.Check
                                            type='switch'
                                            checked={value.vision ?? true}
                                            onChange={e => {
                                                onChange(draft => {
                                                    draft.vision = e.target.checked;
                                                });
                                            }}
                                        />
                                    </Col>
                                </FormGroup>
                            </Col>}
                            {current?.params?.thinking?.is && current?.params?.thinking?.closable && <Col xs={12}>
                                <FormGroup as={Row} className={'align-items-center'}>
                                    <FormLabel column={true} md={4}>
                                        <Stack direction={'horizontal'} gap={2}>
                                            深度思考
                                            <Tooltip tooltip={'开启深度思考后，在输出最终回答之前，模型会先输出一段思维链内容，以提升最终答案的准确性。'}>
                                                <i className='bi bi-info-circle' />
                                            </Tooltip>
                                        </Stack>
                                    </FormLabel>
                                    <Col xs={4}>
                                        <FormSelect
                                            size={'sm'}
                                            value={value.thinking ?? 'enabled'}
                                            onChange={e => {
                                                onChange(draft => {
                                                    draft.thinking = e.target.value;
                                                });
                                            }}
                                        >
                                            <option value={'enabled'}>开启</option>
                                            <option value={'disabled'}>关闭</option>
                                            {current?.params?.thinking?.auto && <option value={'auto'}>自动</option>}
                                        </FormSelect>
                                    </Col>
                                </FormGroup>
                            </Col>}
                        </Fragment>}
                        <hr className={'my-2'} />
                        <Col xs={12} className={'mb-2'}>
                            <FormGroup as={Row}>
                                <Header column xs={4}>参数</Header>
                                <Col xs={8}></Col>
                            </FormGroup>
                        </Col>
                        <Col xs={12}>
                            <Param
                                label={'回复随机性'}
                                description={'采样温度，控制输出的随机性，值越大，会使输出更随机，更具创造性；值越小，输出会更加稳定或确定'}
                                value={value.params?.temperature ?? 0.3}
                                onChange={onParamChange('temperature')}
                                min={0}
                                max={1}
                                step={0.1}
                            />
                            <Param
                                label={'携带上下文轮数'}
                                description={'设置带入模型上下文的对话历史轮数。轮数越多，多轮对话的相关性越高，但消耗的 Token 也越多'}
                                value={value.params?.history_round ?? 5}
                                onChange={onParamChange('history_round')}
                                min={0}
                                max={30}
                            />
                        </Col>
                    </Row>
                </Modal>;
            }}
        >
            <Dropdown.Toggle as={ModelButton} variant={'light'} className={'d-flex align-items-center justify-content-between'}>
                <span className='d-flex align-items-center overflow-hidden'>
                    <i className='bi bi-x-diamond me-2' />
                    <span className='lh-1 text-truncate'>{current?.label}</span>
                    {value.tool && <Tooltip tooltip={'支持工具调用'}>
                        <Feature><i className='bi bi-tools' /></Feature>
                    </Tooltip>}
                    {value.vision && <Tooltip tooltip={'支持视觉功能'}>
                        <Feature><i className='bi bi-eye' /></Feature>
                    </Tooltip>}
                    {value.thinking !== 'disabled' && <Tooltip tooltip={'支持深度思考'}>
                        <Feature><i className='bi bi-lightbulb' /></Feature>
                    </Tooltip>}
                </span>
            </Dropdown.Toggle>
        </OverlayTrigger>
    </div>;
}

const Feature = styled.div`
    font-size: 10px;
    line-height: 1;
    background-color: hsla(0, 0%, 100%, .48);
    border: 1px solid var(--bs-border-color);
    border-radius: 5px;
    padding-left: .25rem;
    padding-right: .25rem;
    margin-left: .25rem;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgb(107 114 128);
`;

const Header = styled(FormLabel)`
    font-weight: bold;
    font-size: 1.2rem;
    line-height: 1.2;
`;

const Modal = styled.div`
    width: 480px;
    max-width: calc(100% - 2rem);
`;

const ModelButton = styled(Button)`
    min-width: 200px;
`;
