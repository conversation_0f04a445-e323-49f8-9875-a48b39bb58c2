import { useBot } from '@/pages/bot/provider';
import { Content, PanelGroup, RequestButton, styled, Toast } from '@topthink/common';
import isEqual from 'lodash/isEqual';
import merge from 'lodash/merge';
import { Col, Row, Stack } from 'react-bootstrap';
import Input from '../components/input';
import Onboarding from '../components/onboarding';
import Output from '../components/output';
import Suggestion from '../components/suggestion';
import useConfig from '../components/use-config';
import Variable from '../components/variable';
import Chat from './chat';
import Dataset from './dataset';
import Model from './model';
import Plugin from './plugin';
import Prompt from './prompt';
import Database from './database';

export default function Agent() {
    const { current, update, space } = useBot<BotAgentConfiguration>();

    const [config, updater] = useConfig<BotAgentConfiguration>(() => {
        return merge({
            model: {
                name: null
            },
            prompt: '',
            variable: {
                variables: []
            },
            dataset: {
                datasets: []
            },
            database: {
                databases: []
            },
            onboarding: {
                enable: false,
                prologue: '',
                questions: []
            },
            suggestion: {
                enable: false
            },
            tools: [],
            input: {
                speech: {
                    enable: false
                },
                file: {
                    enable: false,
                },
            },
            output: {
                speech: {
                    enable: false
                },
            }
        }, current.config);
    });

    return <Content extra={<Stack direction={'horizontal'} gap={2}>
        <Model {...updater('model')} />
        <div className='d-flex align-items-center'>
            <div className='vr fs-3' />
        </div>
        <RequestButton
            url={{
                method: 'post',
                url: `/space/${space.hash_id}/bot/${current.hash_id}/config`,
                data: {
                    config
                }
            }}
            onSuccess={() => {
                update({
                    ...current,
                    config
                });
                Toast.success('保存成功');
            }}
            disabled={isEqual(config, current.config)}
            className='px-4'
        >保存</RequestButton>
    </Stack>}>
        <Row className={'g-3 mb-3 mb-md-0'}>
            <Container md={6}>
                <PanelGroup>
                    <Prompt {...updater()} />
                    <Variable {...updater('variable')} />
                    {config.model.tool && <Dataset {...updater('dataset')} />}
                    {config.model.tool && <Database {...updater('database')} />}
                    {config.model.tool && <Plugin {...updater('tools')} />}
                    <Onboarding {...updater('onboarding')} />
                    <Suggestion {...updater('suggestion')} />
                    <Input {...updater('input')} />
                    <Output {...updater('output')} />
                </PanelGroup>
            </Container>
            <Container md={6}>
                <Chat config={config} />
            </Container>
        </Row>
    </Content>;
};

const Container = styled(Col)`
    min-height: calc(var(--100vh, 100vh) - 64px - 24px - 2rem);

    @media (min-width: 768px) {
        height: calc(var(--100vh, 100vh) - 64px - 2rem);
        overflow-y: auto;
    }

    @media (min-width: 1540px) {
        height: calc(var(--100vh, 100vh) - 64px - 24px - 2rem);
    }

    .card:last-child {
        margin-bottom: 0 !important;
    }

    hr {
        margin: .75rem 0;
        opacity: 0.15;
    }

    .fs-5 {
        font-size: 1.15rem !important;
        font-weight: bold;
    }

    .form-switch {
        padding-left: 2em;

        .form-check-input {
            margin-left: -2em;
        }
    }
`;
