import { useBot } from '@/pages/bot/provider';
import { MessageBox, MessageBoxType, VariableForm } from '@topthink/chat';
import { Button, Card, Space, styled, Tooltip } from '@topthink/common';
import { Fragment, useRef } from 'react';
import { Dropdown, Stack } from 'react-bootstrap';
import useRequest from '../components/use-request';
import useVariables from '../components/use-variables';

interface Props {
    config: BotAgentConfiguration;
}

export default function Chat({ config }: Props) {

    const { variable, onboarding, suggestion, output, input } = config;
    const { current } = useBot();
    const box = useRef<MessageBoxType>(null);

    const variables = useVariables(variable.variables);
    const request = useRequest(config);

    return <Container>
        <Stack direction='horizontal'>
            <div className='fs-5 me-auto'>调试与预览</div>
            <Space>
                {variables && <Dropdown>
                    <Tooltip tooltip={'设置变量'}>
                        <Dropdown.Toggle className={'no-caret'} variant='light' size='sm'>
                            <i className='bi bi-braces-asterisk' />
                        </Dropdown.Toggle>
                    </Tooltip>
                    <Variable className={'p-3'}>
                        <VariableForm
                            values={variables.values}
                            variables={variables.config}
                            onChange={({ formData }) => {
                                variables.setValues(formData);
                            }}
                        >
                            <Fragment />
                        </VariableForm>
                    </Variable>
                </Dropdown>}
                <Button size='sm' onClick={() => {
                    box.current?.reset();
                }} variant={'light'} tooltip={'重新开始'}>
                    <i className='bi bi-arrow-clockwise' />
                </Button>
            </Space>
        </Stack>
        <hr />
        <Body
            bot={current}
            logLevel={'all'}
            onboarding={onboarding}
            ref={box}
            speech={output.speech.enable ? output.speech : undefined}
            request={request}
            input={{
                variables,
                suggestion: suggestion.enable,
                fileTypes: input.file.enable ? (input.file.types || []) : undefined,
                speech: input.speech.enable ? input.speech : undefined
            }}
        />
    </Container>;
}

const Variable = styled(Dropdown.Menu)`
    width: 400px;
`;

const Body = styled(MessageBox)`
    flex: 1;
    overflow: hidden;
    margin: -.75rem -1rem -1rem;
`;

const Container = styled(Card)`
    height: 100%;
    margin-bottom: 0 !important;

    .card-body {
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }
`;
