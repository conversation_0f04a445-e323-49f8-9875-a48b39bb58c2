import { Row } from 'react-bootstrap';
import File from './file';
import Speech from './speech';
import { Panel } from '@topthink/common';
import { useBot } from '@/pages/bot/provider';
import FlowPanel from '../../flow/panel';

interface Props extends ConfigUpdaterProps<BotFeature, 'input'> {

}

export default function Input({ value, onChange }: Props) {
    const { current } = useBot();
    const As = current.type == 'flow' ? FlowPanel : Panel;

    return <As title={'输入'}>
        <Row className='g-2'>
            <Speech value={value} onChange={onChange} />
            <File value={value} onChange={onChange} />
        </Row>
    </As>;
}
