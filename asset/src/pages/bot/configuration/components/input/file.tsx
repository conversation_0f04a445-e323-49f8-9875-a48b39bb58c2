import { ActionIcon } from '@/components/action';
import PanelItem from '../panel-item';
import { SUPPORT_FILE_TYPES } from '@/utils/constants';
import { ModalForm, Tooltip } from '@topthink/common';
import { Form } from 'react-bootstrap';

export default function File({ value, onChange }: ConfigUpdaterProps<BotFeature, 'input'>) {

    const types = value.file.types && value.file.types.length > 0 ? value.file.types : SUPPORT_FILE_TYPES;

    return <PanelItem>
        <span className='me-auto'>
            文件输入
            <Tooltip placement={'top'} tooltip='支持向智能体发送文件类型数据，智能体将基于文件信息进行响应。'>
                <i className='bi bi-info-circle ms-2' />
            </Tooltip>
        </span>
        <ModalForm
            text={<i className='bi bi-sliders' />}
            modalProps={{ header: '设置' }}
            buttonProps={{ as: ActionIcon, className: 'ms-1 visible' }}
            formData={{
                types,
            }}
            schema={{
                type: 'object',
                properties: {
                    types: {
                        type: 'array',
                        title: '支持上传文件类型',
                        items: {
                            type: 'string',
                            enum: SUPPORT_FILE_TYPES
                        },
                        uniqueItems: true
                    },
                }
            }}
            uiSchema={{
                types: {
                    'ui:widget': 'checkboxes',
                    'ui:options': {
                        inline: true
                    }
                },
            }}
            onSubmit={({ formData }) => {
                onChange(draft => {
                    draft.file.types = formData.types;
                });
                return true;
            }}
        />
        <Form.Check
            className='ms-2'
            type='switch'
            checked={value.file.enable}
            onChange={(e) => onChange(draft => {
                draft.file.enable = e.target.checked;
            })}
        />
    </PanelItem>;
}
