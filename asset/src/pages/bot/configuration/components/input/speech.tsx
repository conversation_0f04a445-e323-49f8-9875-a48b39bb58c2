import { Form } from 'react-bootstrap';
import { ModalForm } from '@topthink/common';
import { ActionIcon } from '@/components/action';
import useModels from '@/hooks/use-models';
import PanelItem from '../panel-item';

export default function Speech({ value, onChange }: ConfigUpdaterProps<BotFeature, 'input'> ) {
    const models = useModels('audio');

    return <PanelItem>
        <span className='me-auto'>
            语音输入
        </span>
        <ModalForm
            text={<i className='bi bi-sliders' />}
            modalProps={{ header: '设置' }}
            buttonProps={{ as: ActionIcon, className: 'ms-1 visible' }}
            schema={{
                type: 'object',
                properties: {
                    model: {
                        type: 'string',
                        title: '语音模型',
                        enum: ['builtin', ...models.map(model => model.code)],
                        enumNames: ['浏览器内置', ...models.map(model => model.label)],
                    },
                }
            }}
            formData={{
                model: value.speech.model || 'builtin',
            }}
            uiSchema={{
                model: {
                    'ui:widget': 'typeahead',
                },
            }}
            onSubmit={({ formData }) => {
                onChange(draft => {
                    draft.speech.model = formData.model;
                });
                return true;
            }}
        />
        <Form.Check
            className='ms-2'
            type='switch'
            checked={value.speech.enable}
            onChange={(e) => onChange(draft => {
                draft.speech.enable = e.target.checked;
            })}
        />
    </PanelItem>;
}
