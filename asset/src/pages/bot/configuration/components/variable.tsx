import { FormSchema, FormUiSchema, ModalForm, Panel, Space, styled } from '@topthink/common';
import { ActionIcon, RemoveIcon } from '@/components/action';
import { useBot } from '@/pages/bot/provider';
import FlowPanel from '../flow/panel';

interface Props extends ConfigUpdaterProps<BotFeature, 'variable'> {

}

export default function Variable({ value, onChange }: Props) {
    const { current } = useBot();
    const formSchema: FormSchema = {
        type: 'object',
        properties: {
            type: {
                type: 'string',
                title: '类型',
                enum: ['text', 'textarea', 'select'],
                enumNames: ['文本', '段落', '下拉选项'],
                default: 'text'
            },
            key: {
                type: 'string',
                title: '变量名称'
            },
            label: {
                type: 'string',
                title: '显示名称'
            },
            required: {
                type: 'boolean',
                title: '必填'
            }
        },
        dependencies: {
            type: {
                oneOf: [
                    {
                        properties: {
                            type: {
                                const: 'select'
                            },
                            options: {
                                type: 'array',
                                title: '选项',
                                items: {
                                    type: 'string'
                                }
                            }
                        }
                    },
                ]
            }
        }
    };

    const uiSchema: FormUiSchema = {
        'ui:order': ['*', 'options', 'required'],
        type: {
            'ui:widget': 'radio',
            'ui:options': {
                button: true
            }
        },
        options: {
            items: {
                'ui:label': false
            }
        }
    };

    const As = current.type == 'flow' ? FlowPanel : Panel;
    const buttonAs = current.type == 'flow' ? ActionIcon : undefined;
    const text = current.type == 'flow' ? <i className='bi bi-plus-circle-fill' /> : <>
        <i className='bi bi-plus me-1' />添加</>;

    const action = <ModalForm
        buttonProps={{ as: buttonAs, size: 'sm', variant: 'light' }}
        modalProps={{ header: '添加变量' }}
        text={text}
        omitExtraData
        schema={formSchema}
        uiSchema={uiSchema}
        onSubmit={({ formData }) => {
            onChange(draft => {
                draft.variables.push(formData);
            });
            return true;
        }}
    />;

    return <As title='变量' action={action}>
        {value.variables.length > 0 ? <Container className='border rounded overflow-hidden'>
                <table className='table align-middle mb-0'>
                    <thead>
                    <tr>
                        <th>变量名称</th>
                        <th>显示名称</th>
                        <th>必填</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    {value.variables.map((variable, index) => {

                        const getIcon = () => {
                            switch (variable.type) {
                                case 'text':
                                    return <i className='text-muted bi bi-cursor-text' />;
                                case 'textarea':
                                    return <i className='text-muted bi bi-card-text' />;
                                case 'select':
                                    return <i className='text-muted bi bi-ui-checks' />;
                            }
                        };

                        return <tr key={index}>
                            <td>
                                <Space>
                                    {getIcon()}
                                    {variable.key}
                                </Space>
                            </td>
                            <td>{variable.label}</td>
                            <td>{variable.required && <i className='bi bi-check2 text-primary' />}</td>
                            <td>
                                <Space size={5}>
                                    <ModalForm
                                        text={<i className='bi bi-gear' />}
                                        modalProps={{ header: '编辑变量' }}
                                        buttonProps={{ as: ActionIcon }}
                                        formData={variable}
                                        schema={formSchema}
                                        uiSchema={uiSchema}
                                        onSubmit={({ formData }) => {
                                            onChange(draft => {
                                                draft.variables.splice(index, 1, formData);
                                            });
                                            return true;
                                        }}
                                    />
                                    <RemoveIcon onClick={() => {
                                        onChange(draft => {
                                            draft.variables.splice(index, 1);
                                        });
                                    }} />
                                </Space>
                            </td>
                        </tr>;
                    })}
                    </tbody>
                </table>
            </Container> :
            <div className='text-muted'>变量能使用户输入表单引入提示词</div>}
    </As>;
}

const Container = styled.div`
    font-size: 12px;

    thead {
        --bs-table-bg: var(--bs-gray-100);
        --bs-table-color: var(--bs-secondary);

        tr {
            th {
                &:nth-child(3) {
                    width: 50px;
                    text-align: center;
                }

                &:last-child {
                    width: 70px;
                }
            }
        }
    }

    tbody {
        tr {
            td {
                &:nth-child(3) {
                    text-align: center;
                }
            }

            &:last-child td {
                border-bottom: none;
            }
        }
    }
`;
