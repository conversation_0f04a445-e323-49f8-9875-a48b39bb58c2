import { Panel } from '@topthink/common';
import { Form } from 'react-bootstrap';
import { useBot } from '../../provider';
import FlowPanel from '../flow/panel';

export default function Suggestion({ value, onChange }: ConfigUpdaterProps<BotFeature, 'suggestion'>) {
    const { current } = useBot();
    const action = <Form.Check
        className={current.type == 'flow' ? 'fs-6 lh-1' : 'fs-5'}
        type='switch'
        checked={value.enable}
        onChange={(e) => onChange(draft => {
            draft.enable = e.target.checked;
        })}
    />;

    const As = current.type == 'flow' ? FlowPanel : Panel;

    return <As title='问题建议' eventKey='suggestion' action={action} open={value.enable}>
        <div className={'text-muted'}>在 智能体 回复后，自动根据对话内容提供 3 条用户提问建议</div>
    </As>;
}
