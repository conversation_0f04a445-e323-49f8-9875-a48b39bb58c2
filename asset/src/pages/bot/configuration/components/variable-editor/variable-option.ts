import { MenuOption } from '@lexical/react/LexicalTypeaheadMenuPlugin';
import { JSX } from 'react/jsx-runtime';

export interface Variable {
    key: string;
    label?: string;
    element?: JSX.Element;

    [x: string]: any;
}

export default class VariableOption extends MenuOption {
    label?: string;

    [x: string]: any;

    constructor({ key, ...properties }: Variable) {
        super(key);
        Object.assign(this, properties);
    }
}
