import { EditorState, LexicalEditor, TextNode } from 'lexical';
import { $createVariableNode } from './variable-node';
import { Variable } from './variable-option';

export function toEditorState(text: string) {
    const paragraph = (text || '').split('\n');

    return JSON.stringify({
        root: {
            children: paragraph.map((p) => {
                return {
                    children: [
                        {
                            detail: 0,
                            format: 0,
                            mode: 'normal',
                            style: '',
                            text: p,
                            type: 'text',
                            version: 1
                        }
                    ],
                    direction: 'ltr',
                    format: '',
                    indent: 0,
                    type: 'paragraph',
                    version: 1
                };
            }),
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1
        }
    });
}

export function fromEditorState(state: EditorState) {
    const text: string[] = [];
    const paragraphs = state.toJSON().root.children;
    paragraphs.forEach((paragraph: any) => {
        const children = paragraph.children;
        const paragraphText: string[] = [];
        children.forEach((child: any) => {
            if (child.type === 'linebreak') {
                paragraphText.push(`
`);
            } else if (child.text) {
                paragraphText.push(child.text);
            } else if (child.type === 'variable') {
                paragraphText.push(`{{${child.variable.key}}}`);
            }
        });
        text.push(paragraphText.join(''));
    });
    return text.join(`
`);
}

const getMatch = (text: string) => {
    const matches = /\{{2}([^{\s\/]+?)}{2}/i.exec(text);
    if (!matches) return null;
    const key = matches[1];

    const hashtagLength = key.length + 4;
    const startOffset = matches.index;
    const endOffset = startOffset + hashtagLength;
    return {
        key,
        end: endOffset,
        start: startOffset,
    };
};

export function registerLexicalTextEntity(editor: LexicalEditor, variables: Variable[]) {
    const textNodeTransform = (node: TextNode) => {
        if (!node.isSimpleText()) {
            return;
        }
        let text = node.getTextContent();
        let currentNode = node;
        let match;
        let offset = 0;

        while (text) {
            match = getMatch(text);
            text = match === null ? '' : text.slice(match.end);
            if (match === null) {
                return;
            }
            const key = match.key;
            const variable = variables.find((item) => item.key === key);
            if (!variable) {
                offset += match.end;
                continue;
            }

            let nodeToReplace;

            const start = match.start + offset;
            const end = match.end + offset;
            offset = 0;
            if (start === 0) {
                [nodeToReplace, currentNode] = currentNode.splitText(end);
            } else {
                [, nodeToReplace, currentNode] = currentNode.splitText(start, end);
            }
            const replacementNode = $createVariableNode(variable);
            replacementNode.setFormat(nodeToReplace.getFormat());
            nodeToReplace.replace(replacementNode);
            if (currentNode == null) {
                return;
            }
        }
    };

    const removePlainTextTransform = editor.registerNodeTransform(TextNode, textNodeTransform);

    return [removePlainTextTransform];
}
