import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import {
    LexicalTypeaheadMenuPlugin,
    MenuRenderFn as LexicalMenuRenderFn,
    TriggerFn
} from '@lexical/react/LexicalTypeaheadMenuPlugin';
import { mergeRegister } from '@lexical/utils';
import { $createTextNode, $getSelection, $isRangeSelection, TextNode } from 'lexical';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { Dropdown } from 'react-bootstrap';
import { createPortal } from 'react-dom';
import { registerLexicalTextEntity } from './utils';
import VariableOption, { Variable } from './variable-option';

export type MenuRenderFn = LexicalMenuRenderFn<VariableOption>;

interface Props {
    variables: Variable[];
    menuRenderFn?: MenuRenderFn;
}

export default function VariablePlugin(props: Props) {
    const { variables } = props;
    const [editor] = useLexicalComposerContext();

    const [queryString, setQueryString] = useState<string | null>(null);
    const checkForTriggerMatch = useBasicTypeaheadTriggerMatch('/', {
        minLength: 0
    });

    const onSelectOption = useCallback((selectedOption: VariableOption, nodeToRemove: TextNode | null, closeMenu: () => void) => {
        editor.update(() => {
            const selection = $getSelection();
            if (!$isRangeSelection(selection) || selectedOption == null) {
                return;
            }
            if (nodeToRemove) {
                nodeToRemove.remove();
            }
            selection.insertNodes([
                $createTextNode(`{{${selectedOption.key}}}`)
            ]);
            closeMenu();
        });
    }, [editor]);

    useEffect(() => {
        mergeRegister(...registerLexicalTextEntity(editor, variables));
    }, [editor, variables]);

    const menuRenderFn = useMemo<MenuRenderFn>(() => {
        if (props.menuRenderFn) {
            return props.menuRenderFn;
        }
        return (anchorElementRef, { options, selectedIndex, selectOptionAndCleanUp }) => {
            if (anchorElementRef.current == null || options.length === 0) {
                return null;
            }
            const menu = <Dropdown.Menu show>
                {options.map((option, index) => {
                    return <Dropdown.Item
                        key={index}
                        active={index === selectedIndex}
                        onClick={() => {
                            selectOptionAndCleanUp(option);
                        }}
                    >{option.label || option.key}</Dropdown.Item>;
                })}
            </Dropdown.Menu>;

            return createPortal(menu, anchorElementRef.current);
        };
    }, [props.menuRenderFn]);

    const options = useMemo(() => {
        const lowerCaseQuery = (queryString || '').toLowerCase();

        return variables.filter((item) => {
            const labelMatch = item.label?.toLowerCase().includes(lowerCaseQuery);
            const keyMatch = item.key.toLowerCase().includes(lowerCaseQuery);

            return labelMatch || keyMatch;
        }).map(item => new VariableOption(item));
    }, [variables, queryString]);

    return <LexicalTypeaheadMenuPlugin
        onQueryChange={setQueryString}
        onSelectOption={onSelectOption}
        triggerFn={checkForTriggerMatch}
        options={options}
        menuRenderFn={menuRenderFn}
    />;
}

const PUNCTUATION = '\\.,\\+\\*\\?\\$\\@\\|#{}\\(\\)\\^\\-\\[\\]\\\\/!%\'"~=<>_:;';

function useBasicTypeaheadTriggerMatch(
    trigger: string,
    { minLength = 1, maxLength = 75 }: { minLength?: number; maxLength?: number }
): TriggerFn {
    return useCallback(
        (text: string) => {
            const validChars = `[^${trigger}${PUNCTUATION}\\s]`;
            const TypeaheadTriggerRegex = new RegExp(
                `([^${trigger}]|^)(` + `[${trigger}]` + `((?:${validChars}){0,${maxLength}})` + ')$'
            );
            const match = TypeaheadTriggerRegex.exec(text);
            if (match !== null) {
                const maybeLeadingWhitespace = match[1];
                const matchingString = match[3];
                if (matchingString.length >= minLength) {
                    return {
                        leadOffset: match.index + maybeLeadingWhitespace.length,
                        matchingString,
                        replaceableString: match[2]
                    };
                }
            }
            return null;
        },
        [maxLength, minLength, trigger]
    );
}
