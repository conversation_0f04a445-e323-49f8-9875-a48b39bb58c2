import { InitialConfigType, LexicalComposer } from '@lexical/react/LexicalComposer';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { EditorRefPlugin } from '@lexical/react/LexicalEditorRefPlugin';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin';
import { PlainTextPlugin } from '@lexical/react/LexicalPlainTextPlugin';
import { styled } from '@topthink/common';
import { $createTextNode, $getRoot, $getSelection, EditorState, LexicalEditor } from 'lexical';
import { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { useDeepCompareEffect } from 'use-deep-compare';
import { fromEditorState, toEditorState } from './utils';
import { VariableNode } from './variable-node';
import { Variable } from './variable-option';
import VariablePlugin, { MenuRenderFn as RMenuRenderFn } from './variable-plugin';

export interface VariableEditorType {
    insertVariable(): void;
}

export type MenuRenderFn = RMenuRenderFn;

interface Props {
    value: string;
    onChange: (value: string) => void;
    placeholder: string;
    variables?: Variable[];
    menuRenderFn?: MenuRenderFn;
}

export const VariableEditor = forwardRef<VariableEditorType, Props>(({
    value,
    onChange,
    placeholder,
    variables,
    menuRenderFn
}, ref) => {
    const [text, setText] = useState(value);
    const [key, setKey] = useState(() => (new Date()).toISOString());
    const editorRef = useRef<LexicalEditor>(null);

    const initialConfig = useMemo<InitialConfigType>(() => {
        return {
            namespace: 'VariableEditor',
            nodes: [VariableNode],
            editorState: toEditorState(value),
            onError(error: Error) {
                throw error;
            },
        };
    }, [key]);

    useDeepCompareEffect(() => {
        setKey((new Date()).toISOString());
    }, [variables?.map(({ key, label, node }) => ({ key, label, node }))]);

    useEffect(() => {
        if (value !== text) {
            setText(value);
            setKey((new Date()).toISOString());
        }
    }, [value]);

    useImperativeHandle(ref, () => ({
        insertVariable() {
            editorRef.current?.update(() => {
                let selection = $getSelection();
                if (!selection) {
                    const root = $getRoot();
                    selection = root.selectEnd();
                }
                selection.insertNodes([$createTextNode('/')]);
            });
        }
    }), [editorRef]);

    const onChangeCallback = useCallback((editorState: EditorState) => {
        setText((prevText) => {
            const nextText = fromEditorState(editorState);
            if (nextText !== prevText) {
                requestAnimationFrame(() => {
                    onChange(nextText);
                });
            }
            return nextText;
        });
    }, [onChange, setText]);

    const contentEditable = <StyledContentEditable
        aria-placeholder={placeholder}
        placeholder={<Placeholder>{placeholder}，输入 '/' 插入变量</Placeholder>}
    />;

    return <Container key={key}>
        <LexicalComposer initialConfig={initialConfig}>
            <PlainTextPlugin
                contentEditable={contentEditable}
                ErrorBoundary={LexicalErrorBoundary}
            />
            <OnChangePlugin onChange={onChangeCallback} />
            <HistoryPlugin />
            {variables && <VariablePlugin menuRenderFn={menuRenderFn} variables={variables} />}
            <EditorRefPlugin editorRef={editorRef} />
        </LexicalComposer>
    </Container>;
});

export default VariableEditor;

const StyledContentEditable = styled(ContentEditable)`
    min-height: 120px;
    max-height: 400px;
    overflow-y: auto;
    resize: none;
    font-size: 1rem;
    position: relative;
    line-height: 1.5;
    outline: 0;
    padding: 0.375rem 0.75rem;
    color: var(--bs-body-color);
    appearance: none;
    background-color: var(--bs-body-bg);
    background-clip: padding-box;
    border: var(--bs-border-width) solid var(--bs-border-color);
    border-radius: var(--bs-border-radius);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

    &:focus {
        color: var(--bs-body-color);
        background-color: var(--bs-body-bg);
        border-color: rgb(157.5, 175.5, 255);
        outline: 0;
        box-shadow: 0 0 0 0.25rem rgba(60, 96, 255, 0.25);
    }

    p {
        margin-bottom: 0;
    }
`;

const Placeholder = styled.div`
    color: var(--bs-secondary-color);
    overflow: hidden;
    position: absolute;
    text-overflow: ellipsis;
    top: 0.375rem;
    left: 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    user-select: none;
    display: inline-block;
    pointer-events: none;
`;

const Container = styled.div`
    position: relative;
`;
