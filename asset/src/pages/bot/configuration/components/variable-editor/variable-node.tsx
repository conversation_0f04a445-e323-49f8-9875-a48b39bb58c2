import { styled } from '@topthink/common';
import {
    DecoratorNode,
    DOMConversionMap,
    DOMExportOutput,
    LexicalNode,
    NodeKey,
    SerializedLexicalNode,
    Spread,
    TextFormatType
} from 'lexical';
import { JSX } from 'react/jsx-runtime';
import { Variable } from './variable-option';

export type SerializedVariableLabelNode = Spread<
    {
        variable: Variable;
        format: number | TextFormatType;
    },
    SerializedLexicalNode
>;

export class VariableNode extends DecoratorNode<JSX.Element> {
    __variable: Variable;
    __format: number | TextFormatType;

    constructor(variable: Variable, format?: number | TextFormatType, key?: NodeKey) {
        super(key);
        this.__variable = variable;
        this.__format = format || 0;
    }

    static clone(node: VariableNode): VariableNode {
        return new VariableNode(node.__variable, node.__format, node.__key);
    }

    static getType(): string {
        return 'variable';
    }

    setFormat(format: number | TextFormatType): void {
        const self = this.getWritable();
        self.__format = format;
    }

    getFormat(): number | TextFormatType {
        return this.__format;
    }

    static importJSON(serializedNode: SerializedVariableLabelNode): VariableNode {
        const node = $createVariableNode(serializedNode.variable);

        node.setFormat(serializedNode.format);

        return node;
    }

    exportJSON(): SerializedVariableLabelNode {
        return {
            type: 'variable',
            version: 1,
            variable: this.__variable,
            format: this.__format || 0,
        };
    }

    createDOM(): HTMLElement {
        return document.createElement('span');
    }

    exportDOM(): DOMExportOutput {
        const element = document.createElement('span');
        return { element };
    }

    static importDOM(): DOMConversionMap | null {
        return {};
    }

    updateDOM(): false {
        return false;
    }

    getTextContent(
        _includeInert?: boolean | undefined,
        _includeDirectionless?: false | undefined
    ): string {
        return `{{${this.__variable.key}}}`;
    }

    decorate(): JSX.Element {
        const { element, label, key } = this.__variable;
        const children = element ? element : label || key;
        return <Variable>{children}</Variable>;
    }
}

const Variable = styled.span`
    border-radius: 5px;
    background: var(--bs-gray-200);
    padding: 0 0.375rem;
    color: var(--bs-secondary);
    margin: 0 0.125rem;
    line-height: calc(1.5rem - 2px);
    white-space: nowrap;
    display: inline-block;

    svg {
        vertical-align: -2px;
        display: inline-block;
    }
`;

export function $createVariableNode(variable: Variable) {
    return new VariableNode(variable);
}

export function $isVariableNode(node: VariableNode | LexicalNode | null | undefined): node is VariableNode {
    return node instanceof VariableNode;
}
