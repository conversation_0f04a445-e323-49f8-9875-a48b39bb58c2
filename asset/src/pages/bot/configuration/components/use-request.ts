import { request } from '@topthink/common';
import { useMemo } from 'react';
import { useBot } from '../../provider';

export default function useRequest(config: BotConfiguration) {
    const { current, space } = useBot();

    return useMemo(() => {
        const instance = request.create({
            baseURL: `space/${space.hash_id}/bot/${current.hash_id}/chat`,
        });
        instance.interceptors.request.use((request) => {
            if (!request.url) {
                request.data = {
                    ...request.data,
                    config
                };
            }
            return request;
        });
        return instance;
    }, [config, current, space]);
}
