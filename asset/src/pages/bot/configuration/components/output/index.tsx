import { Panel } from '@topthink/common';
import { Row } from 'react-bootstrap';
import Speech from './speech';
import FlowPanel from '../../flow/panel';
import { useBot } from '@/pages/bot/provider';

interface Props extends ConfigUpdaterProps<BotFeature, 'output'> {

}

export default function Output({ value, onChange }: Props) {
    const { current } = useBot();
    const As = current.type == 'flow' ? FlowPanel : Panel;

    return <As title={'输出'}>
        <Row className='g-2'>
            <Speech value={value} onChange={onChange} />
        </Row>
    </As>;
}
