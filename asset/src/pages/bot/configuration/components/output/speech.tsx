import { FormSchema, ModalForm } from '@topthink/common';
import { Form } from 'react-bootstrap';
import SpeakButton from './speak-button';
import { useState } from 'react';
import { useBot } from '../../../provider';
import { ActionIcon } from '@/components/action';
import useModels from '@/hooks/use-models';
import PanelItem from '../panel-item';

export default function Speech({ value, onChange }: ConfigUpdaterProps<BotFeature, 'output'>) {
    const { space, current } = useBot();
    const models = useModels<AudioModel>('audio');
    const [speech, setSpeech] = useState({ model: value.speech.model, voice: value.speech.voice });

    return <PanelItem>
        <span className='me-auto'>
            语音输出
        </span>
        <ModalForm
            text={<i className='bi bi-sliders' />}
            modalProps={{
                header: '设置',
                footer: ({ okButton, cancelButton }) => {
                    return <>
                        <SpeakButton
                            key={JSON.stringify(speech)}
                            url={`/space/${space.hash_id}/bot/${current.hash_id}/chat/speech`}
                            {...speech}
                        />
                        {cancelButton}
                        {okButton}
                    </>;
                }
            }}
            onChange={({ formData }) => {
                setSpeech({
                    model: formData.model,
                    voice: formData.voice
                });
            }}
            buttonProps={{ as: ActionIcon, className: 'ms-1 visible' }}
            omitExtraData={true}
            schema={{
                type: 'object',
                properties: {
                    model: {
                        type: 'string',
                        title: '语音模型',
                        enum: ['builtin', ...models.map(model => model.code)],
                        enumNames: ['浏览器内置', ...models.map(model => model.label)],
                    },
                    autoplay: {
                        type: 'boolean',
                        title: '自动播放',
                        default: false
                    }
                },
                required: ['model'],
                dependencies: {
                    model: {
                        oneOf: models.flatMap<FormSchema>((model) => {
                            if (model.params?.voice?.length) {
                                return [{
                                    properties: {
                                        model: {
                                            const: model.code
                                        },
                                        voice: {
                                            type: 'string',
                                            title: '音色',
                                            enum: model.params.voice.map(voice => voice.code),
                                            enumNames: model.params.voice.map(voice => voice.name),
                                        },
                                    },
                                    required: ['voice'],
                                }];
                            }
                            return [];
                        })
                    }
                }
            }}
            formData={{
                model: value.speech.model || 'builtin',
                voice: value.speech.voice,
                autoplay: value.speech.autoplay
            }}
            uiSchema={{
                'ui:order': ['model', '*', 'autoplay'],
                model: {
                    'ui:widget': 'typeahead'
                },
                voice: {
                    'ui:widget': 'typeahead'
                }
            }}
            onSubmit={({ formData }) => {
                onChange(draft => {
                    draft.speech.model = formData.model;
                    draft.speech.voice = formData.voice;
                    draft.speech.autoplay = formData.autoplay;
                });
                return true;
            }}
        />
        <Form.Check
            className='ms-2'
            type='switch'
            checked={value.speech.enable}
            onChange={(e) => onChange(draft => {
                draft.speech.enable = e.target.checked;
            })}
        />
    </PanelItem>;
}
