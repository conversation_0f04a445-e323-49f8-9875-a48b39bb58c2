import { useSynthesis } from '@topthink/chat';
import { Button, request, styled } from '@topthink/common';

interface Props {
    url: string;
    model?: string;
    voice?: string;
}

export default function SpeakButton({ url, model, voice }: Props) {
    const { playing, speaking, start, stop } = useSynthesis({
        text: '你好，很高兴认识你',
        loader: async (input) => {
            const res = await request({
                url,
                method: 'POST',
                data: {
                    input,
                    model,
                    voice
                }
            });

            return res.audio;
        }
    });

    return <Button
        loading={speaking}
        onClick={() => {
            playing ? stop() : start();
        }}
        variant={'light'}
        className={'me-auto'}>
        {playing ? <Icon className={'bi bi-volume-down-fill'} /> :
            <Icon className={'bi bi-volume-down'} />}
    </Button>;
}

const Icon = styled.i`
    &::before {
        transform: scale(1.4)
    }
`;
