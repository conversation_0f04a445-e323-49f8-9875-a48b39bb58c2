import { Col, Form, Row } from 'react-bootstrap';
import { Panel, styled, Tooltip } from '@topthink/common';
import { useMemo } from 'react';
import { RemoveIcon } from '@/components/action';
import { useBot } from '@/pages/bot/provider';
import FlowPanel from '../flow/panel';

export default function Onboarding({ value, onChange }: ConfigUpdaterProps<BotFeature, 'onboarding'>) {
    const { current } = useBot();
    const questions = useMemo(() => {
        const questions = [...value.questions];
        if (questions.length === 0 || questions[questions.length - 1]) {
            questions.push('');
        }
        return questions;
    }, [value.questions]);

    const As = current.type == 'flow' ? FlowPanel : Panel;

    const action = <Form.Switch
        className={current.type == 'flow' ? 'fs-6 lh-1' : 'fs-5'}
        checked={value.enable}
        onChange={(e) => onChange(draft => {
            draft.enable = e.target.checked;
        })}
    />;

    return <As title='开场白' eventKey='onboarding' action={action} open={value.enable}>
        <p>
            <textarea
                className='form-control'
                placeholder='在此处填写智能体的开场白'
                rows={3}
                value={value.prologue}
                onChange={(e) => onChange(draft => {
                    draft.prologue = e.target.value;
                })}
            />
        </p>
        <h6>
            开场问题
            <Tooltip placement={'top'} tooltip='填写超过 3 条时，将随机显示 3 条问题建议。'>
                <i className='bi bi-info-circle ms-2' />
            </Tooltip>
        </h6>
        <Row className={'g-2'}>
            {questions.map((question, index) => {
                return <Col md={12} key={index}>
                    <QuestionItem>
                        <input
                            value={question}
                            onChange={e => onChange(draft => {
                                if (e.target.value || draft.questions[index]) {
                                    draft.questions[index] = e.target.value;
                                }
                            })}
                            onBlur={e => {
                                onChange(draft => {
                                    if (!e.target.value.trim() && index < draft.questions.length - 1) {
                                        draft.questions.splice(index, 1);
                                    }
                                });
                            }}
                            className='form-control'
                            placeholder='输入开场白引导问题'
                        />
                        {index < questions.length - 1 && <RemoveIcon onClick={() => {
                            onChange(draft => {
                                draft.questions.splice(index, 1);
                            });
                        }} />}
                    </QuestionItem>
                </Col>;
            })}
        </Row>
    </As>;
}

const QuestionItem = styled.div`
    position: relative;

    ${RemoveIcon} {
        position: absolute;
        right: .375rem;
        top: 50%;
        transform: translateY(-50%);
        visibility: hidden;
    }

    &:hover {
        ${RemoveIcon} {
            visibility: visible;
        }
    }
`;
