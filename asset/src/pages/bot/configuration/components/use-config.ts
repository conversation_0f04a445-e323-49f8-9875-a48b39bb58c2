import { Draft, useImmer } from '@topthink/common';
import { useCallback } from 'react';

export default function useConfig<C>(defaultConfig: C | (() => C)): [C, ConfigUpdater<C>, (args: C | ((draft: Draft<C>) => void)) => void] {
    const [config, setConfig] = useImmer<C>(defaultConfig);

    const updater = useCallback<ConfigUpdater<C>>(function <T extends keyof C>(name?: T) {
        if (name) {
            return {
                value: config[name],
                onChange: (callback: (value: C[T]) => void | C[T]) => {
                    setConfig(draft => {
                        // @ts-ignore
                        const result = callback(draft[name]);
                        if (result) {
                            // @ts-ignore
                            draft[name] = result;
                        }
                    });
                }
            } as ConfigUpdaterProps<C>;
        }
        return {
            value: config,
            onChange: (callback: (value: Draft<C>) => void | C) => {
                setConfig(callback);
            }
        } as ConfigUpdaterProps<C>;
    }, [config]);

    return [config, updater, setConfig];
}
