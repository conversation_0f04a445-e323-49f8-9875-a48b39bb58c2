import { Variable } from '@topthink/chat';
import { useEffect, useMemo, useState } from 'react';

export default function useVariables(config: Variable[]) {

    const getVars = () => {
        return config.reduce<Record<string, string>>((acc, cur) => {
            let defaultValue = '';
            if (cur.type === 'select' && cur.options && cur.options.length > 0) {
                defaultValue = cur.options[0];
            }
            acc[cur.key] = defaultValue;
            return acc;
        }, {});
    };

    const [vars, setVars] = useState<Record<string, string>>(getVars);

    useEffect(() => {
        setVars(getVars);
    }, [config]);

    return useMemo(() => {
        if (config.length > 0) {
            return {
                config,
                values: vars,
                setValues: setVars,
            };
        }
    }, [config, vars]);
}
