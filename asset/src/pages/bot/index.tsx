import {
    <PERSON><PERSON>,
    <PERSON>,
    CardList,
    CardListType,
    Content,
    css,
    Link, LinkButton,
    RequestButton,
    Result,
    styled
} from '@topthink/common';
import CreateBotModal from '@/components/create-bot-modal';
import { useCurrentSpace } from '@/components/space-provider';
import { Dropdown, Overlay } from 'react-bootstrap';
import { useRef, useState } from 'react';
import LevelAccess from '@/components/level-access';
import { DEVELOPER } from '@/utils/constants';
import { ReactComponent as AgentIcon } from '@/images/agent.svg';
import { ReactComponent as FlowIcon } from '@/images/flow.svg';

const BotItem = function({ bot, list }: { bot: Bot, list: CardListType | null }) {
    const { current } = useCurrentSpace();
    const [show, setShow] = useState(false);
    const ref = useRef(null);
    const target = useRef(null);

    return <BotItemContainer ref={ref}>
        <LevelAccess level={DEVELOPER} fallback={<Card>
            <BotTitle>
                <BotAvatar>
                    <img src={bot.avatar} />
                    {bot.type == 'agent' && <BotIcon className={'rounded-1 bg-success'}>
                        <AgentIcon />
                    </BotIcon>}
                    {bot.type == 'flow' && <BotIcon className={'rounded-1 bg-primary'}>
                        <FlowIcon />
                    </BotIcon>}
                </BotAvatar>
                <BotName>{bot.name}</BotName>
            </BotTitle>
            <BotDesc>{bot.description || '暂无描述'}</BotDesc>
            <div className={'d-grid mt-3'}>
                <LinkButton to={`/space/${current.hash_id}/bot/${bot.hash_id}`} variant={'light'} size='sm'>对话</LinkButton>
            </div>
        </Card>}>
            <Link to={`/space/${current.hash_id}/bot/${bot.hash_id}/configuration`}>
                <Card>
                    <BotTitle>
                        <BotAvatar>
                            <img src={bot.avatar} />
                            {bot.type == 'agent' && <BotIcon className={'rounded-1 bg-success'}>
                                <AgentIcon />
                            </BotIcon>}
                            {bot.type == 'flow' && <BotIcon className={'rounded-1 bg-primary'}>
                                <FlowIcon />
                            </BotIcon>}
                        </BotAvatar>
                        <BotName>{bot.name}</BotName>
                        <BotAction
                            onMouseOver={() => setShow(true)}
                            onMouseLeave={() => setShow(false)}
                            ref={target}
                            onClick={e => e.preventDefault()}
                            $active={show}
                        >
                            <i className='bi bi-three-dots' />
                        </BotAction>
                    </BotTitle>
                    <BotDesc>{bot.description || '暂无描述'}</BotDesc>
                    <div className={'d-grid mt-3'}>
                        <LinkButton to={`/space/${current.hash_id}/bot/${bot.hash_id}`} variant={'light'} size='sm'>对话</LinkButton>
                    </div>
                </Card>
            </Link>
        </LevelAccess>
        <Overlay
            show={show}
            target={target}
            placement='bottom-start'
            container={ref}
        >
            {(props) => {
                return <BotActionMenu
                    ref={props.ref}
                    style={props.style}
                    onMouseOver={() => setShow(true)}
                    onMouseLeave={() => setShow(false)}
                >
                    <Dropdown.Menu className={'position-static'} show={!!props.style.transform}>
                        <Dropdown.Item as={Link} to={`/space/${current.hash_id}/bot/${bot.hash_id}/configuration`}>编排</Dropdown.Item>
                        <RequestButton
                            method={'delete'}
                            url={`/space/${current.hash_id}/bot/${bot.hash_id}`}
                            className={'text-danger'}
                            as={Dropdown.Item}
                            confirm={'删除智能体将无法撤销。用户将不能访问你的智能体，所有编排配置和日志均将一并被删除。'}
                            onSuccess={() => list?.reload()}
                        >删除</RequestButton>
                    </Dropdown.Menu>
                </BotActionMenu>;
            }}
        </Overlay>
    </BotItemContainer>;
};

export const Component = function() {
    const { current } = useCurrentSpace();
    const list = useRef<CardListType>(null);
    return <Content extra={<CreateBotModal button={Button} />}>
        <CardList
            columns={4}
            ref={list}
            source={`/space/${current.hash_id}/bot`}
            renderItem={(item) => {
                return <BotItem list={list.current} bot={item} />;
            }}
            renderEmpty={() => {
                return <Card>
                    <Result
                        extra={<div>
                            <h4>构建属于你的第一个智能体</h4>
                            <p className='text-muted mb-4'>发挥大模型和插件能力快速构建智能体</p>
                            <p><CreateBotModal button={Button} /></p>
                        </div>}
                    />
                </Card>;
            }}
        />
    </Content>;
};

const BotActionMenu = styled.div`
    z-index: 1;
`;

const BotDesc = styled.div`
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    color: var(--bs-secondary);
`;

const BotAction = styled.div<{ $active: boolean }>`
    display: flex;
    height: 24px;
    width: 24px;
    align-items: center;
    justify-content: center;
    margin-top: -1rem;
    border-radius: 4px;
    color: var(--bs-gray-700);

    ${({ $active }) => $active && css`
        background-color: var(--bs-gray-300);
    `};

    &:hover {
        background-color: var(--bs-gray-300);
    }
`;

const BotName = styled.div`
    font-size: 1.25rem;
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
`;
const BotIcon = styled.div`
    position: absolute;
    right: -.25rem;
    bottom: -.25rem;
    width: 1rem;
    height: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;

    svg {
        width: .75rem;
        height: .75rem;
        color: #FFF;
    }
`;

const BotAvatar = styled.div`
    width: 2.5rem;
    height: 2.5rem;
    position: relative;

    img {
        border-radius: .5rem;
        width: 100%;
        height: 100%;
    }
`;

const BotTitle = styled.div`
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
`;

const BotItemContainer = styled.div`
    display: flex;
    width: 100%;

    & > a {
        display: flex;
        width: 100%;
    }

    .card {
        width: 100%;
    }

    &:hover {
        color: inherit;

        .card {
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
        }
    }
`;
