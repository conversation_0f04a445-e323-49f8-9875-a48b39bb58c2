import Back from '@/components/back';
import EditBotModal from '@/pages/bot/edit-modal';
import { styled } from '@topthink/common';
import SiderLayout from '@/components/sider-layout';
import { useBot } from '@/pages/bot/provider';

export const Component = () => {
    const { space, current, update } = useBot();

    const header = <Container>
        <Back to={`/space/${space.hash_id}/bot`} />
        <Avatar>
            <img src={current.avatar} />
        </Avatar>
        <span className='text-truncate me-auto'>{current.name}</span>
        <EditBotModal bot={current} onSuccess={update}>
            <i className='bi bi-pencil-fill' />
        </EditBotModal>
    </Container>;

    return <SiderLayout logo={header} />;
};


const Avatar = styled.div`
    width: 2rem;
    height: 2rem;
    border-radius: .5rem;
    overflow: hidden;
    flex-shrink: 0;

    img {
        width: 100%;
        height: 100%;
    }
`;

const Container = styled.div`
    display: flex;
    flex: 1;
    gap: 8px;
    min-width: 0;
    align-items: center;
    font-size: 16px;
`;
