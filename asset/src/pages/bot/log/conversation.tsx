import Form from '@/pages/bot/annotation/form';
import { useBot } from '@/pages/bot/provider';
import { MessageBox } from '@topthink/chat';
import { RequestButton, useRequest } from '@topthink/common';
import Icon from '../configuration/flow/workflow/nodes/icon';
import { isNodeEnum } from '../configuration/flow/workflow/utils';

interface Props {
    id: string;
}

export default function Conversation({ id }: Props) {
    const { current, space } = useBot();

    const { result } = useRequest(`/space/${space.hash_id}/bot/${current.hash_id}/conversation/${id}/message`);

    if (!result) {
        return null;
    }

    return <MessageBox
        bot={current}
        conversation={{
            id,
            messages: result
        }}
        nodeIconResolver={(type) => {
            if (isNodeEnum(type)) {
                return <Icon type={type} bg={false} color />;
            }
            return null;
        }}
        logLevel={'all'}
        actions={({ Component, message, content, setMessage }) => {
            if (message.annotation) {
                const annotation = message.annotation;
                return <Form
                    action={`/space/${space.hash_id}/bot/${current.hash_id}/annotation/${annotation.id}`}
                    method={'PUT'}
                    text={<i className='bi bi-bookmark-check text-primary' />}
                    buttonProps={{
                        as: function(props) {
                            return <Component {...props} tooltip={'编辑标注'} />;
                        }
                    }}
                    modalProps={{
                        header: '编辑标注',
                        footer: ({ okButton, cancelButton, action }) => {
                            return <>
                                <RequestButton
                                    variant={'danger'}
                                    className={'me-auto'}
                                    confirm={'确定要删除吗？'}
                                    url={`/space/${space.hash_id}/bot/${current.hash_id}/annotation/${annotation.id}`}
                                    method={'delete'}
                                    onSuccess={() => {
                                        setMessage(draft => {
                                            draft.annotation = undefined;
                                        });
                                        action.close();
                                    }}
                                >删除</RequestButton>
                                {cancelButton}
                                {okButton}
                            </>;
                        }
                    }}
                    formData={{
                        question: annotation.question,
                        answer: annotation.answer,
                    }}
                    onSuccess={(annotation) => {
                        setMessage(draft => {
                            draft.annotation = annotation;
                        });
                    }}
                />;
            } else if (message.id) {
                return <Form
                    action={`/space/${space.hash_id}/bot/${current.hash_id}/annotation`}
                    method={'POST'}
                    text={<i className='bi bi-bookmark-check' />}
                    buttonProps={{
                        as: function(props) {
                            return <Component {...props} tooltip={'添加标注'} />;
                        }
                    }}
                    modalProps={{ header: '添加标注' }}
                    formData={{
                        question: message.query,
                        answer: content,
                        message_id: message.id,
                    }}
                    onSuccess={(annotation) => {
                        setMessage(draft => {
                            draft.annotation = annotation;
                        });
                    }}
                />;
            }
        }}
    />;
}
