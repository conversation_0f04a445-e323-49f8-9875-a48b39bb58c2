import { Content, Offcanvas, styled, Table } from '@topthink/common';
import { useBot } from '@/pages/bot/provider';
import { useState } from 'react';
import Conversation from '@/pages/bot/log/conversation';

export const Component = function() {
    const { current, space } = useBot();

    const [id, setId] = useState<string>();

    return <Content>
        <Table
            source={`/space/${space.hash_id}/bot/${current.hash_id}/conversation`}
            columns={[
                {
                    title: '时间',
                    dataIndex: 'create_time',
                    width: 150
                },
                {
                    title: '标题',
                    dataIndex: 'title',
                    render({ value }) {
                        return value || '新对话';
                    }
                },
                {
                    title: '消息数',
                    dataIndex: 'messages_count',
                    align: 'center',
                    width: 100
                },
                {
                    title: '来源',
                    dataIndex: 'source_label',
                    align: 'center',
                    width: 100
                }
            ]}
            onRow={(data) => {
                return {
                    onClick: () => {
                        setId(data.id);
                    },
                    role: 'button'
                };
            }}
        />
        <Offcanvas
            show={!!id}
            onHide={() => {
                setId(undefined);
            }}
            placement={'end'}
            header={'对话日志'}
            style={{
                width: '100%',
                maxWidth: '640px'
            }}
            bodyAs={Container}
        >
            {id && <Conversation id={id} />}
        </Offcanvas>
    </Content>;
};

const Container = styled.div`
    padding: 0;
    overflow: hidden;
    background: rgb(250, 250, 250);
`;
