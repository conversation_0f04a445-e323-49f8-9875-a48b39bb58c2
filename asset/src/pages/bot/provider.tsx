import { Outlet, useParams, useRequest } from '@topthink/common';
import { useCurrentSpace } from '@/components/space-provider';
import { createContext, useCallback, useContext } from 'react';

interface BotContextType<T extends BotConfiguration = BotConfiguration> {
    current: Bot<T>;
    space: Space;
    refresh: () => Promise<void>;
    update: (bot: Bot) => void;
}

const BotContext = createContext<BotContextType | null>(null);

export function useBot<T extends BotConfiguration>(): BotContextType<T> {
    const context = useContext(BotContext);
    if (!context) {
        throw new Error('useBot must be used within a BotProvider');
    }
    return context as BotContextType<T>;
}

export const Component = function() {
    const { current } = useCurrentSpace();
    const { bot: botId } = useParams();
    const { result: bot, refresh, merge } = useRequest<Bot>(`/space/${current.hash_id}/bot/${botId}`, {
        setLoading(state) {
            return {
                ...state,
                loading: true,
            };
        }
    });

    const update = useCallback((bot: Bot) => {
        merge({ result: bot });
    }, [merge]);

    if (!bot) {
        return null;
    }

    return <BotContext.Provider value={{ current: bot, space: current, refresh, update }}>
        <Outlet />
    </BotContext.Provider>;
};
