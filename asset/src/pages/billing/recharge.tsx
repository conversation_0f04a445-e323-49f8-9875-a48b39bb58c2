import {
    Button,
    Card,
    css,
    request,
    Result,
    showRequestError,
    styled,
    useImmer,
    useNavigate,
    useRequestData,
    useSearchParams,
    waitPayComplete
} from '@topthink/common';
import { Col, FormControl, FormLabel, Row } from 'react-bootstrap';
import { useMemo, useState, useEffect } from 'react';
import { useCurrentSpace } from '@/components/space-provider';
import dayjs from 'dayjs';

export const Component = function() {
    const { current, refresh } = useCurrentSpace();
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const { services } = useRequestData<{ services: Record<string, Service> }>();
    const [service, setService] = useState(() => {
        const serviceParam = searchParams.get('service');
        return serviceParam && services[serviceParam] ? serviceParam : Object.keys(services)[0];
    });

    // 监听URL参数变化，自动选择服务
    useEffect(() => {
        const serviceParam = searchParams.get('service');
        if (serviceParam && services[serviceParam]) {
            setService(serviceParam);
        }
    }, [searchParams, services]);

    const [nums, setNums] = useImmer(() => {
        return Object.fromEntries(Object.entries(services).map(([key]) => {
            return [key, 1];
        }));
    });

    const currentService = services[service];

    const price = useMemo(() => {
        if (currentService.temporary) {
            // 临时扩容不按月计算
            return currentService.price * nums[service];
        } else {
            // 常规服务按月计算
            const month = Math.max(1, dayjs(current.expire_time).diff(dayjs(), 'month'));
            return currentService.price * nums[service] * month;
        }
    }, [currentService, nums]);

    const onSubmit = async () => {
        try {
            const result = await request({
                method: 'POST',
                url: `/space/${current.hash_id}/billing/recharge`,
                data: {
                    service,
                    nums: nums[service]
                }
            });
            waitPayComplete({
                result,
                checkUrl: `/space/${current.hash_id}/billing/check`,
                onComplete() {
                    refresh();
                    navigate('../../setting/overview');
                }
            });
        } catch (e) {
            showRequestError(e);
        }
    };

    const canRecharge = useMemo(() => {
        const now = dayjs();
        let expireDate = dayjs(current.expire_time);
        const isExpired = expireDate.isBefore(now);

        return !isExpired && current.plan !== 'trial';
    }, [current]);

    return <Card>
        {canRecharge ? <Row>
            <Col md={8}>
                <Row className='g-3'>
                    <Col md={12}>
                        <FormLabel className='fs-5 mb-3'>增值项目</FormLabel>
                        <ServiceBox>
                            {Object.entries(services).map(([key, item]) => {
                                return <ServiceItem key={key} $active={service === key} onClick={() => setService(key)}>
                                    <input className='form-check-input' type='radio' readOnly checked={service === key} />
                                    <div className='d-flex flex-column gap-1'>
                                        <div className='d-flex align-items-center gap-2'>
                                            <div className='text-nowrap fw-bold'>{item.name}</div>
                                            {'step' in item && <>
                                                <FormControl
                                                    type='number'
                                                    min={item.step}
                                                    step={item.step}
                                                    value={nums[key] * item.step}
                                                    onChange={(e) => {
                                                        const value = Number(e.target.value);
                                                        setNums((draft) => {
                                                            draft[key] = Math.max(1, Math.floor(value / item.step));
                                                        });
                                                    }}
                                                />
                                                <div className='text-nowrap fw-bold'>
                                                    {item.unit}{item.temporary ? '' : '/月'}
                                                </div>
                                            </>}
                                        </div>
                                        {item.description && <div className='text-muted fs-7'>{item.description}</div>}
                                    </div>
                                    <div className='text-black-50 ms-auto'>
                                        ￥<span className='fs-3 fw-bold text-dark'>{item.price * nums[key]}</span>
                                        {item.temporary ? '' : ' / 月'}
                                    </div>
                                </ServiceItem>;
                            })}
                            <div className='text-muted'>支付成功后，您的增值项目将立即正式生效</div>
                        </ServiceBox>
                    </Col>
                </Row>
            </Col>
            <Col md={4}>
                <div className={'d-flex flex-column border rounded p-3 h-100'}>
                    <h5 className='mb-3'>购买方案</h5>
                    <p className='d-flex justify-content-between text-muted mb-2'>
                        <span>空间名称</span><span>{current.name}</span>
                    </p>
                    <p className='d-flex justify-content-between text-muted mb-2'>
                        <span>购买服务</span><span>{currentService.name}</span>
                    </p>
                    <p className='d-flex justify-content-between text-muted mb-2'>
                        <span>有效期</span>
                        <span className='fw-bold'>{dayjs(current.expire_time).format('YYYY-MM-DD')}</span>
                    </p>
                    <hr />
                    <h5 className='mb-3'>订单详情</h5>
                    <p className='d-flex justify-content-between text-muted mb-2'>
                        <span>
                            {currentService.name}
                            {'step' in currentService && ` [${nums[service] * currentService.step}${currentService.unit}]`}
                        </span>
                        <span>￥{nums[service] * currentService.price}元/月</span>
                    </p>
                    <hr />
                    <h5 className='mb-3 d-flex justify-content-between'>
                        <span>应付金额</span>
                        <span>￥{price}</span>
                    </h5>
                    <Button className={'w-100 mt-auto'} size={'lg'} onClick={onSubmit}>支付订单</Button>
                </div>
            </Col>
        </Row> : <Result status='warning' title='当前订阅计划不支持购买增值服务' />}
    </Card>;
};

const ServiceItem = styled.div<{ $active?: boolean }>`
    border-radius: 6px;
    background-color: #f8f9fd;
    padding: 1.25rem 1rem;
    cursor: pointer;
    border: 1px solid transparent;
    display: flex;
    align-items: center;
    gap: 1rem;

    &:hover {
        border: 1px solid #fec86d;
    }

    .form-control {
        &:focus {
            border-color: #fec86d;
            box-shadow: 0 0 0 0.25rem rgba(117, 47, 6, 0.15);
        }
    }

    .form-check-input {
        font-size: 18px;
        margin-top: 0;

        &:checked {
            background-color: #752f06;
            border-color: #752f06;
        }

        &:focus {
            box-shadow: 0 0 0 0.25rem rgba(117, 47, 6, 0.25);
        }
    }

    ${props => props.$active && css`
        background: linear-gradient(332deg, #ffebc2, #ffd189);
        border-radius: 6px;
        border: 2px solid #fec86d !important;

        .form-control {
            border-color: rgba(117, 47, 6, 0.1);
        }

        .text-black-50 {
            color: rgba(117, 47, 6, .68) !important;

            .text-dark {
                color: #752f06 !important;
            }
        }
    `}
`;

const ServiceBox = styled.div`
    display: flex;
    flex-direction: column;
    gap: 1rem;
    min-height: 450px;
`;
