import {
    But<PERSON>,
    Card,
    css,
    request,
    showRequestError,
    styled,
    Tooltip,
    useImmer,
    useNavigate,
    useRequestData,
    waitPayComplete
} from '@topthink/common';
import { Badge, Col, FormCheck, FormLabel, Row } from 'react-bootstrap';
import { useMemo, useState } from 'react';
import { useCurrentSpace } from '@/components/space-provider';
import dayjs from 'dayjs';
import Qrcode from '../../images/qrcode.png';

export const Component = function() {
    const { current, refresh } = useCurrentSpace();
    const navigate = useNavigate();
    const [mode, setMode] = useState<'month' | 'year'>('month');
    const { plans, services } = useRequestData<{ plans: Record<string, Plan>, services: Record<string, Service> }>();
    const [plan, setPlan] = useState(() => {
        if (plans[current.plan]?.price) {
            return current.plan;
        }
        return 'standard';
    });
    const [service, setService] = useImmer(() => {
        return Object.fromEntries(Object.entries(services).map(([key]) => {
            return [key, key in current && !!current[key]];
        }));
    });

    const nextDate = useMemo(() => {
        const now = dayjs();
        let expireDate = dayjs(current.expire_time);
        const isExpired = expireDate.isBefore(now);

        if (isExpired) {
            return now.add(1, mode);
        }

        const price = plans[current.plan]?.price;
        const nextPrice = plans[plan]?.price;

        if (price && nextPrice && current.plan !== plan) {
            const days = expireDate.diff(now, 'day');
            const nextDays = Math.ceil(days * price / nextPrice);
            expireDate = now.add(nextDays, 'day');
        }

        return expireDate.add(1, mode);
    }, [current, plan, mode]);

    const canChangeService = dayjs(current.expire_time).diff(dayjs(), 'day') <= 7;
    const currentPlan = plans[plan];

    const price = useMemo(() => {
        if (currentPlan.price) {
            const price = Object.entries(services).reduce((price, [key, item]) => {
                if (!service[key]) {
                    return price;
                }
                const nums = key in current ? Number(current[key]) : 0;
                return price + item.price * nums;
            }, currentPlan.price);

            return price * (mode === 'month' ? 1 : 10);
        }
        return 0;
    }, [currentPlan, current, services, service, mode]);

    const onSubmit = async () => {
        try {
            const result = await request({
                method: 'POST',
                url: `/space/${current.hash_id}/billing/subscribe`,
                data: {
                    plan,
                    mode,
                    service: canChangeService ? service : undefined
                }
            });
            waitPayComplete({
                result,
                checkUrl: `/space/${current.hash_id}/billing/check`,
                onComplete() {
                    refresh();
                    navigate('../../setting/overview');
                }
            });
        } catch (e) {
            showRequestError(e);
        }
    };

    return <Card>
        <Row>
            <Col md={8}>
                <Row className='g-3'>
                    <Col md={12}>
                        <FormLabel className='fs-5 mb-3'>订阅方式</FormLabel>
                        <div className={'d-flex gap-3'}>
                            <FormCheck
                                id='mode-month'
                                name='mode'
                                type={'radio'}
                                label={'按月订阅'}
                                inline
                                checked={mode === 'month'}
                                onChange={() => setMode('month')}
                            />
                            <FormCheck
                                id='mode-year'
                                name='mode'
                                type={'radio'}
                                label={<span className={'d-flex align-items-center gap-2'}>
                                    按年订阅<Badge>赠送2个月</Badge>
                                </span>}
                                inline
                                checked={mode === 'year'}
                                onChange={() => setMode('year')}
                            />

                        </div>
                    </Col>
                    <Col md={12}>
                        <FormLabel className='fs-5 mb-3'>订阅计划</FormLabel>
                        <Plans>
                            {Object.entries(plans).map(([key, value]) => {
                                if (!value.price && value.rights) {
                                    return null;
                                }
                                return <PlanItem key={key} $active={plan === key} onClick={() => setPlan(key as any)}>
                                    <PlanItemName>{value.name}</PlanItemName>
                                    {value.price ? <PlanItemPrice>
                                            <span className='fs-5 me-1'>￥</span>
                                            {value.price * (mode === 'month' ? 1 : 10)}
                                        </PlanItemPrice> :
                                        <PlanItemPrice><span className='fs-4'>联系我们</span></PlanItemPrice>}
                                </PlanItem>;
                            })}
                        </Plans>
                        {currentPlan.rights && <>
                            <Rights>
                                <RightsTitle>
                                    <h5>{currentPlan.name} 权益</h5>
                                    <h6>{currentPlan.slogan}</h6>
                                </RightsTitle>
                                <ul>
                                    <li>
                                        <span>Token</span>
                                        <span>{currentPlan.rights.token}K/月</span>
                                    </li>
                                    <li>
                                        <span>智能体数量</span>
                                        <span>{currentPlan.rights.bot}</span>
                                    </li>
                                    <li>
                                        <span>团队成员</span>
                                        <span>{currentPlan.rights.member}</span>
                                    </li>
                                    <li>
                                        <span>外嵌小部件</span>
                                        <span><i className='bi bi-check2' /></span>
                                    </li>
                                    <li>
                                        <span>知识库数量</span>
                                        <span>{currentPlan.rights.dataset}</span>
                                    </li>
                                    <li>
                                        <span>数据库数量</span>
                                        <span>{currentPlan.rights.database}</span>
                                    </li>
                                    <li>
                                        <span>集成三方通道</span>
                                        <span><i className='bi bi-check2' /></span>
                                    </li>
                                    <li>
                                        <span>自定义插件数量</span>
                                        <span>{currentPlan.rights.plugin}</span>
                                    </li>
                                    <li>
                                        <span>空间 API</span>
                                        <span>{currentPlan.rights.api ? <i className='bi bi-check2'></i> :
                                            <i className='bi bi-x-lg'></i>}</span>
                                    </li>
                                    <li>
                                        <span>对话记录保存</span>
                                        <span>{currentPlan.rights.history}天</span>
                                    </li>
                                </ul>
                            </Rights>
                            <div className='text-muted'>支付成功后，您的
                                [<span className='fw-bold'>{currentPlan.name}</span>]
                                新订阅计划以及<span className='fw-bold'>增值项目的调整</span>将立即正式生效
                            </div>
                        </>}
                    </Col>
                </Row>
            </Col>
            <Col md={4}>
                {currentPlan.price ? <div className={'d-flex flex-column border rounded p-3 h-100'}>
                    <h5 className='mb-3'>购买方案</h5>
                    <p className='d-flex justify-content-between text-muted mb-2'>
                        <span>空间名称</span><span>{current.name}</span>
                    </p>
                    <p className='d-flex justify-content-between text-muted mb-2'>
                        <span>购买套餐</span><span>{currentPlan.name}</span>
                    </p>
                    <p className='d-flex justify-content-between text-muted mb-2'>
                        <span>有效期</span><span className='fw-bold'>{nextDate.format('YYYY-MM-DD')}</span>
                    </p>
                    <hr />
                    <h5 className='mb-3'>订单详情</h5>
                    <p className='d-flex justify-content-between text-muted mb-2'>
                        <span>{currentPlan.name}</span>
                        <span>￥{currentPlan.price * (mode === 'month' ? 1 : 10)}/{mode === 'month' ? '月' : '年'}</span>
                    </p>
                    {Object.entries(services).map(([key, item]) => {
                        const nums = key in current ? Number(current[key]) : 0;
                        return nums > 0 && <div className='d-flex text-muted mb-2' key={key}>
                            <FormCheck
                                disabled={!canChangeService}
                                checked={service[key]}
                                onChange={(e) => {
                                    setService(draft => {
                                        draft[key] = e.target.checked;
                                    });
                                }}
                                id={`service-${key}`}
                                className='me-2'
                            />
                            {canChangeService ? <label htmlFor={`service-${key}`}>
                                {item.name}
                                {'step' in item && ` [${nums * item.step}${item.unit}]`}
                            </label> : <Tooltip tooltip='仅在空间订阅计划到期 7 天内续费时可以取消'>
                                <label htmlFor={`service-${key}`}>
                                    {item.name}
                                    {'step' in item && ` [${nums * item.step}${item.unit}]`}
                                </label>
                            </Tooltip>}
                            <span className='ms-auto'>
                                ￥{nums * item.price * (mode === 'month' ? 1 : 10)}/{mode === 'month' ? '月' : '年'}
                            </span>
                        </div>;
                    })}
                    <hr />
                    <h5 className='mb-3 d-flex justify-content-between'>
                        <span>应付金额</span>
                        <span>￥{price}</span>
                    </h5>
                    <Button className={'w-100 mt-auto'} size={'lg'} onClick={onSubmit}>支付订单</Button>
                </div> : <div className='d-flex align-items-center justify-content-center h-100'>
                    <img src={Qrcode} width={200} height={200} />
                </div>}
            </Col>
        </Row>
    </Card>;
};

const RightsTitle = styled.div`
    display: flex;
    align-items: center;

    h5 {
        font-size: 14px;
        font-weight: 600;
        color: #752f06;
    }

    h6 {
        font-size: 12px;
        font-weight: 400;
        color: rgba(117, 47, 6, .68);
        margin-left: 10px;
    }
`;

const Rights = styled.div`
    background: #f8f9fd;
    margin-top: 20px;
    padding: 20px 20px 15px 20px;
    border-radius: 10px;
    position: relative;
    margin-bottom: 1rem;

    ul {
        display: flex;
        flex-flow: wrap;
        justify-content: space-between;
        padding: 0;
        margin: 10px 0 0;

        li {
            width: 38%;
            display: flex;
            justify-content: space-between;
            font-size: 13px;
            color: #752f06;
            padding: 4px 0;
            margin: 0;

            span:nth-child(2) {
                color: rgba(117, 47, 6, .68);
            }
        }
    }
`;

const PlanItemName = styled.div`
    margin-top: 28px;
    font-size: 16px;
    color: rgba(37, 48, 68, .84);
`;

const PlanItemPrice = styled.div`
    font-size: 30px;
    color: #626262;
    font-weight: 600;
    margin-top: 4px;
`;

const PlanItem = styled.div<{ $active?: boolean }>`
    flex: 1;
    border-radius: 6px;
    border: 1px solid transparent;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    background: #f8f9fd;
    height: 130px;
    padding-bottom: 30px;
    position: relative;

    &:hover {
        border: 1px solid #fec86d;
    }

    ${({ $active }) => $active && css`
        color: #fec86d;
        border: 1px solid #fec86d;
        background: linear-gradient(332deg, #ffebc2, #ffd189);

        ${PlanItemName} {
            color: #893f03;
        }

        ${PlanItemPrice} {
            color: #752f06;
        }
    `}
`;

const Plans = styled.div`
    display: flex;
    gap: 25px;
`;
