import { useSpace } from '@/components/space-provider';
import { Link, styled } from '@topthink/common';
import { Dropdown } from 'react-bootstrap';
import SiderLayout from '@/components/sider-layout';
import CreateSpaceModal from '@/components/create-space-modal';
import Footer from './footer';

export const Component = function() {
    const { current, spaces } = useSpace();

    if (!current) {
        return null;
    }

    const header = <Header>
        <Dropdown drop={'end'}>
            <Dropdown.Toggle as={Toggle}>
                <span className='text-truncate flex-fill'>{current.name}</span>
                <i className='bi bi-chevron-expand fs-6' />
            </Dropdown.Toggle>
            <Dropdown.Menu>
                {spaces.map((item) => {
                    return <Dropdown.Item key={item.hash_id} as={Link} to={`/space/${item.hash_id}`}>
                        <i className='bi bi-boxes me-2' />{item.name}
                    </Dropdown.Item>;
                })}
                <Divider />
                <CreateSpaceModal
                    button={Dropdown.Item}
                    text={<><i className='bi bi-plus-square me-2' />创建空间</>}
                />
            </Dropdown.Menu>
        </Dropdown>
    </Header>;

    const footer = <Footer />;

    return <SiderLayout
        header={header}
        footer={footer}
    />;
};

const Divider = styled(Dropdown.Divider)`
    &:last-child {
        display: none;
    }
`;

const Toggle = styled.div`
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    font-size: 16px;
    padding: 8px 8px 8px 24px;

    &:hover {
        background-color: rgb(240, 240, 240);
    }

    &:after {
        display: none !important;
    }
`;

const Header = styled.div`
    padding-top: 10px;

    .dropdown-menu {
        max-width: 230px;

        .dropdown-item {
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
`;
