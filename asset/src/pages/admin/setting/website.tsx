import { Card, Form, Loader, useRequest } from '@topthink/common';

export const Component = () => {
    const { result } = useRequest('/admin/setting/website');
    if (!result) {
        return <Loader />;
    }
    return <Card>
        <Form
            formData={result}
            action={'/admin/setting/website'}
            submitText={'保存'}
            schema={{
                type: 'object',
                properties: {
                    title: {
                        type: 'string',
                        title: '网站名称'
                    },
                    logo: {
                        type: 'string',
                        title: '网站LOGO'
                    },
                    help: {
                        type: 'string',
                        title: '帮助文档'
                    },
                    qrcode: {
                        type: 'string',
                        title: '交流社群'
                    },
                    scripts: {
                        type: 'string',
                        title: '自定义脚本'
                    },
                }
            }}
            uiSchema={{
                logo: {
                    'ui:widget': 'avatar',
                    'ui:options': {
                        endpoint: '/upload/internal',
                    }
                },
                help: {
                    'ui:help': '帮助文档链接，以 http[s]:// 开头'
                },
                qrcode: {
                    'ui:widget': 'avatar',
                    'ui:options': {
                        endpoint: '/upload/internal',
                    }
                },
                scripts: {
                    'ui:widget': 'textarea',
                    'ui:options': {
                        rows: 5
                    }
                }
            }}
        />
    </Card>;
};
