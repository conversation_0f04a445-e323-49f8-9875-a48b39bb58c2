import { Card, Form, Loader, Result, useRequest } from '@topthink/common';
import { useManifest } from '@/components/manifest-provider';

export const Component = () => {
    const cloud = useManifest('cloud');

    if (cloud) {
        return <Card>
            <Result status={'info'} title={'由顶想云托管'} />
        </Card>;
    }

    const { result } = useRequest('/admin/setting/login');

    if (!result) {
        return <Loader />;
    }


    return <Card>
        <Form
            method={'post'}
            action={'/admin/setting/login'}
            formData={result}
            schema={{
                type: 'object',
                properties: {
                    qrcode: {
                        type: 'boolean',
                        title: '扫码登录',
                        description: '由顶想云提供的微信扫码登录'
                    },
                    wechat: {
                        type: 'object',
                        title: '微信登录',
                        properties: {
                            enable: {
                                title: '开启',
                                type: 'boolean'
                            },
                        },
                        dependencies: {
                            enable: {
                                oneOf: [
                                    {
                                        properties: {
                                            enable: {
                                                const: true
                                            },
                                            client_id: {
                                                type: 'string',
                                                title: 'AppID'
                                            },
                                            client_secret: {
                                                type: 'string',
                                                title: 'AppSecret'
                                            },
                                        }
                                    }
                                ]
                            }
                        }
                    },
                    qq: {
                        type: 'object',
                        title: 'QQ登录',
                        properties: {
                            enable: {
                                title: '开启',
                                type: 'boolean'
                            },
                        },
                        dependencies: {
                            enable: {
                                oneOf: [
                                    {
                                        properties: {
                                            enable: {
                                                const: true
                                            },
                                            client_id: {
                                                type: 'string',
                                                title: 'AppID'
                                            },
                                            client_secret: {
                                                type: 'string',
                                                title: 'AppSecret'
                                            },
                                        }
                                    }
                                ]
                            }
                        }
                    },
                    github: {
                        type: 'object',
                        title: 'GitHub登录',
                        properties: {
                            enable: {
                                title: '开启',
                                type: 'boolean'
                            },
                        },
                        dependencies: {
                            enable: {
                                oneOf: [
                                    {
                                        properties: {
                                            enable: {
                                                const: true
                                            },
                                            client_id: {
                                                type: 'string',
                                                title: 'AppID'
                                            },
                                            client_secret: {
                                                type: 'string',
                                                title: 'AppSecret'
                                            },
                                        }
                                    }
                                ]
                            }
                        }
                    }
                }
            }}
        />
    </Card>;
};
