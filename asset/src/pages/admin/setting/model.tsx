import { Card, Form, Loader, useRequest } from '@topthink/common';

const getModels = <M extends Model = Model>(models: M[], callback: (model: M) => boolean) => {
    return models.filter(model => {
        return callback(model);
    }).map(model => ({ label: model.label, value: model.code }));
};

export const Component = () => {
    const { result } = useRequest('/admin/setting/model');
    const { result: models } = useRequest('/admin/model');
    if (!result || !models) {
        return <Loader />;
    }

    return <Card>
        <Form
            method={'post'}
            action={'/admin/setting/model'}
            formData={result}
            schema={{
                type: 'object',
                properties: {
                    chat: {
                        title: '对话模型',
                        type: 'array',
                        items: {
                            type: 'string',
                        }
                    },
                    secondary: {
                        title: '辅助模型',
                        type: 'string',
                        description: '主要用于会话标题自动生成、问题建议等场景'
                    },
                    audio: {
                        title: '语音模型',
                        type: 'array',
                        items: {
                            type: 'string',
                        }
                    },
                    embedding: {
                        title: '向量模型',
                        type: 'string',
                    },
                    rerank: {
                        title: '排序模型',
                        type: 'string',
                        description: '可选,留空则不对知识库召回文档排序'
                    }
                }
            }}
            uiSchema={{
                chat: {
                    'ui:widget': 'typeahead',
                    'ui:options': {
                        async: false,
                        enumOptions: getModels(models, model => model.type == 'chat'),
                        filter: true,
                    }
                },
                secondary: {
                    'ui:widget': 'typeahead',
                    'ui:options': {
                        enumOptions: getModels(models, model => model.type == 'chat'),
                        async: false,
                    }
                },
                audio: {
                    'ui:widget': 'typeahead',
                    'ui:options': {
                        enumOptions: getModels(models, model => model.type == 'audio'),
                        async: false,
                        filter: true,
                    }
                },
                embedding: {
                    'ui:widget': 'typeahead',
                    'ui:options': {
                        enumOptions: getModels<TextModel>(models, model => model.type == 'text' && !!model.factor.embedding),
                        async: false,
                    }
                },
                rerank: {
                    'ui:widget': 'typeahead',
                    'ui:options': {
                        enumOptions: getModels<TextModel>(models, model => model.type == 'text' && !!model.factor.rerank),
                        async: false,
                    }
                },
            }}
        />
    </Card>;
};
