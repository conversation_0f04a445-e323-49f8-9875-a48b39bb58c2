import { Dropdown } from 'react-bootstrap';
import { ModalForm } from '@topthink/common';
import { useManifest } from '@/components/manifest-provider';

interface Props {
    space: Space;
    onSuccess?: () => void;
}

export default function PlanSetting({ space, onSuccess }: Props) {
    const { cloud } = useManifest();

    return <ModalForm
        action={`/admin/space/${space.id}/plan`}
        text={'版本设置'}
        buttonProps={{ as: Dropdown.Item }}
        onSuccess={onSuccess}
        formData={{
            plan: space.plan,
            expire_time: space.expire_time,
            token: space.token
        }}
        schema={{
            type: 'object',
            required: ['plan'],
            properties: {
                plan: {
                    type: 'string',
                    title: '版本',
                    enum: [
                        'trial',
                        'standard',
                        'professional',
                        'enterprise'
                    ],
                    default: 'trail'
                },
                expire_time: {
                    type: 'string',
                    format: 'date-time',
                    title: '有效期'
                },
                token: {
                    type: 'number',
                    title: '额外Token额度'
                }
            }
        }}
        uiSchema={{
            plan: {
                'ui:widget': cloud ? undefined : 'hidden',
                'ui:options': {
                    enumNames: ['体验版', '基础版', '专业版', '企业版'],
                },
            },
            expire_time: {
                'ui:widget': cloud ? undefined : 'hidden',
            },
        }}
    />;
}
