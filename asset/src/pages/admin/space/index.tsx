import { Content, formatLongNumber, Space, Table } from '@topthink/common';
import { Dropdown } from 'react-bootstrap';
import getUserAvatar from '@/utils/get-user-avatar';
import PlanSetting from '@/pages/admin/space/plan-setting';

export const Component = () => {
    return <Content>
        <Table
            source={`/admin/space`}
            search={true}
            sync={true}
            tableLayout={'fixed'}
            columns={[
                {
                    title: 'ID',
                    dataIndex: 'id',
                    width: 60
                },
                {
                    title: '名称',
                    dataIndex: 'name',
                },
                {
                    title: '创始人',
                    width: 150,
                    dataIndex: 'owner',
                    render({ value }) {
                        return <Space>
                            <img className='rounded-circle' width={24} height={24} src={getUserAvatar(value)} />
                            {value.name}
                        </Space>;
                    }
                },
                {
                    title: '版本',
                    width: 80,
                    dataIndex: 'plan_name',
                },
                {
                    title: '额外Token额度',
                    width: 150,
                    dataIndex: 'token',
                    align: 'center',
                    render({ value }) {
                        return formatLongNumber(value);
                    }
                },
                {
                    title: '有效期',
                    width: 150,
                    dataIndex: 'expire_time',
                    render({ record }) {
                        return record.expire_time ? record.expire_time : '--';
                    }
                },
                {
                    title: '创建时间',
                    dataIndex: 'create_time',
                    width: 150
                },
                {
                    title: '操作',
                    key: 'action',
                    align: 'right',
                    width: 60,
                    render({ action, record }) {
                        return <Dropdown align={'end'}>
                            <Dropdown.Toggle
                                className={'border-0 no-caret'}
                                variant={'outline-secondary'}
                            ><i className='bi bi-three-dots-vertical' /></Dropdown.Toggle>
                            <Dropdown.Menu className={'shadow'}>
                                <PlanSetting space={record} onSuccess={action.reload} />
                            </Dropdown.Menu>
                        </Dropdown>;
                    }
                }
            ]}
        />
    </Content>;
};
