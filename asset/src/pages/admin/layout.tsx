import { SiderLayout, styled } from '@topthink/common';
import Back from '@/components/back';
import AdminAccess from '@/components/admin-access';

export const Component = () => {
    return <AdminAccess>
        <SiderLayout
            top={0}
            headerAs={function({ children }) {
                return <Header>
                    <Back to={'/'} />{children}
                </Header>;
            }}
        />
    </AdminAccess>;
};

const Header = styled.div`
    height: 65px;
    padding: 1rem;
    border-bottom: 1px solid #e3e3e3;
    display: flex;
    align-items: center;
    gap: .5rem;
`;
