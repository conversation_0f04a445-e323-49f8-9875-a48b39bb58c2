import formatPeriodDate from '@/utils/format-period-date';
import { Line, LineConfig } from '@ant-design/plots';
import { Loader, useRequest } from '@topthink/common';

interface Props {
    period: string;
}

export default function UserChart({ period }: Props) {

    const { result } = useRequest<DataItem[]>({
        url: '/admin/statistic/user',
        params: { period }
    }, {
        refreshDeps: [period]
    });

    if (!result) {
        return <Loader />;
    }

    const lineConfig: LineConfig = {
        data: result,
        height: 250,
        xField: 'date',
        yField: 'value',
        meta: {
            value: {
                alias: '新增用户',
            }
        },
        xAxis: {
            label: {
                formatter(text) {
                    return formatPeriodDate(period, text);
                }
            },
        },
        smooth: true,
    };

    return <Line {...lineConfig} />;
}
