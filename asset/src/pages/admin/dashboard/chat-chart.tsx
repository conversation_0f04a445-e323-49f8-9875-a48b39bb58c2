import { Loader, useRequest } from '@topthink/common';
import { DualAxes, DualAxesConfig } from '@ant-design/plots';
import formatPeriodDate from '@/utils/format-period-date';

interface ChatData {
    conversation: DataItem[];
    message: DataItem[];
}

interface Props {
    period: string;
    url: string;
}

export default function ChatChart({ period, url }: Props) {
    const { result } = useRequest<ChatData>({
        url,
        params: { period }
    }, {
        refreshDeps: [period, url]
    });

    if (!result) {
        return <Loader />;
    }

    const config: DualAxesConfig = {
        data: [result.message.map((v) => ({
            date: v.date,
            message: Number(v.value),
        })), result.conversation.map((v) => ({
            date: v.date,
            conversation: Number(v.value),
        }))],
        height: 250,
        xField: 'date',
        yField: ['message', 'conversation'],
        meta: {
            message: {
                alias: '消息数',
            },
            conversation: {
                alias: '会话数'
            }
        },
        xAxis: {
            label: {
                formatter(text) {
                    return formatPeriodDate(period, text);
                },
            },
        },
        geometryOptions: [
            {
                geometry: 'column',
            },
            {
                geometry: 'line',
                smooth: true,
            },
        ],
    };


    return <DualAxes {...config} />;
}
