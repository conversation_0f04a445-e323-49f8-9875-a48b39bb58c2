import { formatL<PERSON><PERSON><PERSON><PERSON>, Loader, NumberFormat, Statistic, useRequest } from '@topthink/common';
import { Col, Row } from 'react-bootstrap';

export default function Basic() {
    const { result } = useRequest<BasicStatistics>('/admin/statistic/basic');

    if (!result) {
        return <Loader />;
    }

    return <Row className={'g-3 mb-3'}>
        <Col>
            <Statistic
                title={'用户'}
                content={
                    <NumberFormat value={result.user.total} currency={false} options={{ minimumFractionDigits: 0 }} />
                }
                footer={
                    <span>昨日新增 {result.user.yesterday}</span>
                }
            />
        </Col>
        <Col>
            <Statistic
                title={'空间'}
                content={
                    <NumberFormat value={result.space.total} currency={false} options={{ minimumFractionDigits: 0 }} />
                }
                footer={
                    <>昨日新增 <NumberFormat value={result.space.yesterday} currency={false} options={{ minimumFractionDigits: 0 }} /></>
                }
            />
        </Col>
        <Col>
            <Statistic
                title={'智能体'}
                content={
                    <NumberFormat value={result.bot.total} currency={false} options={{ minimumFractionDigits: 0 }} />
                }
                footer={
                    <span>昨日新增 {result.bot.yesterday}</span>
                }
            />
        </Col>
        <Col>
            <Statistic
                title={'当前版本'}
                content={result.license.plan}
                footer={
                    <span>Tokens余额：{result.license.tokens === null ? '--' : formatLongNumber(result.license.tokens)}</span>
                }
            />
        </Col>
    </Row>;
}
