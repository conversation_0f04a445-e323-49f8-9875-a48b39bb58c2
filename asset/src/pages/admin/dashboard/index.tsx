import PeriodButtons from '@/components/period-buttons';
import { Card, Content, Loader, styled } from '@topthink/common';
import { Suspense, useState } from 'react';
import { Nav } from 'react-bootstrap';
import Basic from './basic';
import ChatChart from './chat-chart';
import UserChart from './user-chart';

export const Component = () => {
    const [period, setPeriod] = useState<string>('30days');
    const [tab, setTab] = useState('user');
    const charts = {
        user: <UserChart period={period} />,
        chat: <ChatChart period={period} url={'/admin/statistic/chat'} />,
    };

    return <Content>
        <Basic />
        <Card>
            <Nav variant='tabs' defaultActiveKey={tab} onSelect={key => key && setTab(key)}>
                <Nav.Item>
                    <Nav.Link eventKey='user'>用户</Nav.Link>
                </Nav.Item>
                <Nav.Item>
                    <Nav.Link eventKey='chat'>对话</Nav.Link>
                </Nav.Item>
                <StyledPeriodButtons periods={['30days', 'last-month', '6months', '1year']} period={period} onChange={setPeriod} />
            </Nav>
            <Container>
                <Suspense fallback={<Loader />}>
                    {charts[tab as keyof typeof charts]}
                </Suspense>
            </Container>
        </Card>
    </Content>;
};

const Container = styled.div`
    padding: 2rem .25rem 0;
    position: relative;
`;

const StyledPeriodButtons = styled(PeriodButtons)`
    position: absolute;
    right: 1rem;

    .btn {
        line-height: 1.2;
    }
`;
