import { Badge } from 'react-bootstrap';

interface Props {
    value: number;
}

export default function Type({ value }: Props) {

    let bg, text;

    switch (value) {
        case 1:
            bg = 'primary';
            text = 'ThinkAI';
            break;
        case 2:
            bg = 'secondary';
            text = 'Builtin';
            break;
        case 3:
            bg = 'info';
            text = 'Custom';
            break;
    }

    return <Badge bg={bg} className='ms-2'>
        {text}
    </Badge>;
}
