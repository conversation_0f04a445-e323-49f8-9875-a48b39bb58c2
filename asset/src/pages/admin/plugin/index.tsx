import { Content, ModalForm, RequestButton, Space, Table } from '@topthink/common';
import { Badge, Stack } from 'react-bootstrap';
import Type from './type';


export const Component = () => {
    return <Content>
        <Table
            source={`/admin/plugin`}
            columns={[
                {
                    dataIndex: 'name',
                    title: '名称',
                    render({ record }) {
                        return <Stack direction={'horizontal'} gap={3}>
                            <img className={'rounded'} src={record.icon} width={30} height={30} />
                            <Stack gap={1}>
                                <span className='link-dark'>
                                    {record.title}
                                    <Type value={record.type} />
                                </span>
                                <span className='fs-7 text-muted'>{record.description || '暂无描述'}</span>
                            </Stack>
                        </Stack>;
                    }
                },
                {
                    dataIndex: 'sort',
                    title: '排序',
                    width: 80,
                    align: 'center',
                    render({ value, record, action }) {
                        return <ModalForm
                            action={`/admin/plugin/${record.id}/sort`}
                            method={'post'}
                            buttonProps={{ size: 'sm' }}
                            text={value}
                            modalProps={{ header: '设置排序' }}
                            formData={{ sort: record.sort || 0 }}
                            schema={{
                                type: 'object',
                                properties: {
                                    sort: {
                                        type: 'number',
                                        title: '排序值',
                                        description: '数值越大排序越靠前'
                                    }
                                },
                                required: ['sort']
                            }}
                            uiSchema={{
                                sort: {
                                    'ui:autofocus': true,
                                    'ui:placeholder': '请输入排序值'
                                }
                            }}
                            onSuccess={() => action.reload()}
                        />;
                    }
                },
                {
                    dataIndex: 'status',
                    title: '状态',
                    width: 80,
                    align: 'center',
                    render({ value }) {
                        return value === 1 ?
                            <Badge bg={'success'}>启用</Badge> :
                            <Badge bg={'secondary'}>禁用</Badge>;
                    }
                },
                {
                    dataIndex: 'create_time',
                    title: '创建时间',
                    width: 150,
                },
                {
                    key: 'action',
                    title: '操作',
                    width: 150,
                    align: 'right',
                    render({ record, action }) {
                        return <Space>
                            <RequestButton
                                className={record.status === 1 ? 'text-danger' : 'text-primary'}
                                url={{
                                    url: `/admin/plugin/${record.id}/status`,
                                    method: 'post',
                                    data: { status: record.status === 1 ? 0 : 1 }
                                }}
                                onSuccess={() => action.reload()}
                            >{record.status === 1 ? '禁用' : '启用'}</RequestButton>
                        </Space>;
                    }
                },
            ]}
        />
    </Content>;
};
