import { Content, request, showRequestError, Space, Table } from '@topthink/common';
import { Badge, Dropdown } from 'react-bootstrap';
import { ReactNode } from 'react';
import getUserAvatar from '@/utils/get-user-avatar';
import { useManifest } from '@/components/manifest-provider';

export const Component = () => {
    const { cloud } = useManifest();

    return <Content>
        <Table
            source={`/admin/user`}
            columns={[
                {
                    title: 'ID',
                    dataIndex: 'id',
                    width: 60
                },
                {
                    title: '昵称',
                    dataIndex: 'name',
                    render({ record }) {
                        return <Space>
                            <img className='rounded-circle' width={24} height={24} src={getUserAvatar(record)} />
                            {record.name}
                            {(record.status & 4) > 0 && <Badge>管理员</Badge>}
                        </Space>;
                    }
                },
                {
                    title: '注册时间',
                    dataIndex: 'create_time',
                    width: 150
                },
                {
                    title: '操作',
                    key: 'action',
                    align: 'right',
                    width: 150,
                    render({ record, action }) {
                        const status = 4;
                        const value = record.status;
                        const menus: ReactNode[] = [];

                        if (!cloud) {
                            menus.push(<Dropdown.Item key={'admin'} onClick={async () => {
                                try {
                                    await request({
                                        method: 'post',
                                        url: `/admin/user/${record.id}/status`,
                                        data: {
                                            status: (value & status) > 0 ? value & ~status : value | status
                                        }
                                    });

                                    action.reload(true);
                                } catch (e) {
                                    showRequestError(e);
                                }
                            }}>{(value & status) > 0 ? '取消管理员' : '设为管理员'}</Dropdown.Item>);
                        }

                        return <Dropdown align={'end'}>
                            <Dropdown.Toggle
                                className={'border-0 no-caret'}
                                variant={'outline-secondary'}
                            ><i className='bi bi-three-dots-vertical' /></Dropdown.Toggle>
                            <Dropdown.Menu className={'shadow'}>
                                {menus.length > 0 ? menus :
                                    <Dropdown.ItemText className='text-muted'>暂无可用操作</Dropdown.ItemText>}
                            </Dropdown.Menu>
                        </Dropdown>;
                    }
                }
            ]}
        />
    </Content>;
};
