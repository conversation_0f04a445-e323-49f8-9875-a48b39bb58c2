import { <PERSON>, Loader, Re<PERSON><PERSON><PERSON>on, Result, useNavigate, useParams, useRequest } from '@topthink/common';
import Container from '../components/container';
import { useSpace } from '@/components/space-provider';

export const Component = function() {
    const { code } = useParams();
    const navigate = useNavigate();
    const { refresh } = useSpace();

    const { result, error } = useRequest(`/invite/${code}`);

    if (error) {
        return <Container>
            <Card>
                <Result status={'error'} title={'邀请链接不存在或已过期'} />
            </Card>
        </Container>;
    }

    if (!result) {
        return <Loader />;
    }

    const { user, space } = result;

    return <Container>
        <Card>
            <p className='card-text text-muted mb-5'>
                <strong className='me-2'>{user.name}</strong>
                邀请您加入空间
            </p>
            <h5 className='card-title text-center mb-5'>{space.name}</h5>
            <div className='d-grid'>
                <RequestButton
                    url={`/invite/${code}`}
                    method={'post'}
                    onSuccess={async (result) => {
                        await refresh();
                        navigate(`/space/${result.hash_id}`, { replace: true });
                    }}
                >立即加入</RequestButton>
            </div>
        </Card>
    </Container>;
};
