import { Content, RequestButton, Space, Table, TableType } from '@topthink/common';
import { Badge } from 'react-bootstrap';
import { useCurrentSpace } from '@/components/space-provider';
import { useDatabase } from '../provider';
import FieldFormModal from './form-modal';
import { useRef } from 'react';

export const Component = () => {
    const { current: space } = useCurrentSpace();
    const { current: database } = useDatabase();
    const table = useRef<TableType>(null);
    return <Content extra={<FieldFormModal
        onSuccess={() => table.current?.reload()}
        action={`/space/${space.hash_id}/database/${database.hash_id}/field`}
        method="post"
        text={<><i className='bi bi-plus-lg me-2' />添加字段</>}
        modalProps={{ header: '添加字段' }}
    />}>
        <Table
            ref={table}
            source={`/space/${space.hash_id}/database/${database.hash_id}/field`}
            columns={[
                {
                    dataIndex: 'label',
                    title: '显示名',
                },
                {
                    dataIndex: 'name',
                    title: '字段名',
                },
                {
                    dataIndex: 'type',
                    title: '类型',
                    width: 100,
                },
                {
                    dataIndex: 'required',
                    title: '必填',
                    width: 80,
                    align: 'center',
                    render({ value }) {
                        return value ? <Badge bg='success'>是</Badge> : <Badge bg='secondary'>否</Badge>;
                    }
                },
                {
                    key: 'action',
                    title: '操作',
                    width: 120,
                    align: 'right',
                    render({ record, action }) {
                        return <Space>
                            <FieldFormModal
                                formData={{
                                    label: record.label,
                                    name: record.name,
                                    type: record.type,
                                    required: record.required,
                                    options: record.options || []
                                }}
                                onSuccess={() => action.reload()}
                                action={`/space/${space.hash_id}/database/${database.hash_id}/field/${record.id}`}
                                method="put"
                                text="编辑"
                                modalProps={{ header: '编辑字段' }}
                            >编辑</FieldFormModal>
                            <RequestButton method={'delete'} url={`/space/${space.hash_id}/database/${database.hash_id}/field/${record.id}`} confirm={'确定要删除吗?'} onSuccess={() => action.reload()}>
                                删除
                            </RequestButton>
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
};
