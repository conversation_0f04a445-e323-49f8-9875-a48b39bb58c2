import { ModalForm } from '@topthink/common';
import { ReactNode } from 'react';

interface FieldFormModalProps {
    formData?: any;
    onSuccess?: () => void;
    children?: ReactNode;
    action: string;
    method: 'post' | 'put';
    text: ReactNode;
    modalProps?: {
        header?: string;
        [key: string]: any;
    };
}

export default function FieldFormModal({ formData, onSuccess,  action, method, text, modalProps }: FieldFormModalProps) {

    return <ModalForm
        text={text}
        modalProps={modalProps}
        action={action}
        method={method}
        formData={formData}
        schema={{
            type: 'object',
            properties: {
                name: {
                    type: 'string',
                    title: '字段名',
                    pattern: '^[a-zA-Z][a-zA-Z0-9_]*$'
                },
                label: {
                    type: 'string',
                    title: '显示名'
                },
                type: {
                    type: 'string',
                    title: '类型',
                    enum: ['string', 'integer', 'float', 'boolean', 'datetime'],
                    enumNames: ['文本', '整数', '浮点数', '布尔值', '时间'],
                    default: 'string',
                },
                required: {
                    type: 'boolean',
                    title: '必填',
                    default: false
                },
            },
            required: ['label', 'name', 'type'],
        }}
        uiSchema={{
            name: {
                'ui:autofocus': true,
                'ui:placeholder': '输入字段名，只能包含字母、数字和下划线，且必须以字母开头'
            },
            label: {
                'ui:placeholder': '输入字段显示名'
            },
            type: {
                'ui:widget': 'select'
            },
            required: {
                'ui:widget': 'checkbox'
            }
        }}
        onSuccess={onSuccess}
    />;
}
