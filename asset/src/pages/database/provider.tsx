import Back from '@/components/back';
import SiderLayout from '@/components/sider-layout';
import { useCurrentSpace } from '@/components/space-provider';
import { styled, useParams, useRequest } from '@topthink/common';
import { createContext, useContext } from 'react';
import EditDatabaseModal from './edit-modal';

interface DatabaseContextType {
    current: Database;
    refresh: () => void;
}

const DatabaseContext = createContext<DatabaseContextType>({} as DatabaseContextType);

export const useDatabase = () => useContext(DatabaseContext);

export const Component = function() {
    const { current } = useCurrentSpace();
    const { database: databaseId } = useParams();
    const { result: database, refresh } = useRequest<Database>(`/space/${current.hash_id}/database/${databaseId}`, {
        setLoading(state) {
            return {
                ...state,
                loading: true,
            };
        }
    });

    if (!database) {
        return null;
    }

    const header = <Container>
        <Back to={`/space/${current.hash_id}/database`} />
        <span className='text-truncate me-auto'>{database.name}</span>
        <EditDatabaseModal database={database} onSuccess={refresh} size={'sm'} variant={'light'}>
            <i className='bi bi-pencil-fill' />
        </EditDatabaseModal>
    </Container>;

    return <DatabaseContext.Provider value={{ current: database, refresh }}>
        <SiderLayout logo={header} />
    </DatabaseContext.Provider>;
};

const Container = styled.div`
    display: flex;
    flex: 1;
    gap: 8px;
    min-width: 0;
    align-items: center;
    font-size: 16px;
`;
