import { FormSchema, FormUiSchema, ModalForm, Space, Tooltip } from '@topthink/common';
import { ReactNode, useMemo } from 'react';
import { useCurrentSpace } from '@/components/space-provider';

interface EditDatabaseModalProps {
    database: Database;
    onSuccess?: () => void;
    children?: ReactNode;
    variant?: string;
    size?: 'sm' | 'lg';
}

export default function EditDatabaseModal({ database, onSuccess, children, variant, size }: EditDatabaseModalProps) {
    const { current } = useCurrentSpace();

    const [formData, schema, uiSchema] = useMemo(() => {
        const formData: Partial<Database> & { [key: string]: any } = {
            name: database.name,
            description: database.description,
            mode: database.mode
        };

        const schema: FormSchema = {
            type: 'object',
            properties: {
                name: {
                    type: 'string',
                    title: '名称'
                },
                description: {
                    type: 'string',
                    title: '描述'
                },
                mode: {
                    type: 'string',
                    title: '权限模式',
                    enum: [1, 2, 3],
                    default: 1
                }

            },
            required: ['name']
        };

        const uiSchema: FormUiSchema = {
            name: {
                'ui:autofocus': true,
                'ui:placeholder': '输入数据库名称'
            },
            description: {
                'ui:widget': 'textarea',
                'ui:placeholder': '输入数据库描述',
                'ui:options': {
                    rows: 3
                }
            },
            mode: {
                'ui:widget': 'radio',
                'ui:enumNames': [
                    <Space size={5}>
                        个人模式
                        <Tooltip tooltip={'用户仅能对自己存储的数据进行增删查改。该模式适用于，个人账本、读书笔记等场景。'}>
                            <i className='bi bi-question-circle' />
                        </Tooltip>
                    </Space>,
                    <Space size={5}>
                        共享模式
                        <Tooltip tooltip={'用户可自由增加数据、查询表中的所有数据，但只能修改和删除自己添加的数据。该模式适用于，信息收集表、排行榜等场景。'}>
                            <i className='bi bi-question-circle' />
                        </Tooltip>
                    </Space>,
                    <Space size={5}>
                        只读模式
                        <Tooltip tooltip={'用户可以查询表中的所有数据，但不能修改和删除任何数据。该模式适用于导入数据分析等场景。'}>
                            <i className='bi bi-question-circle' />
                        </Tooltip>
                    </Space>,
                ],
            }
        };

        return [formData, schema, uiSchema];
    }, [database]);

    return <ModalForm
        action={`/space/${current.hash_id}/database/${database.hash_id}`}
        method={'put'}
        buttonProps={{ variant, size }}
        modalProps={{ header: '基本信息' }}
        text={children}
        formData={formData}
        schema={schema}
        uiSchema={uiSchema}
        onSuccess={onSuccess}
    />;
}
