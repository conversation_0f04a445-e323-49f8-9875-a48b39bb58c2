import { FormSchema, FormUiSchema, ModalForm, ModalFormProps } from '@topthink/common';
import { useMemo } from 'react';


interface RecordFormModalProps extends Pick<ModalFormProps, 'action' | 'method' | 'text' | 'onSuccess' | 'modalProps' | 'formData'> {
    fields: DatabaseField[];
}

export default function RecordFormModal({ fields, ...props }: RecordFormModalProps) {
    const [schema, uiSchema] = useMemo(() => {
        const properties: Record<string, any> = {};
        const required: string[] = [];
        const uiSchema: FormUiSchema = {};

        fields.forEach(field => {
            let fieldSchema: any = {
                title: field.label
            };

            switch (field.type) {
                case 'string':
                    fieldSchema.type = 'string';
                    break;
                case 'integer':
                    fieldSchema.type = 'integer';
                    break;
                case 'float':
                    fieldSchema.type = 'number';
                    break;
                case 'boolean':
                    fieldSchema.type = 'boolean';
                    break;
                case 'datetime':
                    fieldSchema.type = 'string';
                    fieldSchema.format = 'date-time';
                    break;
            }

            properties[field.name] = fieldSchema;

            if (field.required) {
                required.push(field.name);
            }

            // 设置UI Schema
            const fieldUiSchema: Record<string, any> = {};

            switch (field.type) {
                case 'text':
                    if (field.name === fields[0].name) {
                        fieldUiSchema['ui:autofocus'] = true;
                    }
                    break;
                case 'select':
                    fieldUiSchema['ui:widget'] = 'select';
                    break;
                case 'radio':
                    fieldUiSchema['ui:widget'] = 'radio';
                    break;
                case 'checkbox':
                    fieldUiSchema['ui:widget'] = 'checkboxes';
                    break;
                case 'boolean':
                    fieldUiSchema['ui:widget'] = 'checkbox';
                    break;
            }

            uiSchema[field.name] = fieldUiSchema;
        });

        const schema: FormSchema = {
            type: 'object',
            properties,
            required
        };

        return [schema, uiSchema];
    }, [fields]);

    return <ModalForm
        {...props}
        schema={schema}
        uiSchema={uiSchema}
    />;
}
