import { useCurrentSpace } from '@/components/space-provider';
import { Content, dayjs, RequestButton, Space, Table, useRequest } from '@topthink/common';
import { useRef } from 'react';
import { ButtonGroup, Dropdown } from 'react-bootstrap';
import { useDatabase } from '../provider';
import RecordFormModal from './form-modal';
import RecordImportModal from './import-modal';


export const Component = () => {
    const { current: space } = useCurrentSpace();
    const { current: database } = useDatabase();
    const table = useRef<any>(null);

    const { result: fields = [] } = useRequest<DatabaseField[]>(`/space/${space.hash_id}/database/${database.hash_id}/field`);

    return <Content extra={fields.length > 0 ?
        <Dropdown as={ButtonGroup} align={'end'}>
            <RecordFormModal
                fields={fields}
                action={`/space/${space.hash_id}/database/${database.hash_id}/record`}
                method={'post'}
                text={<><i className='bi bi-plus-lg me-2' />添加记录</>}
                modalProps={{ header: '添加记录' }}
                onSuccess={() => table.current?.reload()}
            />
            <Dropdown.Toggle split />
            <Dropdown.Menu>
                <RecordImportModal fields={fields} />
            </Dropdown.Menu>
        </Dropdown> : null}>
        <Table
            toolBarRender={() => {
                return <RequestButton
                    url={`/space/${space.hash_id}/database/${database.hash_id}/record/export`}
                    method={'post'}
                    variant={'secondary'}
                    onSuccess={({ url }) => {
                        if (url) {
                            window.open(url, '_blank');
                        }
                    }}
                >导出数据</RequestButton>;
            }}
            ref={table}
            source={`/space/${space.hash_id}/database/${database.hash_id}/record`}
            columns={[
                ...fields.map(field => ({
                    dataIndex: field.name,
                    title: field.label,
                    render({ value }: { value: any }) {
                        switch (field.type) {
                            case 'boolean':
                                return value ? '是' : '否';
                            default:
                                return value;
                        }
                    }
                })),
                {
                    dataIndex: 'create_time',
                    title: '创建时间',
                    width: 150,
                    render({ value }) {
                        return dayjs(value).add(8, 'hour').format('YYYY-MM-DD HH:mm:ss');
                    }
                },
                {
                    dataIndex: 'action',
                    title: '操作',
                    width: 100,
                    align: 'center',
                    render({ record, action }) {
                        return <Space>
                            <RecordFormModal
                                fields={fields}
                                formData={record}
                                action={`/space/${space.hash_id}/database/${database.hash_id}/record/${record.id}`}
                                method={'put'}
                                text='编辑'
                                modalProps={{ header: '编辑记录' }}
                                onSuccess={() => action.reload()}
                            />
                            <RequestButton
                                method={'delete'}
                                url={`/space/${space.hash_id}/database/${database.hash_id}/record/${record.id}`}
                                confirm={'确定要删除吗?'}
                                onSuccess={action.reload}>删除</RequestButton>
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
};
