import { useCurrentSpace } from '@/components/space-provider';
import { ModalForm } from '@topthink/common';
import { Dropdown } from 'react-bootstrap';
import { useDatabase } from '../provider';

interface RecordImportModalProps {
    fields: DatabaseField[];
}

export default function RecordImportModal({ fields }: RecordImportModalProps) {

    const { current: space } = useCurrentSpace();
    const { current: database } = useDatabase();

    const download = async () => {
        const writeXlsxFile = (await import('write-excel-file')).default;

        await writeXlsxFile([
            fields.map(v => ({ value: v.name }))
        ], {
            fileName: 'template.xlsx'
        });
    };

    return <ModalForm
        action={`/space/${space.hash_id}/database/${database.hash_id}/record/import`}
        method={'post'}
        text={<><i className={'bi bi-file-arrow-up me-2'} /> 导入数据</>}
        schema={{
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    title: '选择文件',
                }
            }
        }}
        uiSchema={{
            file: {
                'ui:widget': 'upload',
                'ui:help': <>上传文件前可下载模板，根据指引在模板中编辑 <a className='link-primary' role={'button'} onClick={download}>下载模板</a></>,
                'ui:options': {
                    label: false,
                    accept: '.xlsx',
                    endpoint: '/upload/database',
                }
            }
        }}
        modalProps={{ header: '导入数据' }}
        buttonProps={{ as: Dropdown.Item }}
    />;
}
