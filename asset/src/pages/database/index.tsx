import CreateDatabaseModal from '@/components/create-database-modal';
import PlanAccess from '@/components/plan-access';
import { useCurrentSpace } from '@/components/space-provider';
import EditDatabaseModal from '@/pages/database/edit-modal';
import { Content, Link, RequestButton, Space, Table } from '@topthink/common';
import { Stack } from 'react-bootstrap';

export const Component = () => {
    const { current } = useCurrentSpace();

    return <Content extra={current.plan_level >= 20 && <CreateDatabaseModal />}>
        <PlanAccess level={20}>
            <Table
                source={`/space/${current.hash_id}/database`}
                columns={[
                    {
                        dataIndex: 'name',
                        title: '名称',
                        render({ record }) {
                            return <Stack as={Link} to={`/space/${current.hash_id}/database/${record.hash_id}`} gap={1}>
                                <span className='link-dark'>{record.name}</span>
                                <span className='fs-7 text-muted'>{record.description || '暂无描述'}</span>
                            </Stack>;
                        }
                    },
                    {
                        dataIndex: 'create_time',
                        title: '创建时间',
                        width: 150,
                    },
                    {
                        key: 'action',
                        title: '操作',
                        width: 120,
                        align: 'right',
                        render({ record, action }) {
                            return <Space>
                                <EditDatabaseModal database={record} onSuccess={action.reload}>编辑</EditDatabaseModal>
                                <RequestButton method={'delete'} url={`/space/${current.hash_id}/database/${record.hash_id}`} confirm={'确定要删除吗?'} onSuccess={action.reload}>删除</RequestButton>
                            </Space>;
                        }
                    }
                ]}
            />
        </PlanAccess>
    </Content>;
};
