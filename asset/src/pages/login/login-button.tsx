import { RequestButton, useLocation } from '@topthink/common';
import { useEffect, useRef } from 'react';

interface Props {
    autologin?: boolean;
    name: string;
    label: string;
    onSuccess: ({ url }: { url: string }) => void;
}

export default function LoginButton({ autologin, name, label, onSuccess }: Props) {
    const ref = useRef<HTMLButtonElement>(null);
    const { state } = useLocation();
    useEffect(() => {
        if (state?.from !== 'logout' && autologin) {
            ref.current?.click();
        }
    }, []);

    return <RequestButton
        ref={ref}
        variant={'outline-primary'}
        url={`/auth/login/${name}`}
        onSuccess={onSuccess}
    >使用 {label} 登录</RequestButton>;
}
