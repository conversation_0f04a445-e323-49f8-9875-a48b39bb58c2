import { Loader, request, styled, useAsyncCallback, useNavigate, useRequest } from '@topthink/common';
import { useEffect, useRef, useState } from 'react';
import { ReactComponent as RefreshIcon } from '../../images/refresh.svg';

interface Props {
    onLogined: (token?: string) => string;
}

export default function Qrcode({ onLogined }: Props) {
    const [text, setText] = useState('请使用微信，扫码登录');
    const [expired, setExpired] = useState(false);
    const navigate = useNavigate();

    const unmountedRef = useRef(false);

    useEffect(() => {
        unmountedRef.current = false;
        return () => {
            unmountedRef.current = true;
        };
    }, []);

    const { execute: check } = useAsyncCallback(async (token) => {
        try {
            const data = await request({
                url: `/auth/check`,
                params: { token },
                raxConfig: {
                    retryDelay: 2000,
                    backoffType: 'static',
                    shouldRetry(err) {
                        return !unmountedRef.current && err.response?.status === 449;
                    }
                }
            });

            //登录成功
            const url = onLogined(data.token);
            navigate(url, { replace: true });
        } catch {
            setText('小程序码已过期，请点击刷新');
            setExpired(true);
        }
    });

    const { result, execute, loading } = useRequest('/auth/qrcode', {
        onSuccess(result) {
            setText('请使用微信，扫码登录');
            check(result.token);
        }
    });

    return <>
        <Container>
            {!result || loading ? <Loader /> : <img src={result.image} />}
            {expired && <Mask onClick={() => {
                setExpired(false);
                execute();
            }}><RefreshIcon /></Mask>}
        </Container>
        <div className={'text-center text-muted fs-5'}>{text}</div>
    </>;
}


const Mask = styled.div`
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, .5);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #FFFFFF;

    svg {
        width: 90px;
        height: 90px;
    }
`;

const Container = styled.div`
    width: 198px;
    height: 198px;
    border-radius: 50%;
    border: 1px solid #e9ecef;
    margin: 0 auto 2rem;
    overflow: hidden;
    padding: 16px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
        width: 100%;
        height: 100%;
    }
`;
