import { useManifest } from '@/components/manifest-provider';
import { styled } from '@topthink/common';
import { useState } from 'react';
import LoginButton from './login-button';
import Qrcode from './qrcode';

export default function Login({ onAuthorize, onLogined }: any) {

    let { login: { qrcode, channels }, website, cloud } = useManifest();

    const supportQrcode = qrcode || channels.length == 0;
    const supportSwitcher = supportQrcode && channels.length > 0 && !cloud;

    const [social, setSocial] = useState(cloud || !supportQrcode);

    if (cloud) {
        channels = [{
            name: 'topthink',
            label: '顶想云',
            autologin: true,
        }];
    }

    return <>
        <h2 className='text-center mb-5'>欢迎使用{website.title}</h2>
        <Container>
            {social ? <Buttons>
                {channels.map((item) => {
                    return <LoginButton
                        key={item.name}
                        {...item}
                        onSuccess={({ url }) => {
                            onAuthorize(url, { channel: item.name });
                        }}
                    />;
                })}
            </Buttons> : <Qrcode onLogined={onLogined} />}
        </Container>
        {supportSwitcher && <Switcher onClick={() => setSocial(!social)}>
            {social ? <i className={'bi bi-qr-code'} /> : <i className={'bi bi-display'} />}
        </Switcher>}
    </>;
}

const Buttons = styled.div`
    display: flex;
    gap: 1rem;
    flex-direction: column;
    width: 100%;
`;

const Container = styled.div`
    min-height: 300px;
    display: flex;
    justify-content: center;
    flex-direction: column;
`;

const Switcher = styled.div`
    background: var(--bs-primary);
    width: 56px;
    height: 56px;
    clip-path: polygon(100% 0%, 100% 100%, 0% 0%);
    position: absolute;
    right: 0;
    top: 0;
    border-top-right-radius: var(--bs-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFF;
    cursor: pointer;

    .bi {
        font-size: 1.75rem;
    }
`;
