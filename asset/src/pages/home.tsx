import { <PERSON><PERSON>, <PERSON>, Content, <PERSON>, Result, useAsyncEffect, useNavigate, useUser } from '@topthink/common';
import { useSpace } from '@/components/space-provider';
import CreateSpaceModal from '@/components/create-space-modal';
import Logo from '@/components/logo';
import { Dropdown } from 'react-bootstrap';
import getUserAvatar from '@/utils/get-user-avatar';
import AdminAccess from '@/components/admin-access';

export const Component = function() {
    const { spaces } = useSpace();
    const { user } = useUser();
    const navigate = useNavigate();

    useAsyncEffect(async () => {
        if (spaces.length) {
            navigate(`space/${spaces[0].hash_id}`, { replace: true });
        }
    }, []);

    const extra = <Dropdown navbar align={'end'}>
        <Dropdown.Toggle role={'button'} as={'a'} className='nav-link'>
            <img className='rounded-circle' width='25' height='25' src={getUserAvatar(user)} />
        </Dropdown.Toggle>
        <Dropdown.Menu className={'shadow'}>
            <AdminAccess>
                <Dropdown.Item as={Link} to='/admin'><i className='bi bi-gear me-2' />系统设置</Dropdown.Item>
            </AdminAccess>
            <Dropdown.Item as={Link} to='/logout'><i className='bi bi-box-arrow-right me-2' />退出登录</Dropdown.Item>
        </Dropdown.Menu>
    </Dropdown>;

    return <Content title={<Logo />} extra={extra}>
        <Card>
            <Result
                status={'info'}
                extra={<div>
                    <h4>暂无空间</h4>
                    <div className={'mt-4'}>
                        <CreateSpaceModal button={Button} />
                    </div>
                </div>}
            />
        </Card>
    </Content>;
};
