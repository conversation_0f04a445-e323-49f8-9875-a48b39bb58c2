import dayjs from 'dayjs';
import { css, formatLong<PERSON><PERSON>ber, Link, LinkButton, styled, useUser } from '@topthink/common';
import { useCurrentSpace } from '@/components/space-provider';
import { Dropdown, ProgressBar } from 'react-bootstrap';
import getPercent from '@/utils/get-percent';
import AdminAccess from '@/components/admin-access';
import { useManifest } from '@/components/manifest-provider';
import LevelAccess from '@/components/level-access';
import { MASTER } from '@/utils/constants';
import getUserAvatar from '@/utils/get-user-avatar';
import UserSetting from '@/pages/user';

export default function Footer() {
    const { current, refresh } = useCurrentSpace();
    const { user } = useUser();
    const isExpired = dayjs(current.expire_time).isBefore(dayjs());
    const { website: { help, qrcode }, cloud } = useManifest();

    return <Container>
        <SpaceInfo $paid={current.plan !== 'trial'} onMouseEnter={refresh}>
            <Info>
                <div>
                    <div className='d-flex align-items-center justify-content-between mb-1'>
                        <span>Token额度：</span><span>{formatLongNumber(current.quota.token.used)}/{formatLongNumber(current.quota.token.limit)}</span>
                    </div>
                    <ProgressBar now={getPercent(current.quota.token.used, current.quota.token.limit)} striped />
                </div>
                {current.expire_time && <div className='d-flex align-items-center justify-content-between'>
                    <span>有效期至：</span><span>{dayjs(current.expire_time).format('YYYY-MM-DD')}</span>
                </div>}
            </Info>
            <Plan>
                <PlanName>
                    <i className='bi bi-gem' />
                    {current.plan_name}
                    {isExpired && <span className='text-danger'>(已过期)</span>}
                </PlanName>
                {cloud && <LevelAccess level={MASTER}>
                    <BuyButton to={`/space/${current.hash_id}/billing`}>{current.plan === 'trial' ? '升级' : '续费'}</BuyButton>
                </LevelAccess>}
            </Plan>
        </SpaceInfo>
        {help && <MenuItem as={'a'} href={help} target='_blank'>
            <i className='bi bi-question-circle' />帮助文档
        </MenuItem>}
        {qrcode && <Dropdown drop={'end'}>
            <Dropdown.Toggle as={MenuItem} className='no-caret'>
                <i className='bi bi-wechat' />交流社群
            </Dropdown.Toggle>
            <Dropdown.Menu>
                <div className='p-3'>
                    <img src={qrcode} width={200} height={200} />
                </div>
            </Dropdown.Menu>
        </Dropdown>}
        <UserInfo>
            <Dropdown drop={'end'}>
                <Dropdown.Toggle as={MenuItem} className='no-caret'>
                    <img className='rounded-circle' width='25' height='25' src={getUserAvatar(user)} />
                    {user.name}
                </Dropdown.Toggle>
                <Dropdown.Menu>
                    <AdminAccess>
                        <Dropdown.Item as={Link} to='/admin'><i className='bi bi-gear me-2' />系统设置</Dropdown.Item>
                    </AdminAccess>
                    <UserSetting />
                    <Dropdown.Item as={Link} to='/logout'><i className='bi bi-box-arrow-right me-2' />退出登录</Dropdown.Item>
                </Dropdown.Menu>
            </Dropdown>
        </UserInfo>
    </Container>;
}

const MenuItem = styled.div`
    padding: 0px 8px 0px 24px;
    height: 38px;
    line-height: 38px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    color: dimgray;

    &:hover {
        background-color: rgb(240, 240, 240);
    }
`;

const UserInfo = styled.div`
    border-top: 1px solid #e3e3e3;
    padding: 10px 0px;
    margin-top: 10px;

    ${MenuItem} {
        color: var(--bs-dark);
    }
`;

const Info = styled.div`
    background: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
    padding: 8px;
    margin: 8px 8px 0;
    border-radius: 8px;
    color: rgba(var(--bs-primary-rgb), 0.9);
    display: none;
    flex-direction: column;
    gap: .8rem;

    .progress {
        height: .7rem;
    }
`;

const BuyButton = styled(LinkButton)`
    padding-top: 0;
    padding-bottom: 0;
`;

const PlanName = styled.div`
    display: flex;
    align-items: center;
    gap: 0.5rem;

    .bi {
        font-size: 16px;
        line-height: 18px;
        min-width: 19px;
        text-align: center;
    }
`;

const Plan = styled.div`
    display: flex;
    padding: 8px 8px 8px 16px;
    justify-content: space-between;
    font-weight: bold;
`;

const SpaceInfo = styled.div<{ $paid?: boolean }>`
    background: linear-gradient(148deg, #eff0fb, var(--bs-primary-bg-subtle) 59%, var(--bs-primary-border-subtle));
    margin: 8px;
    border-radius: 8px;
    color: var(--bs-primary);
    padding: 0;
    overflow: hidden;

    &:hover {
        ${Info} {
            display: flex;
        }
    }

    ${props => props.$paid && css`
        --bs-primary-rgb: 137, 63, 3;
        color: rgb(137, 63, 3);
        background: linear-gradient(332deg, rgb(255, 235, 194), rgb(255, 209, 137));

        .btn {
            --bs-btn-bg: rgb(137, 63, 3);
            --bs-btn-border-color: rgb(137, 63, 3);
            --bs-btn-hover-bg: #743603;
            --bs-btn-hover-border-color: #6e3202;
            --bs-btn-active-bg: #6e3202;
            --bs-btn-active-border-color: #672f02;
            --bs-btn-disabled-bg: rgb(137, 63, 3);
            --bs-btn-disabled-border-color: rgb(137, 63, 3);
        }

        .progress {
            --bs-progress-bg: #ede2d9;
            --bs-progress-bar-bg: rgb(137, 63, 3);
        }
    `}
`;

const Container = styled.div``;
