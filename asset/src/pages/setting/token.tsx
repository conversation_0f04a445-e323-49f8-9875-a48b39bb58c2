import PlanAccess from '@/components/plan-access';
import { useCurrentSpace } from '@/components/space-provider';
import { Clipboard, Columns, ModalForm, RequestButton, Space, Table } from '@topthink/common';
import { useState } from 'react';

export const Component = () => {
    const { current } = useCurrentSpace();

    const columns: Columns<AccessToken> = [
        {
            title: '名称',
            dataIndex: 'name'
        },
        {
            title: '令牌',
            dataIndex: 'value',
            render({ value }) {
                return <Token value={value} />;
            }
        },
        {
            title: '创建于',
            dataIndex: 'create_time',
            width: 150
        },
        {
            title: '最近使用',
            dataIndex: 'last_time',
            width: 150,
            render({ value }) {
                return value || '--';
            }
        },
        {
            title: '到期',
            dataIndex: 'expire_time',
            width: 150,
            render({ value }) {
                return value || '--';
            }
        },
        {
            title: '操作',
            width: 120,
            align: 'right',
            render({ record, action }) {
                return <>
                    <RequestButton
                        url={`/space/${current.hash_id}/token/${record.id}`}
                        method={'delete'}
                        confirm={'确定要删除吗？'}
                        onSuccess={action.reload}
                    >删除</RequestButton>
                </>;
            }
        }
    ];

    return <PlanAccess level={20}>
        <Table
            source={`/space/${current.hash_id}/token`}
            columns={columns}
            toolBarRender={({ reload }) => {
                return <ModalForm
                    text={'创建令牌'}
                    method={'post'}
                    action={`/space/${current.hash_id}/token`}
                    schema={{
                        type: 'object',
                        properties: {
                            name: {
                                type: 'string',
                                title: '名称'
                            },
                            expire_time: {
                                type: 'string',
                                title: '到期时间'
                            }
                        }
                    }}
                    uiSchema={{
                        expire_time: {
                            'ui:widget': 'datetime',
                            'ui:description': '留空则永久有效',
                        }
                    }}
                    onSuccess={reload}
                />;
            }}
        />
    </PlanAccess>;
};

const Token = function({ value }: { value: string }) {
    const [show, setShow] = useState(false);
    const text = show ? value : '************************************';
    return <Space>
        {text}
        {show && <Clipboard content={value} />}
        {show && <i role={'button'} className={'bi bi-eye-slash'} onClick={() => setShow(false)} />}
        {!show && <i role={'button'} className={'bi bi-eye'} onClick={() => setShow(true)} />}
    </Space>;
};
