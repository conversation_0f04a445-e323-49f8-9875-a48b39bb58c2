import { useCurrentSpace } from '@/components/space-provider';
import getPercent from '@/utils/get-percent';
import {
    Card,
    css,
    formatLongNumber,
    ModalButton,
    request,
    showRequestError,
    styled,
    Tooltip,
    waitPayComplete
} from '@topthink/common';
import dayjs from 'dayjs';
import { useState } from 'react';
import { Badge, Col, FormControl, ProgressBar, Row } from 'react-bootstrap';


export const Component = function() {
    const { current, refresh } = useCurrentSpace();
    const isExpired = dayjs(current.expire_time).isBefore(dayjs());
    const [tempTokens, setTempTokens] = useState(1000); // 以K为单位

    const handleTemporaryRecharge = async () => {
        try {
            const nums = Math.floor(tempTokens / 1000); // 转换为档数
            const result = await request({
                method: 'POST',
                url: `/space/${current.hash_id}/billing/temporary-recharge`,
                data: { nums }
            });
            waitPayComplete({
                result,
                checkUrl: `/space/${current.hash_id}/billing/check`,
                onComplete() {
                    refresh();
                }
            });
            return true; // 关闭模态框
        } catch (e) {
            showRequestError(e);
            return false; // 保持模态框打开
        }
    };

    return <Card>
        <h5 className='d-flex align-items-center gap-2 lh-1'>
            <span className={'text-truncate'}>{current.name}</span>
            <Plan $paid={current.plan !== 'trial'} className={'me-auto'}>
                <i className='bi bi-gem' />
                {current.plan_name}
                {isExpired && <span className='text-danger'>(已过期)</span>}
            </Plan>
            {current.expire_time &&
                <Badge bg={'secondary'} text={'muted'} className='bg-opacity-10 fs-7fw-normal'>
                    有效期至：{dayjs(current.expire_time).format('YYYY-MM-DD')}
                </Badge>}
            <Tooltip tooltip={'刷新'}>
                <Badge role='button' bg={'secondary'} text={'muted'} className='bg-opacity-10 fs-7 fw-normal' onClick={refresh}>
                    <i className='bi bi-arrow-clockwise' />
                </Badge>
            </Tooltip>
        </h5>
        <hr />
        <Row md={4} className='g-3'>
            <Col>
                <Quota>
                    <h5 className='mb-3'><i className='bi bi-x-diamond me-2' />Token额度</h5>
                    <p className='text-black-50 fs-7 lh-base mb-2 d-flex'>
                        每月重置
                        <span className='vr mx-2 my-1' />
                        下次重置时间：{dayjs(current.reset_time).format('YYYY-MM-DD')}
                    </p>
                    <h6 className='fw-normal'>
                        <span className='fs-5 fw-bold'>{formatLongNumber(current.quota.token.used)}</span>/{formatLongNumber(current.quota.token.limit)}
                        {current.quota.token.temporary > 0 && (
                            <small className='text-muted ms-2'>
                                (含临时扩容 {formatLongNumber(current.quota.token.temporary)})
                            </small>
                        )}
                    </h6>
                    <ProgressBar now={getPercent(current.quota.token.used, current.quota.token.limit)} striped />

                    {/* 临时扩容按钮 */}
                    <div className='d-flex justify-content-end mt-2'>
                        <ModalButton
                            size='sm'
                            variant='outline-primary'
                            disabled={current.plan === 'trial' || isExpired}
                            text={<><i className='bi bi-lightning me-1' />临时扩容</>}
                            modalProps={{
                                header: '临时Token扩容',
                                okText: '确认购买',
                                size: 'lg'
                            }}
                            onOk={handleTemporaryRecharge}
                        >
                            <div>
                                <TempServiceItem>
                                    <div className='d-flex flex-column gap-1'>
                                        <div className='d-flex align-items-center gap-2'>
                                            <div className='text-nowrap fw-bold'>Token额度</div>
                                            <FormControl
                                                type='number'
                                                min={1000}
                                                step={1000}
                                                value={tempTokens}
                                                onChange={(e) => {
                                                    const value = Number(e.target.value);
                                                    setTempTokens(Math.max(1000, Math.floor(value / 1000) * 1000));
                                                }}
                                            />
                                            <div className='text-nowrap fw-bold'>K</div>
                                        </div>
                                        <div className='text-muted fs-7'>临时扩容额度，下次重置时失效</div>
                                    </div>
                                    <div className='text-black-50 ms-auto'>
                                        ￥<span className='fs-3 fw-bold text-dark'>{Math.floor(tempTokens / 1000) * 50}</span>
                                    </div>
                                </TempServiceItem>

                                <div className='mt-3 p-3 bg-light rounded'>
                                    <div className='d-flex justify-content-between mb-2'>
                                        <span className='text-muted'>有效期至：</span>
                                        <span className='fw-bold'>{dayjs(current.reset_time).format('YYYY-MM-DD')}</span>
                                    </div>
                                    <div className='d-flex justify-content-between'>
                                        <span className='text-muted'>支付金额：</span>
                                        <span className='fw-bold text-primary fs-5'>￥{Math.floor(tempTokens / 1000) * 50}元</span>
                                    </div>
                                </div>
                            </div>
                        </ModalButton>
                    </div>
                </Quota>
            </Col>
            <Col>
                <Quota>
                    <h5 className='mb-3'><i className='bi bi-robot me-2' />智能体数量</h5>
                    <p className='text-black-50 fs-7 lh-base mb-2 d-flex'>&nbsp;</p>
                    <h6 className='fw-normal'>
                        <span className='fs-5 fw-bold'>{current.quota.bot.used}</span>/{current.quota.bot.limit}
                    </h6>
                    <ProgressBar now={getPercent(current.quota.bot.used, current.quota.bot.limit)} striped />
                </Quota>
            </Col>
            <Col>
                <Quota>
                    <h5 className='mb-3'><i className='bi bi-journal-text me-2' />知识库数量</h5>
                    <p className='text-black-50 fs-7 lh-base mb-2 d-flex'>&nbsp;</p>
                    <h6 className='fw-normal'>
                        <span className='fs-5 fw-bold'>{current.quota.dataset.used}</span>/{current.quota.dataset.limit}
                    </h6>
                    <ProgressBar now={getPercent(current.quota.dataset.used, current.quota.dataset.limit)} striped />
                </Quota>
            </Col>
            <Col>
                <Quota>
                    <h5 className='mb-3'><i className='bi bi-database me-2' />数据库数量</h5>
                    <p className='text-black-50 fs-7 lh-base mb-2 d-flex'>&nbsp;</p>
                    <h6 className='fw-normal'>
                        <span className='fs-5 fw-bold'>{current.quota.database.used}</span>/{current.quota.database.limit}
                    </h6>
                    <ProgressBar now={getPercent(current.quota.database.used, current.quota.database.limit)} striped />
                </Quota>
            </Col>
            <Col>
                <Quota>
                    <h5 className='mb-3'><i className='bi bi-plugin me-2' />插件数量</h5>
                    <p className='text-black-50 fs-7 lh-base mb-2 d-flex'>&nbsp;</p>
                    <h6 className='fw-normal'>
                        <span className='fs-5 fw-bold'>{current.quota.plugin.used}</span>/{current.quota.plugin.limit}
                    </h6>
                    <ProgressBar now={getPercent(current.quota.plugin.used, current.quota.plugin.limit)} striped />
                </Quota>
            </Col>
            <Col>
                <Quota>
                    <h5 className='mb-3'><i className='bi bi-people me-2' />成员</h5>
                    <p className='text-black-50 fs-7 lh-base mb-2 d-flex'>&nbsp;</p>
                    <h6 className='fw-normal'>
                        <span className='fs-5 fw-bold'>{current.quota.member.used}</span>/{current.quota.member.limit}
                    </h6>
                    <ProgressBar now={getPercent(current.quota.member.used, current.quota.member.limit)} striped />
                </Quota>
            </Col>
        </Row>
    </Card>;
};

const Quota = styled.div`
    padding: 1rem 1.25rem;
    background: linear-gradient(180deg, #f7f8fa, #fff);
    border-radius: 6px;

    h5 {
        font-size: 1.15rem;
    }

    .progress {
        height: .7rem;
    }
`;

const Plan = styled.div<{ $paid?: boolean }>`
    background: linear-gradient(148deg, #eff0fb, var(--bs-primary-bg-subtle) 59%, var(--bs-primary-border-subtle));
    font-size: 12px;
    color: var(--bs-primary);
    padding: 4px 8px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    line-height: 1;
    gap: .25rem;
    white-space: nowrap;

    ${props => props.$paid && css`
        color: rgb(137, 63, 3);
        background: linear-gradient(332deg, rgb(255, 235, 194), rgb(255, 209, 137));
    `}
`;

const TempServiceItem = styled.div`
    border-radius: 6px;
    background-color: #f8f9fd;
    padding: 1.25rem 1rem;
    border: 2px solid #fec86d;
    display: flex;
    align-items: center;
    gap: 1rem;
    background: linear-gradient(332deg, #ffebc2, #ffd189);

    .form-control {
        border-color: rgba(117, 47, 6, 0.1);

        &:focus {
            border-color: #fec86d;
            box-shadow: 0 0 0 0.25rem rgba(117, 47, 6, 0.15);
        }
    }

    .form-check-input {
        font-size: 18px;
        margin-top: 0;
        background-color: #752f06;
        border-color: #752f06;
    }

    .text-black-50 {
        color: rgba(117, 47, 6, .68) !important;

        .text-dark {
            color: #752f06 !important;
        }
    }
`;
