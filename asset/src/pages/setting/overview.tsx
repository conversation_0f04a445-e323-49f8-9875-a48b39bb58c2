import { Card, css, formatLongNumber, styled, Tooltip } from '@topthink/common';
import { useCurrentSpace } from '@/components/space-provider';
import { Badge, Col, ProgressBar, Row } from 'react-bootstrap';
import dayjs from 'dayjs';
import getPercent from '@/utils/get-percent';

export const Component = function() {
    const { current, refresh } = useCurrentSpace();
    const isExpired = dayjs(current.expire_time).isBefore(dayjs());

    return <Card>
        <h5 className='d-flex align-items-center gap-2 lh-1'>
            <span className={'text-truncate'}>{current.name}</span>
            <Plan $paid={current.plan !== 'trial'} className={'me-auto'}>
                <i className='bi bi-gem' />
                {current.plan_name}
                {isExpired && <span className='text-danger'>(已过期)</span>}
            </Plan>
            {current.expire_time &&
                <Badge bg={'secondary'} text={'muted'} className='bg-opacity-10 fs-7fw-normal'>
                    有效期至：{dayjs(current.expire_time).format('YYYY-MM-DD')}
                </Badge>}
            <Tooltip tooltip={'刷新'}>
                <Badge role='button' bg={'secondary'} text={'muted'} className='bg-opacity-10 fs-7 fw-normal' onClick={refresh}>
                    <i className='bi bi-arrow-clockwise' />
                </Badge>
            </Tooltip>
        </h5>
        <hr />
        <Row md={4} className='g-3'>
            <Col>
                <Quota>
                    <h5 className='mb-3'><i className='bi bi-x-diamond me-2' />Token额度</h5>
                    <p className='text-black-50 fs-7 lh-base mb-2 d-flex'>
                        每月重置
                        <span className='vr mx-2 my-1' />
                        下次重置时间：{dayjs(current.reset_time).format('YYYY-MM-DD')}
                    </p>
                    <h6 className='fw-normal'>
                        <span className='fs-5 fw-bold'>{formatLongNumber(current.quota.token.used)}</span>/{formatLongNumber(current.quota.token.limit)}
                    </h6>
                    <ProgressBar now={getPercent(current.quota.token.used, current.quota.token.limit)} striped />
                </Quota>
            </Col>
            <Col>
                <Quota>
                    <h5 className='mb-3'><i className='bi bi-robot me-2' />智能体数量</h5>
                    <p className='text-black-50 fs-7 lh-base mb-2 d-flex'>&nbsp;</p>
                    <h6 className='fw-normal'>
                        <span className='fs-5 fw-bold'>{current.quota.bot.used}</span>/{current.quota.bot.limit}
                    </h6>
                    <ProgressBar now={getPercent(current.quota.bot.used, current.quota.bot.limit)} striped />
                </Quota>
            </Col>
            <Col>
                <Quota>
                    <h5 className='mb-3'><i className='bi bi-journal-text me-2' />知识库数量</h5>
                    <p className='text-black-50 fs-7 lh-base mb-2 d-flex'>&nbsp;</p>
                    <h6 className='fw-normal'>
                        <span className='fs-5 fw-bold'>{current.quota.dataset.used}</span>/{current.quota.dataset.limit}
                    </h6>
                    <ProgressBar now={getPercent(current.quota.dataset.used, current.quota.dataset.limit)} striped />
                </Quota>
            </Col>
            <Col>
                <Quota>
                    <h5 className='mb-3'><i className='bi bi-database me-2' />数据库数量</h5>
                    <p className='text-black-50 fs-7 lh-base mb-2 d-flex'>&nbsp;</p>
                    <h6 className='fw-normal'>
                        <span className='fs-5 fw-bold'>{current.quota.database.used}</span>/{current.quota.database.limit}
                    </h6>
                    <ProgressBar now={getPercent(current.quota.database.used, current.quota.database.limit)} striped />
                </Quota>
            </Col>
            <Col>
                <Quota>
                    <h5 className='mb-3'><i className='bi bi-plugin me-2' />插件数量</h5>
                    <p className='text-black-50 fs-7 lh-base mb-2 d-flex'>&nbsp;</p>
                    <h6 className='fw-normal'>
                        <span className='fs-5 fw-bold'>{current.quota.plugin.used}</span>/{current.quota.plugin.limit}
                    </h6>
                    <ProgressBar now={getPercent(current.quota.plugin.used, current.quota.plugin.limit)} striped />
                </Quota>
            </Col>
            <Col>
                <Quota>
                    <h5 className='mb-3'><i className='bi bi-people me-2' />成员</h5>
                    <p className='text-black-50 fs-7 lh-base mb-2 d-flex'>&nbsp;</p>
                    <h6 className='fw-normal'>
                        <span className='fs-5 fw-bold'>{current.quota.member.used}</span>/{current.quota.member.limit}
                    </h6>
                    <ProgressBar now={getPercent(current.quota.member.used, current.quota.member.limit)} striped />
                </Quota>
            </Col>
        </Row>
    </Card>;
};

const Quota = styled.div`
    padding: 1rem 1.25rem;
    background: linear-gradient(180deg, #f7f8fa, #fff);
    border-radius: 6px;

    h5 {
        font-size: 1.15rem;
    }

    .progress {
        height: .7rem;
    }
`;

const Plan = styled.div<{ $paid?: boolean }>`
    background: linear-gradient(148deg, #eff0fb, var(--bs-primary-bg-subtle) 59%, var(--bs-primary-border-subtle));
    font-size: 12px;
    color: var(--bs-primary);
    padding: 4px 8px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    line-height: 1;
    gap: .25rem;
    white-space: nowrap;

    ${props => props.$paid && css`
        color: rgb(137, 63, 3);
        background: linear-gradient(332deg, rgb(255, 235, 194), rgb(255, 209, 137));
    `}
`;
