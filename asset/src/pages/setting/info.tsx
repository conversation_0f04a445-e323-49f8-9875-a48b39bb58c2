import { Card, Form, ModalForm, useNavigate } from '@topthink/common';
import { useCurrentSpace } from '@/components/space-provider';
import LevelAccess from '@/components/level-access';
import { OWNER } from '@/utils/constants';

export const Component = function() {

    const { current, refresh } = useCurrentSpace();
    const navigate = useNavigate();

    return <Card>
        <Form
            method={'put'}
            action={`/space/${current.hash_id}`}
            formData={{
                hash_id: current.hash_id,
                name: current.name
            }}
            schema={{
                type: 'object',
                properties: {
                    hash_id: {
                        type: 'string',
                        title: '空间ID',
                        readOnly: true
                    },
                    name: {
                        type: 'string',
                        title: '空间名称'
                    }
                }
            }}
            submitText={'保存'}
            onSuccess={refresh}
        >
            {({ submit }) => {
                return <div className='col-12 d-flex'>
                    {submit}
                    <LevelAccess level={OWNER}>
                        <ModalForm
                            text={'删除空间'}
                            buttonProps={{ variant: 'outline-danger', className: 'ms-auto' }}
                            method={'delete'}
                            action={`/space/${current.hash_id}`}
                            modalProps={{ header: `确认删除空间「${current.name}」么？` }}
                            schema={{
                                type: 'object',
                                properties: {
                                    name: {
                                        type: 'string',
                                        title: `删除后，「${current.name}」下的所有数据将会永久清除。请谨慎操作！`,
                                    }
                                }
                            }}
                            uiSchema={{
                                name: {
                                    'ui:placeholder': `输入应用名称确认删除：${current.name}`,
                                    'ui:autofocus': true
                                }
                            }}
                            onSuccess={async () => {
                                await refresh();
                                navigate('/');
                            }}
                        />
                    </LevelAccess>
                </div>;
            }}
        </Form>
    </Card>;
};
