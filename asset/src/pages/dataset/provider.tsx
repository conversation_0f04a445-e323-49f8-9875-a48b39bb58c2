import { styled, useParams, useRequest } from '@topthink/common';
import { useCurrentSpace } from '@/components/space-provider';
import { createContext, useContext } from 'react';
import EditDatasetModal from '@/pages/dataset/edit-modal';
import SiderLayout from '@/components/sider-layout';
import Back from '@/components/back';

interface DatasetContextType {
    current: Dataset;
    refresh: () => Promise<void>;
}

const DatasetContext = createContext<DatasetContextType | null>(null);

export function useDataset() {
    const context = useContext(DatasetContext);
    if (!context) {
        throw new Error('useDataset must be used within a DatasetProvider');
    }
    return context;
}

export const Component = function() {
    const { current } = useCurrentSpace();
    const { dataset: datasetId } = useParams();
    const { result: dataset, refresh } = useRequest<Dataset>(`/space/${current.hash_id}/dataset/${datasetId}`, {
        setLoading(state) {
            return {
                ...state,
                loading: true,
            };
        }
    });

    if (!dataset) {
        return null;
    }

    const header = <Container>
        <Back to={`/space/${current.hash_id}/dataset`} />
        <span className='text-truncate me-auto'>{dataset.name}</span>
        <EditDatasetModal dataset={dataset} onSuccess={refresh} size={'sm'} variant={'light'}>
            <i className='bi bi-pencil-fill' />
        </EditDatasetModal>
    </Container>;

    return <DatasetContext.Provider value={{ current: dataset, refresh }}>
        <SiderLayout logo={header} />
    </DatasetContext.Provider>;
};

const Container = styled.div`
    display: flex;
    flex: 1;
    gap: 8px;
    min-width: 0;
    align-items: center;
    font-size: 16px;
`;
