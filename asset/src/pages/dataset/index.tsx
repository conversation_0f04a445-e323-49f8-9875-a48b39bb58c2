import { Content, Link, RequestButton, Space, Table } from '@topthink/common';
import { useCurrentSpace } from '@/components/space-provider';
import CreateDatasetModal from '@/components/create-dataset-modal';
import { Stack } from 'react-bootstrap';
import byteSize from 'byte-size';
import EditDatasetModal from '@/pages/dataset/edit-modal';

export const Component = function() {
    const { current } = useCurrentSpace();

    return <Content extra={<CreateDatasetModal />}>
        <Table
            source={`/space/${current.hash_id}/dataset`}
            emptyText={<span className='text-muted'>暂无知识库</span>}
            columns={[
                {
                    dataIndex: 'name',
                    title: '名称',
                    render({ record }) {
                        return <Stack as={Link} to={`/space/${current.hash_id}/dataset/${record.hash_id}`} gap={1}>
                            <span className='link-dark'>{record.name}</span>
                            <span className='fs-7 text-muted'>{record.description || '暂无描述'}</span>
                        </Stack>;
                    }
                },
                {
                    dataIndex: 'type',
                    title: '类型',
                    width: 100,
                    align: 'center',
                    render({ value }) {
                        return value === 1
                            ? <span className='text-info bg-info-subtle py-1 px-2 rounded fs-7'>Web站点</span>
                            : <span className='text-primary bg-primary-subtle py-1 px-2 rounded fs-7'>手动上传</span>;
                    }
                },
                {
                    dataIndex: 'size',
                    width: 100,
                    title: '文件大小',
                    align: 'center',
                    render({ value }) {
                        return `${byteSize(value)}`;
                    }
                },
                {
                    dataIndex: 'create_time',
                    title: '创建时间',
                    width: 150,
                },
                {
                    key: 'action',
                    title: '操作',
                    width: 120,
                    align: 'right',
                    render({ record, action }) {
                        return <Space>
                            <EditDatasetModal dataset={record} onSuccess={action.reload}>
                                编辑
                            </EditDatasetModal>
                            <RequestButton method={'delete'} url={`/space/${current.hash_id}/dataset/${record.hash_id}`} confirm={'确定要删除吗?'} onSuccess={action.reload}>删除</RequestButton>
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
};
