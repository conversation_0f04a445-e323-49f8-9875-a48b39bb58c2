import { ModalForm } from '@topthink/common';
import { useDataset } from '@/pages/dataset/provider';
import { useCurrentSpace } from '@/components/space-provider';
import template from './template.xlsx';

export default function CreateModal({ onSuccess }: { onSuccess?: () => void }) {
    const { current: space } = useCurrentSpace();
    const { current: dataset } = useDataset();

    return <ModalForm
        modalProps={{ size: 'lg' }}
        text={<><i className='bi bi-file-text me-2' />添加文档</>}
        action={`/space/${space.hash_id}/dataset/${dataset.hash_id}/source`}
        method={'post'}
        schema={{
            type: 'object',
            properties: {
                type: {
                    type: 'string',
                    title: '文档类型',
                    enum: ['text', 'file', 'qa', 'url'],
                    default: 'text'
                }
            },
            dependencies: {
                type: {
                    oneOf: [
                        {
                            properties: {
                                type: {
                                    const: 'text'
                                },
                                title: {
                                    type: 'string',
                                    title: '名称'
                                },
                                text: {
                                    type: 'string',
                                    title: '内容'
                                }
                            }
                        },
                        {
                            properties: {
                                type: {
                                    const: 'file'
                                },
                                file: {
                                    type: 'string',
                                    title: '选择文件',
                                }
                            }
                        },
                        {
                            properties: {
                                type: {
                                    const: 'qa'
                                },
                                qa: {
                                    type: 'string',
                                    title: '选择文件',
                                }
                            }
                        },
                        {
                            properties: {
                                type: {
                                    const: 'url'
                                },
                                url: {
                                    type: 'string',
                                    title: '网页地址',
                                    description: '支持从网页导入内容，仅支持静态网页',
                                }
                            }
                        }
                    ]
                }
            }
        }}
        uiSchema={{
            type: {
                'ui:widget': 'radio',
                'ui:options': {
                    button: true,
                    enumNames: ['手动输入', '文件导入', '问答导入', '网页导入']
                }
            },
            title: {
                'ui:options': {
                    placeholder: '名称'
                }
            },
            text: {
                'ui:widget': 'textarea',
                'ui:options': {
                    rows: 20,
                    placeholder: '请输入文件内容，10000个字以内，支持Markdown格式。可使用标题作为问题，紧跟后面的正文作为答案，支持同时输入多组，如：\n\n## 如何申明变量？\n在Python中，可以使用赋值语句来声明变量。例如，将一个整数值赋给变量x可以这样写：x = 42 这样就将整数值42存储在变量x中了。\n\n## python中的模型、类和函数是什么意思，有什么区别？\n在Python中，模型（Model）、类（Class）和函数（Function）是不同的概念，用于不同的目的。在Python中，模型是解决特定问题的类、函数和变量的集合，类和函数之间的主要区别在于，类是创建对象的蓝图，而函数是执行特定任务的代码块。一个类可以包含多个函数，但一个函数不能包含一个类。模型则是解决特定问题的类、函数和变量的集合。'
                }
            },
            file: {
                'ui:widget': 'upload',
                'ui:help': '支持 .txt,.md,.pdf,.docx,.xlsx,.pptx,.png,.jpg 文件。AI会自动对文本进行拆分，需要较长训练时间。',
                'ui:options': {
                    label: false,
                    accept: '.txt,.md,.pdf,.docx,.xlsx,.pptx,.png,.jpg',
                    endpoint: '/upload/dataset',
                    transformValue: (file: any) => {
                        return {
                            name: file.name,
                            value: file.value
                        };
                    }
                }
            },
            qa: {
                'ui:widget': 'upload',
                'ui:help': `接受一个 xlsx 文件，表格头包含 question 和 answer。question 代表问题，answer 代表答案。<a class="link-primary" href="${template}" download='template.xlsx' target="_blank">点击下载xlsx模板</a>`,
                'ui:options': {
                    label: false,
                    accept: '.xlsx',
                    endpoint: '/upload/dataset',
                    transformValue: (file: any) => {
                        return {
                            name: file.name,
                            value: file.value
                        };
                    }
                }
            },
            url: {
                'ui:widget': 'textarea',
                'ui:options': {
                    rows: 5,
                    placeholder: '请输入网页地址，每行一个，每次最多 20 个'
                }
            }
        }}
        onSuccess={onSuccess}
    />;
}
