import { Card, Content, InfiniteScroller, Lo<PERSON>, Result, useParams, useRequest } from '@topthink/common';
import { useCurrentSpace } from '@/components/space-provider';
import { useDataset } from '@/pages/dataset/provider';
import { ReactNode } from 'react';
import { Col, Row } from 'react-bootstrap';
import PartItem from '@/components/part-item';

export const Component = function() {
    const { current: space } = useCurrentSpace();
    const { current: dataset } = useDataset();
    const { id } = useParams();

    const { result: source } = useRequest(`/space/${space.hash_id}/dataset/${dataset.hash_id}/source/${id}`);

    let children: ReactNode;

    if (!source) {
        children = <Loader />;
    } else {
        if (source.status != 1) {
            children = <Card>
                {source.status == -1 ? <Result status='error' title={source.message} /> :
                    <Result status='info' title='处理中' />}
            </Card>;
        } else {
            children = <InfiniteScroller
                source={`/space/${space.hash_id}/dataset/${dataset.hash_id}/source/${id}/part`}
                getState={(result) => {
                    return {
                        end: result.next_page_offset === null,
                        params: {
                            offset: result.next_page_offset,
                        },
                        data: result.points
                    };
                }}
                render={({ data }) => {
                    return <>
                        <Row className={'g-4'}>
                            {data.map(({ payload }, index) => {
                                return <Col md={4} key={index + 1}>
                                    <PartItem index={index}>
                                        {payload.content}
                                    </PartItem>
                                </Col>;
                            })}
                        </Row>
                        {data.length == 0 && <Card>
                            <Result title={'暂无内容'} />
                        </Card>}
                    </>;
                }}
            />;
        }
    }

    return <Content showBack title={source?.title}>
        {children}
    </Content>;
};
