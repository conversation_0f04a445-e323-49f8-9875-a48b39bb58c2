import { Content, Link, RequestButton, Space, Table, TableType, Tooltip } from '@topthink/common';
import { useDataset } from '../provider';
import CreateModal from './create-modal';
import { ProgressBar } from 'react-bootstrap';
import { useCurrentSpace } from '@/components/space-provider';
import { ReactNode, useEffect, useMemo, useRef, useState } from 'react';
import byteSize from 'byte-size';

export const Component = function() {
    const { current: space } = useCurrentSpace();
    const { current: dataset, refresh } = useDataset();
    const [needReload, setNeedReload] = useState(false);

    const table = useRef<TableType>(null);

    useEffect(() => {
        if (needReload) {
            setTimeout(() => {
                setNeedReload(false);
                table.current?.reload(true);
            }, 3000);
        }
    }, [needReload]);

    useEffect(() => {
        if (dataset.status === 2 || dataset.status === 0) {
            setTimeout(refresh, 3000);
        } else if (dataset.status === 1) {
            table.current?.reload();
        }
    }, [dataset]);

    let extra: ReactNode = useMemo(function() {
        switch (dataset.type) {
            case 0:
                return <CreateModal onSuccess={() => {
                    table.current?.reload();
                }} />;
            case 1:
                let status: ReactNode;
                switch (dataset.status) {
                    case 1:
                        status = <Tooltip tooltip={`上次同步时间: ${dataset.last_time}`}>
                            <span className='text-success bg-success-subtle py-1 px-3 rounded '>同步完成</span>
                        </Tooltip>;
                        break;
                    case 2:
                        status = <span className='text-info bg-info-subtle py-1 px-3 rounded '>同步中...</span>;
                        break;
                    case -1:
                        status = <span className='text-danger bg-danger-subtle py-1 px-3 rounded'>
                            同步失败
                        </span>;
                        break;
                    default:
                        status = <span className='text-info bg-info-subtle py-1 px-3 rounded '>队列中...</span>;
                        break;
                }

                return <Space>
                    {status}
                    <RequestButton
                        disabled={dataset.status === 2 || dataset.status === 0}
                        method={'post'}
                        url={`/space/${space.hash_id}/dataset/${dataset.hash_id}/sync`}
                        confirm={'要立即同步数据吗？'}
                        onSuccess={refresh}
                    >立即同步</RequestButton>
                </Space>;
        }
        return null;
    }, [dataset]);


    return <Content extra={extra}>
        <Table
            search
            ref={table}
            sync={true}
            source={`/space/${space.hash_id}/dataset/${dataset.hash_id}/source`}
            columns={[
                {
                    dataIndex: 'title',
                    title: '文件名',
                    render({ record }) {
                        return <Link to={`${record.hash_id}`} className='link-dark'>{record.title}</Link>;
                    }
                },
                {
                    dataIndex: 'size',
                    width: 100,
                    title: '文件大小',
                    align: 'center',
                    render({ value }) {
                        return `${byteSize(value)}`;
                    }
                },
                {
                    dataIndex: 'create_time',
                    width: 160,
                    title: '创建时间',
                },
                {
                    dataIndex: 'status',
                    width: 120,
                    title: '状态',
                    align: 'center',
                    render: ({ value, record }) => {
                        if (value === 2 || value === 0) {
                            requestAnimationFrame(() => {
                                setNeedReload(true);
                            });
                        }
                        switch (value) {
                            case 1:
                                return <span className='text-success bg-success-subtle py-1 px-3 rounded fs-7'>可用</span>;
                            case 2:
                                return <ProgressBar variant='info' animated now={record.message || 30} label={`${record.message}%`} />;
                            case -1:
                                return <Tooltip tooltip={record.message}>
                                    <span className='text-danger bg-danger-subtle py-1 px-3 rounded fs-7'>
                                        失败
                                        <i className='bi bi-question-circle ms-1' />
                                    </span>
                                </Tooltip>;
                            default:
                                return <span className='text-info bg-info-subtle py-1 px-3 rounded fs-7'>待处理</span>;
                        }
                    }
                },
                {
                    key: 'action',
                    title: '操作',
                    align: 'right',
                    width: 90,
                    render({ record, action: { reload } }) {
                        return <Space>
                            {record.status == -1 && <RequestButton
                                url={`/space/${space.hash_id}/dataset/${dataset.hash_id}/source/${record.id}/train`}
                                method='post'
                                onSuccess={reload}
                            >重试</RequestButton>}
                            <RequestButton
                                confirm='确定要删除吗？'
                                disabled={record.status === 2}
                                url={`/space/${space.hash_id}/dataset/${dataset.hash_id}/source/${record.id}`}
                                method='delete'
                                onSuccess={reload}
                            >删除</RequestButton>
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
};
