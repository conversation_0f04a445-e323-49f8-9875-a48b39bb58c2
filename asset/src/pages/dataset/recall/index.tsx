import PartItem from '@/components/part-item';
import { useCurrentSpace } from '@/components/space-provider';
import { Card, Content, Form, Result } from '@topthink/common';
import { useState } from 'react';
import { Badge, Col, Row } from 'react-bootstrap';
import { useDataset } from '../provider';

export const Component = function() {
    const { current: space } = useCurrentSpace();
    const { current: dataset } = useDataset();
    const [data, setData] = useState<DatasetSourcePart[]>([]);

    return <Content>
        <Row className='g-3'>
            <Col md={3}>
                <Card>
                    <Form
                        action={`/space/${space.hash_id}/dataset/${dataset.hash_id}/recall`}
                        schema={{
                            type: 'object',
                            properties: {
                                query: {
                                    type: 'string',
                                    title: '名称'
                                }
                            }
                        }}
                        uiSchema={{
                            query: {
                                'ui:widget': 'textarea',
                                'ui:options': {
                                    rows: 5,
                                    placeholder: '请输入查询内容',
                                    label: false
                                }
                            },
                        }}
                        onSuccess={(data) => {
                            setData(data);
                        }}
                        submitText={'测试'}
                    />
                </Card>
            </Col>
            <Col md={9}>
                {data.length == 0 && <Card>
                    <Result title={'暂无内容'} />
                </Card>}
                {data.length > 0 && <Row className={'g-4'}>
                    {data.map(({ payload, score }, index) => {
                        return <Col md={4} key={index + 1}>
                            <PartItem index={index} action={<Badge bg={'light'} text={'muted'}>{score}</Badge>}>
                                {payload.content}
                            </PartItem>
                        </Col>;
                    })}
                </Row>}
            </Col>
        </Row>
    </Content>;
};
