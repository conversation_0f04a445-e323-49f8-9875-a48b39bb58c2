import { FormSchema, FormUiSchema, ModalForm } from '@topthink/common';
import { useCurrentSpace } from '@/components/space-provider';
import { ReactNode, useMemo } from 'react';

interface Props {
    dataset: Dataset;
    onSuccess: () => void;
    children: ReactNode;
    variant?: 'light';
    size?: 'sm';
}

export default function EditDatasetModal({ dataset, onSuccess, children, variant, size }: Props) {
    const { current } = useCurrentSpace();

    const [formData, schema, uiSchema] = useMemo(() => {
        const formData: Partial<Dataset> & { [key: string]: any } = {
            name: dataset.name,
            description: dataset.description,
        };

        const schema: FormSchema = {
            type: 'object',
            properties: {
                name: {
                    type: 'string',
                    title: '名称'
                },
                description: {
                    type: 'string',
                    title: '描述'
                },

            },
            required: ['name']
        };

        const uiSchema: FormUiSchema = {
            name: {
                'ui:autofocus': true,
                'ui:placeholder': '输入知识库名称'
            },
            freq: {
                'ui:widget': 'select',
            },
            description: {
                'ui:widget': 'textarea',
                'ui:placeholder': '输入知识库描述',
                'ui:options': {
                    rows: 3
                }
            }
        };

        if (dataset.type === 1) {
            formData.sitemap = dataset.config.sitemap;
            formData.freq = dataset.freq;

            schema.properties!.sitemap = {
                type: 'string',
                title: '站点地图(sitemap)'
            };
            schema.properties!.freq = {
                type: 'string',
                title: '更新频率',
                enum: [0, 1, 3, 7, 30],
                enumNames: ['不自动更新', '每 1 天', '每 3 天', '每 7 天', '每 30 天'],
                default: 0
            };
            uiSchema['ui:order'] = ['name', 'sitemap', 'freq', '*'];
            uiSchema['sitemap'] = {
                'ui:placeholder': 'https://www.example.com/sitemap.xml'
            };
        }

        return [formData, schema, uiSchema];
    }, [dataset]);

    return <ModalForm
        action={`/space/${current.hash_id}/dataset/${dataset.hash_id}`}
        method={'put'}
        buttonProps={{ variant, size }}
        modalProps={{ header: '基本信息' }}
        text={children}
        formData={formData}
        schema={schema}
        uiSchema={uiSchema}
        onSuccess={onSuccess}
    />;
}
