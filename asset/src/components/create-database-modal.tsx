import { ModalForm, Space, Tooltip, useNavigate } from '@topthink/common';
import { useCurrentSpace } from '@/components/space-provider';

export default function CreateDatabaseModal() {
    const { current } = useCurrentSpace();
    const navigate = useNavigate();

    return <ModalForm
        action={`/space/${current.hash_id}/database`}
        text={<><i className='bi bi-database me-2' />创建数据库</>}
        schema={{
            type: 'object',
            properties: {
                name: {
                    type: 'string',
                    title: '名称'
                },
                description: {
                    type: 'string',
                    title: '描述'
                },
                mode: {
                    type: 'string',
                    title: '权限模式',
                    enum: [1, 2, 3],
                    default: 1
                }
            },
            required: ['name']
        }}
        uiSchema={{
            name: {
                'ui:autofocus': true,
                'ui:placeholder': '输入数据库名称'
            },
            description: {
                'ui:widget': 'textarea',
                'ui:placeholder': '输入数据库描述',
                'ui:options': {
                    rows: 3
                }
            },
            mode: {
                'ui:widget': 'radio',
                'ui:enumNames': [
                    <Space size={5}>
                        个人模式
                        <Tooltip tooltip={'用户仅能对自己存储的数据进行增删查改。该模式适用于，个人账本、读书笔记等场景。'}>
                            <i className='bi bi-question-circle' />
                        </Tooltip>
                    </Space>,
                    <Space size={5}>
                        共享模式
                        <Tooltip tooltip={'用户可自由增加数据、查询表中的所有数据，但只能修改和删除自己添加的数据。该模式适用于，信息收集表、排行榜等场景。'}>
                            <i className='bi bi-question-circle' />
                        </Tooltip>
                    </Space>,
                    <Space size={5}>
                        只读模式
                        <Tooltip tooltip={'用户可以查询表中的所有数据，但不能修改和删除任何数据。该模式适用于导入数据分析等场景。'}>
                            <i className='bi bi-question-circle' />
                        </Tooltip>
                    </Space>,
                ],
            }
        }}
        onSuccess={(data) => {
            navigate(`/space/${current.hash_id}/database/${data.hash_id}`);
        }}
    />;
}
