import { Button, ModalButton, Table } from '@topthink/common';
import { Stack } from 'react-bootstrap';
import byteSize from 'byte-size';
import { useCurrentSpace } from './space-provider';

interface Props {
    value: string[];
    onChange: (updater: (draft: string[]) => void) => void;
    text: string;
}

export default function DatasetSelector({ value, onChange, text }: Props) {
    const { current: space } = useCurrentSpace();

    return <ModalButton
        size='sm'
        variant='light'
        modalProps={{ footer: false, size: 'lg', header: '选择知识库' }}
        text={text}
    >
        <Table
            card={false}
            toolBarRender={false}
            source={`/space/${space.hash_id}/dataset`}
            columns={[
                {
                    dataIndex: 'name',
                    title: '名称',
                    render({ record }) {
                        return <Stack gap={1}>
                            {record.name}
                            <span className='fs-7 text-muted'>{record.description || '暂无描述'}</span>
                        </Stack>;
                    }
                },
                {
                    dataIndex: 'size',
                    width: 100,
                    title: '文件大小',
                    align: 'center',
                    render({ value }) {
                        return `${byteSize(value)}`;
                    }
                },
                {
                    dataIndex: 'create_time',
                    title: '创建时间',
                    width: 150,
                },
                {
                    key: 'action',
                    title: '操作',
                    width: 120,
                    align: 'right',
                    render({ record }) {
                        if (value.includes(record.hash_id)) {
                            return <Button
                                onClick={() => {
                                    onChange(draft => {
                                        const index = draft.indexOf(record.hash_id);
                                        if (index !== -1) draft.splice(index, 1);
                                    });
                                }}
                                className={'link-danger'}
                            >移除</Button>;
                        } else {
                            return <Button
                                onClick={() => {
                                    onChange(draft => {
                                        draft.push(record.hash_id);
                                    });
                                }}
                            >添加</Button>;
                        }
                    }
                }
            ]} />
    </ModalButton>;
}
