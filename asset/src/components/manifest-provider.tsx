import { createContext, PropsWith<PERSON>hildren, useContext } from 'react';
import { Loader, Result, useRequest } from '@topthink/common';
import get from 'lodash/get';
import { Helmet } from 'react-helmet';
import logoSrc from '@/images/logo.svg';

const Context = createContext<Manifest | null>(null);

export default function ManifestProvider(props: PropsWithChildren) {
    const { result, error } = useRequest<Manifest>('/manifest', {
        onSuccess: (result) => {
            //自定义脚本
            const scripts = result.website.scripts;
            if (scripts) {
                const fragment = document.createRange().createContextualFragment(scripts);
                document.body.appendChild(fragment);
            }
        }
    });

    if (error) {
        return <Result status={'error'} title={error.message} />;
    }

    if (!result) {
        return <Loader />;
    }

    return <Context.Provider value={result}>
        <Helmet>
            <title>{result.website.title}</title>
            <link rel='shortcut icon' href={result.website.logo || logoSrc} type='image/png' />
        </Helmet>
        {props.children}
    </Context.Provider>;
}

function useManifest(): Manifest;
function useManifest<TKey1 extends keyof Manifest>(key1: TKey1): Manifest[TKey1];
function useManifest<TKey1 extends keyof Manifest, TKey2 extends keyof Manifest[TKey1]>(key1: TKey1, key2: TKey2): Manifest[TKey1][TKey2];
function useManifest<TKey1 extends keyof Manifest, TKey2 extends keyof Manifest[TKey1], TKey3 extends keyof Manifest[TKey1][TKey2]>(key1: TKey1, key2: TKey2, key3: TKey3): Manifest[TKey1][TKey2][TKey3];
function useManifest(...path: any[]) {
    const manifest = useContext(Context);
    if (!manifest) {
        throw new Error('useManifest must be used within a ManifestProvider');
    }
    if (path.length > 0) {
        return get(manifest, path);
    }
    return manifest;
}

export { useManifest };
