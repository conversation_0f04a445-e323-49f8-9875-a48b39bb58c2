import { Card, Result, useRouteError } from '@topthink/common';
import { useEffect } from 'react';
import { Container } from 'react-bootstrap';

export default function ErrorBoundary() {
    const error = useRouteError() as Error;

    useEffect(() => {
        console.error(error);
    }, [error]);

    return <Container className={'mt-5'}>
        <Card>
            <Result status={'error'} title={error.message} />
        </Card>
    </Container>;
}
