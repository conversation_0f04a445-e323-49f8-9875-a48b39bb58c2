import { useCurrentSpace } from '@/components/space-provider';
import { ModalForm, ModalFormProps } from '@topthink/common';

interface Props {
    button: Required<ModalFormProps>['buttonProps']['as'];
    onSuccess?: () => void;
}

export default function CreatePluginModal({ button, onSuccess }: Props) {
    const { current } = useCurrentSpace();

    return <ModalForm
        action={`/space/${current.hash_id}/plugin`}
        buttonProps={{ as: button }}
        text={<><i className='bi bi-plugin me-2' />创建插件</>}
        schema={{
            type: 'object',
            properties: {
                type: {
                    type: 'string',
                    title: '类型',
                    enum: [3, 4],
                    enumNames: ['OpenAPI', 'MCP'],
                    default: 3
                },
                title: {
                    type: 'string',
                    title: '名称'
                },
                description: {
                    type: 'string',
                    title: '描述'
                }
            },
            required: ['type', 'title']
        }}
        uiSchema={{
            title: {
                'ui:autofocus': true,
                'ui:placeholder': '输入插件名称'
            },
            description: {
                'ui:widget': 'textarea',
                'ui:placeholder': '输入插件描述',
                'ui:options': {
                    rows: 3
                }
            }
        }}
        onSuccess={onSuccess}
    />;
}
