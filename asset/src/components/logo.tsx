import { Link, styled } from '@topthink/common';
import logoSrc from '@/images/logo.svg';
import { useManifest } from './manifest-provider';

export default function Logo() {
    const { title, logo } = useManifest('website');

    return <Container>
        <Link className='navbar-brand d-flex align-items-center' to='/'>
            <img className={'me-3 rounded'} src={logo || logoSrc} width={25} height={25} />
            {title || 'ThinkBot'}
        </Link>
    </Container>;
}

const Container = styled.div`
    padding: 0 0.75rem;
    font-size: 1.25rem;
    --bs-navbar-brand-color: rgba(var(--bs-emphasis-color-rgb), 1);
`;
