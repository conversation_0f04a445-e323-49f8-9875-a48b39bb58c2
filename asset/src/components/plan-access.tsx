import { Card, LinkButton, Result } from '@topthink/common';
import { ReactNode } from 'react';
import { useCurrentSpace } from './space-provider';

export default function PlanAccess({ level, children }: { level: number, children: ReactNode }) {
    const { current } = useCurrentSpace();

    if (current.plan_level < level) {
        return <Card>
            <Result
                status={'error'}
                title={'当前订阅版本不支持'}
                extra={<LinkButton to={`/space/${current.hash_id}/billing`}>去升级</LinkButton>}
            />;
        </Card>;
    }

    return children;
}
