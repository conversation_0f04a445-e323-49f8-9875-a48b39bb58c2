import { Link, styled } from '@topthink/common';

interface Props {
    to: string,
}

export default function Back({ to }: Props) {
    return <StyledLink className='text-dark' to={to}>
        <i className='bi bi-chevron-left' />
    </StyledLink>;
}

const StyledLink = styled(Link)`
    --bs-btn-padding-y: 0.25rem;
    --bs-btn-padding-x: 0.5rem;
    --bs-btn-font-size: 0.875rem;
    border-radius: 0.25rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--bs-btn-padding-y) var(--bs-btn-padding-x);
    font-size: var(--bs-btn-font-size);

    &:hover {
        background-color: #e9ecef;
    }
`;
