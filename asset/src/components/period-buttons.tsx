import { Button } from '@topthink/common';
import { forwardRef } from 'react';
import { ButtonGroup, ButtonProps } from 'react-bootstrap';

interface Props {
    className?: string;
    period: string;
    onChange: (period: string) => void;
    getButtonProps?: (period: string) => Partial<ButtonProps>;
    periods?: string[];
}

const PERIODS = {
    '24hours': '最近 24 小时',
    'yesterday': '昨天',
    '3days': '最近 3 天',
    '7days': '最近 7 天',
    '30days': '最近 30 天',
    'last-month': '上个月',
    '90days': '最近 90 天',
    '180days': '最近 180 天',
    '6months': '最近 6 个月',
    '1year': '最近 1 年',
};

const PeriodButtons = forwardRef<HTMLDivElement, Props>(({
    className,
    period,
    periods = ['24hours', '7days', '30days', '90days'],
    onChange,
    getButtonProps
}, ref) => {

    return <ButtonGroup className={className} ref={ref}>
        {Object.entries(PERIODS).filter(([key]) => periods.includes(key)).map(([key, value]) => {
            const props = getButtonProps?.(key) ?? {};

            return <Button
                key={key}
                variant={'outline-primary'}
                {...props}
                active={period === key}
                onClick={() => onChange(key)}>{value}</Button>;
        })}
    </ButtonGroup>;
});

export default PeriodButtons;
