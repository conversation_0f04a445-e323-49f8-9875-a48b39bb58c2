import { SiderLayout as BsSiderLayout, styled, } from '@topthink/common';
import { ComponentProps, ReactNode } from 'react';
import Logo from './logo';

interface Props extends ComponentProps<typeof BsSiderLayout> {
    header?: ReactNode;
    logo?: ReactNode;
}

export default function SiderLayout({ logo, header, footer }: Props) {
    return <BsSiderLayout
        top={0}
        headerAs={function() {
            return <>
                <Header>
                    {logo || <Logo />}
                </Header>
                {header}
            </>;
        }}
        footer={footer}
    />;
}


const Header = styled.div`
    height: 65px;
    padding: 1rem;
    border-bottom: 1px solid #e3e3e3;
    display: flex;
    align-items: center;
`;

