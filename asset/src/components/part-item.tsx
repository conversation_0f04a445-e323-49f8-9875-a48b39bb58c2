import { Markdown } from '@topthink/chat';
import { Card, ModalButton, styled } from '@topthink/common';
import { forwardRef, ReactNode } from 'react';
import { Badge } from 'react-bootstrap';

interface Props {
    index: number;
    children: ReactNode;
    action?: ReactNode;
}

export default function PartItem({ index, action, children }: Props) {

    if (typeof children === 'string') {
        children = <Markdown content={children} />;
    } else {
        children = <div className={'text-pre-wrap'}>{children}</div>;
    }

    return <PartCard>
        <div className='mb-3 d-flex align-items-center'>
            <Badge bg={'secondary'} className={'me-auto'}><i className='bi bi-hash me-1' />{index + 1}</Badge>
            {action}
        </div>
        <ModalButton
            text={<><i className='bi bi-hash me-1' />{index + 1}</>}
            modalProps={{ scrollable: true, footer: false, size: 'lg' }}
            as={forwardRef<HTMLDivElement>((props, ref) => {
                return <PartContent {...props} ref={ref}>
                    {children}
                </PartContent>;
            })}
        >
            <PartContent>
                {children}
            </PartContent>
        </ModalButton>
    </PartCard>;
}

const PartCard = styled(Card)`
    height: 190px;
    cursor: pointer;

    .card-body {
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    &:hover {
        box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
    }
`;

const PartContent = styled.div`
    flex: 1;
    overflow: hidden;
`;
