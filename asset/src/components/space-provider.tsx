import { createContext, Props<PERSON><PERSON><PERSON><PERSON>dren, useContext, useEffect, useMemo } from 'react';
import { useParams, useRequest, useUser } from '@topthink/common';
import Assistant from './assistant';

interface SpaceContextType {
    spaces: Space[];
    current: Space | undefined;
    refresh: () => Promise<void>;
}

const SpaceContext = createContext<SpaceContextType | null>(null);

export default function SpaceProvider({ children }: PropsWithChildren) {
    const { setUser } = useUser();
    const { result: spaces, refresh } = useRequest<Space[]>('/space', {
        setLoading(state) {
            return {
                ...state,
                loading: true,
            };
        }
    });
    const { space: id } = useParams();

    const current = useMemo(() => {
        if (!spaces) {
            return undefined;
        }
        return spaces.find((space) => space.hash_id === id);
    }, [id, spaces]);

    useEffect(() => {
        if (current) {
            setUser(user => {
                return {
                    ...user,
                    access_level: current.pivot.access_level
                };
            });
        }
    }, [current]);

    if (!spaces) {
        return null;
    }

    return <SpaceContext.Provider value={{
        spaces,
        current,
        refresh
    }}>
        {children}
        {(current && current.pivot.access_level >= 40) && <Assistant key={current.hash_id} />}
    </SpaceContext.Provider>;
}

export function useCurrentSpace() {
    const space = useSpace();
    return {
        current: space.current!,
        refresh: space.refresh
    };
}

export function useSpace() {
    const context = useContext(SpaceContext);

    if (!context) {
        throw new Error('not found SpaceProvider');
    }
    return context;
}
