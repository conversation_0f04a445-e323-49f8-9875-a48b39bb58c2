import { css, styled, Tooltip } from '@topthink/common';
import { forwardRef, <PERSON><PERSON><PERSON>Handler, ReactNode } from 'react';

interface Props {
    children: ReactNode;
    tooltip?: string;
    placement?: 'top' | 'bottom';
    onClick?: MouseEventHandler<HTMLDivElement>;
    onMouseDown?: MouseEventHandler<HTMLDivElement>;
    className?: string;
    size?: 'sm';
}

const ActionButton = forwardRef<HTMLDivElement, Props>(({
    className,
    children,
    size,
    tooltip,
    placement,
    onClick,
    onMouseDown
}, ref) => {
    const component = <Container
        ref={ref}
        className={className}
        onClick={onClick}
        onMouseDown={onMouseDown}
        $size={size}
    >{children}</Container>;

    if (tooltip) {
        return <Tooltip tooltip={tooltip} placement={placement}>
            {component}
        </Tooltip>;
    }

    return component;
});

export default ActionButton;

const Container = styled.div<{ $size?: 'sm' }>`
    width: 2rem;
    height: 2rem;
    align-items: center;
    justify-content: center;
    border-radius: .375rem;
    cursor: pointer;
    font-size: 1.2rem;
    color: var(--bs-secondary);
    display: flex;

    svg {
        font-size: 1.5rem;
    }

    ${({ $size }) => $size === 'sm' && css`
        width: 1.5rem;
        height: 1.5rem;
        font-size: 1rem;
    `};

    &:hover {
        background-color: var(--bs-secondary-bg-subtle);
    }
`;
