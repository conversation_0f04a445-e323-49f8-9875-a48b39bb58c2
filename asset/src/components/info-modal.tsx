import {
    css,
    Loader,
    ModalButton,
    ModalButtonProps,
    ModalType,
    request,
    RequestConfig,
    styled
} from '@topthink/common';
import { createElement, ReactNode, useCallback, useRef, useState } from 'react';

type ChildrenProps<T, P extends Record<string, any>> = {
    data: T;
    action: ModalType;
} & P;

interface Props<T, P extends Record<string, any>> extends Omit<ModalButtonProps, 'size'> {
    source?: RequestConfig;
    data?: T;
    renderChildren: (props: ChildrenProps<T, P>) => JSX.Element;
    compact?: boolean;
    footer?: boolean;
    okText?: string;
    header?: ReactNode;
    childrenProps?: P;
    size?: 'sm' | 'lg' | 'xl';
}

export default function InfoModal<T = any, P extends Record<string, any> = {}>({
    text,
    source,
    data,
    renderChildren,
    compact,
    variant = 'link',
    footer,
    okText,
    header = text,
    size,
    modalProps,
    childrenProps,
    ...props
}: Props<T, P>) {

    footer = footer !== true ? false : undefined;

    const [children, setChildren] = useState(() => <Loader />);

    const ref = useRef<ModalType>(null);

    const handleShow = useCallback(async () => {
        setChildren(createElement(renderChildren, {
            data: data ?? (source ? await request(source) : null) as T,
            action: ref.current!,
            ...(childrenProps as P)
        }));
    }, [renderChildren, childrenProps, source, data]);

    return <ModalButton
        ref={ref}
        text={text}
        variant={variant}
        onShow={handleShow}
        modalProps={{
            size,
            header,
            footer,
            okText,
            ...modalProps
        }}
        {...props}
    >
        <Container compact={compact}>
            {children}
        </Container>
    </ModalButton>;
}

const Container = styled.div<{ compact?: boolean; }>`
  min-height: 200px;
  ${({ compact }) => compact && css`
    margin: -1rem;
  `}
`;
