import { MessageBox } from '@topthink/chat';
import { request, styled, useRequest } from '@topthink/common';
import { useState } from 'react';
import BotIconString, { ReactComponent as BotIcon } from '../images/bot.svg';
import { ReactComponent as DownIcon } from '../images/down.svg';
import { useCurrentSpace } from './space-provider';

export default function Assistant() {
    const { current } = useCurrentSpace();
    const [show, setShow] = useState(false);

    const bot = {
        avatar: BotIconString,
        name: '启智小助手',
        description: 'ThinkBot智能助手'
    };

    const { result } = useRequest(`/space/${current.hash_id}/assistant/chat`, {
        setResult: ({ conversation }, state) => {
            const req = request.create({
                baseURL: `space/${current.hash_id}/assistant/chat`,
            });

            return {
                ...state,
                result: {
                    conversation,
                    req
                },
            };
        }
    });

    if (result === undefined) {
        return null;
    }

    const { conversation, req } = result;

    return <>
        <Window $show={show}>
            <Header>
                <Avatar>
                    <img src={bot.avatar} />
                </Avatar>
                <Info>
                    <Nickname>{bot.name}</Nickname>
                    <Signature>{bot.description}</Signature>
                </Info>
                <Action>
                    <Down onClick={() => setShow(false)}><DownIcon width={18} height={18} /></Down>
                </Action>
            </Header>
            <Body>
                {show && <MessageBox
                    request={req}
                    bot={bot}
                    onboarding={{
                        enable: true,
                        prologue: 'Hi，我是你的智能小助手，还可以帮你分析智能体的对话记录，使用过程中有任何问题请随时问我。',
                        questions: []
                    }}
                    conversation={conversation}
                    input={{}}
                />}
            </Body>
        </Window>
        <Button onClick={() => setShow(!show)}>
            <BotIcon />
        </Button>
    </>;
}


const Action = styled.div`

`;

const Body = styled.div`
    flex: 1;
    background: #fafafa;
    overflow: hidden;
`;

const Signature = styled.div`
    word-break: keep-all;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 13px;
    color: rgba(255, 255, 255, 0.8);
`;

const Nickname = styled.div`
    font-size: 16px;
    word-break: keep-all;
    overflow: hidden;
    text-overflow: ellipsis;
`;

const Info = styled.div`
    flex: 1;
    font-weight: normal;
    overflow: hidden;
`;

const Avatar = styled.div`
    position: relative;
    background: rgba(255, 255, 255, 0.8);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--bs-primary);

    img, svg {
        border-radius: 50%;
        width: 100%;
        height: 100%;
    }

`;

const Header = styled.div`
    height: 75px;
    background: var(--bs-primary);
    overflow: hidden;
    padding: 0 20px 0 20px;
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.95);
`;

const Down = styled.div`
    color: #FFFFFF;
    cursor: pointer;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
`;

const Window = styled.div<{ $show: boolean }>`
    position: fixed;
    z-index: 4001;
    height: calc(100% - 20px - 75px - 20px);
    bottom: 1rem;
    right: 1rem;
    width: 420px;
    min-width: 300px;
    max-width: 100%;
    min-height: 250px;
    max-height: 700px;
    box-shadow: 0 5px 40px rgba(0, 0, 0, .16);
    border-radius: 8px;
    overflow: hidden;
    flex-direction: column;
    display: ${props => props.$show ? 'flex' : 'none'};

    @media screen and (max-width: 600px) {
        width: 100%;
        bottom: 0;
        left: 0;
        right: 0;
        border-radius: 0;
        height: calc(100% - 75px);
    }
`;

const Button = styled.div`
    position: fixed;
    bottom: -26px;
    left: auto;
    right: 60px;
    z-index: 4000;
    overflow: hidden;
    cursor: pointer;
    font-size: 50px;
    display: flex;
    transition: bottom 0.1s ease-out;

    &:hover {
        bottom: -2px;
    }
`;
