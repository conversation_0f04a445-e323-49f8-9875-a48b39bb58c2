import { ModalForm, ModalFormProps, useNavigate } from '@topthink/common';
import { useCurrentSpace } from '@/components/space-provider';
import LevelAccess from '@/components/level-access';
import { DEVELOPER } from '@/utils/constants';

interface Props {
    button: Required<ModalFormProps>['buttonProps']['as'];
}

export default function CreateBotModal({ button }: Props) {
    const { current } = useCurrentSpace();
    const navigate = useNavigate();

    return <LevelAccess level={DEVELOPER}>
        <ModalForm
            action={`/space/${current.hash_id}/bot`}
            buttonProps={{ as: button }}
            text={<><i className='bi bi-robot me-2' />创建智能体</>}
            schema={{
                type: 'object',
                properties: {
                    type: {
                        type: 'string',
                        title: '类型',
                        enum: ['agent', 'flow'],
                        enumNames: ['提示词', '工作流'],
                        default: 'agent'
                    },
                    name: {
                        type: 'string',
                        title: '名称'
                    },
                    description: {
                        type: 'string',
                        title: '描述'
                    }
                },
                required: ['type', 'name']
            }}
            uiSchema={{
                type: {
                    'ui:widget': 'radio',
                    'ui:options': {
                        button: true
                    }
                },
                name: {
                    'ui:autofocus': true,
                    'ui:placeholder': '输入智能体名称'
                },
                description: {
                    'ui:widget': 'textarea',
                    'ui:placeholder': '输入智能体描述',
                    'ui:options': {
                        rows: 3
                    }
                }
            }}
            onSuccess={async (data) => {
                navigate(`/space/${current.hash_id}/bot/${data.hash_id}/configuration`);
            }}
        />
    </LevelAccess>;
}
