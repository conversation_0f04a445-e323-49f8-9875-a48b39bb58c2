import { ModalButtonProps, User } from '@topthink/common';
import InfoModal from './info-modal';

interface Props extends Omit<ModalButtonProps, 'id'> {
    id?: number;
    user?: User;
}

export default function UserModal({ id, user, ...props }: Props) {

    return <InfoModal
        source={id ? `/user/${id}` : undefined}
        header={'用户信息'}
        data={user}
        {...props}
        renderChildren={({ data }) => {
            return <>
                <dl className='row'>
                    <dd className='col-2'>昵称</dd>
                    <dd className='col-10'>{data.name}</dd>
                </dl>
                <dl className='row'>
                    <dd className='col-2'>邮箱</dd>
                    <dd className='col-10'>{data.email || '--'}</dd>
                </dl>
                <dl className='row'>
                    <dd className='col-2'>手机</dd>
                    <dd className='col-10'>{data.mobile || '--'}</dd>
                </dl>
            </>;
        }}
    />;
}
