import { Col, Row } from 'react-bootstrap';
import { PropsWithChildren } from 'react';

export default function Container({ children, className }: PropsWithChildren<any>) {
    return <div className={'container vh-100 d-flex align-items-center'}>
        <Row className={'flex-fill'}>
            <Col xs={{ span: 6, offset: 3 }} className={className}>
                {children}
            </Col>
        </Row>
    </div>;
}
