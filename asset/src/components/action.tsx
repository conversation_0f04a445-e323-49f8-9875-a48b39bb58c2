import { styled } from "@topthink/common";

export const ActionIcon = styled.div<{ $variant?: string }>`
    width: 1.75rem;
    height: 1.75rem;
    align-items: center;
    justify-content: center;
    border-radius: .375rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--bs-secondary);
    display: flex;

    &:hover {
        color: var(--bs-${props => props.$variant || 'primary'});
        background-color: var(--bs-${props => props.$variant || 'primary'}-bg-subtle);
    }
`;

export const RemoveIcon = styled(ActionIcon)
.attrs(() => ({ children: <i className='bi bi-trash3' />, $variant: 'danger' }))`

`;
