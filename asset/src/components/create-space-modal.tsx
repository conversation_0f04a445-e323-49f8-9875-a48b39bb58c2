import { ModalForm, ModalFormProps, useNavigate, useUser } from '@topthink/common';
import { useSpace } from '@/components/space-provider';
import { ReactNode } from 'react';
import { useManifest } from '@/components/manifest-provider';

interface Props {
    button: Required<ModalFormProps>['buttonProps']['as'];
    text?: ReactNode;
}

export default function CreateSpaceModal({ button, text = <><i className='bi bi-boxes me-2' />创建空间</> }: Props) {
    const { refresh } = useSpace();
    const { cloud } = useManifest();
    const { user } = useUser();

    const navigate = useNavigate();

    if (!cloud && !user.is_admin) {
        return null;
    }

    return <ModalForm
        action={'/space'}
        buttonProps={{ as: button }}
        text={text}
        modalProps={{ header: '创建空间' }}
        schema={{
            type: 'object',
            properties: {
                name: {
                    type: 'string',
                    title: '空间名称'
                }
            }
        }}
        uiSchema={{
            name: {
                'ui:autofocus': true,
                'ui:placeholder': '请输入空间名称'
            }
        }}
        onSuccess={async (data) => {
            await refresh();
            navigate(`/space/${data.hash_id}`);
        }}
    />;
}
