import { useCurrentSpace } from '@/components/space-provider';
import { ModalForm, useNavigate } from '@topthink/common';
import { ButtonGroup, Dropdown } from 'react-bootstrap';

interface Props {
}

export default function CreateDatasetModal({}: Props) {
    const { current } = useCurrentSpace();
    const navigate = useNavigate();

    return <Dropdown as={ButtonGroup} align={'end'}>
        <ModalForm
            action={`/space/${current.hash_id}/dataset`}
            text={<><i className='bi bi-journal-text me-2' />创建知识库</>}
            schema={{
                type: 'object',
                properties: {
                    name: {
                        type: 'string',
                        title: '名称'
                    },
                    description: {
                        type: 'string',
                        title: '描述'
                    }
                },
                required: ['name']
            }}
            uiSchema={{
                name: {
                    'ui:autofocus': true,
                    'ui:placeholder': '输入知识库名称'
                },
                description: {
                    'ui:widget': 'textarea',
                    'ui:placeholder': '输入知识库描述',
                    'ui:options': {
                        rows: 3
                    }
                }
            }}
            onSuccess={(data) => {
                navigate(`/space/${current.hash_id}/dataset/${data.hash_id}`);
            }}
        />
        <Dropdown.Toggle split />
        <Dropdown.Menu>
            <ModalForm
                action={`/space/${current.hash_id}/dataset`}
                transformData={data => {
                    return {
                        ...data,
                        type: 'web'
                    };
                }}
                buttonProps={{ as: Dropdown.Item }}
                text={<><i className={'bi bi-globe-americas me-2'} />同步自Web站点</>}
                schema={{
                    type: 'object',
                    properties: {
                        name: {
                            type: 'string',
                            title: '名称'
                        },
                        sitemap: {
                            type: 'string',
                            title: '站点地图(sitemap)'
                        },
                        freq: {
                            type: 'string',
                            title: '更新频率',
                            enum: [0, 1, 3, 7, 30],
                            enumNames: ['不自动更新', '每 1 天', '每 3 天', '每 7 天', '每 30 天'],
                            default: 0
                        },
                        description: {
                            type: 'string',
                            title: '描述'
                        }
                    },
                    required: ['name', 'sitemap']
                }}
                uiSchema={{
                    name: {
                        'ui:autofocus': true,
                        'ui:placeholder': '输入知识库名称'
                    },
                    sitemap: {
                        'ui:placeholder': 'https://www.example.com/sitemap.xml'
                    },
                    freq: {
                        'ui:widget': 'select',
                    },
                    description: {
                        'ui:widget': 'textarea',
                        'ui:placeholder': '输入知识库描述',
                        'ui:options': {
                            rows: 3
                        }
                    }
                }}
                onSuccess={(data) => {
                    navigate(`/space/${current.hash_id}/dataset/${data.hash_id}`);
                }}
            />
        </Dropdown.Menu>
    </Dropdown>;
}
