import { Navigate, Outlet, RouteObject, TabLayout, WithRequest } from '@topthink/common';
import ErrorBoundary from './components/error-boundary';
import Layout from './layout';
import { DEVELOPER, MASTER } from '@/utils/constants';

const routes: RouteObject[] = [
    {
        element: <Layout />,
        errorElement: <ErrorBoundary />,
        children: [
            {
                index: true,
                lazy: () => import('./pages/home')
            },
            {
                path: 'space/:space',
                lazy: () => import('./pages/space'),
                children: [
                    {
                        index: true,
                        element: <Navigate to={'bot'} replace />
                    },
                    {
                        path: 'bot',
                        element: <Outlet />,
                        meta: {
                            title: '智能体',
                            icon: 'robot'
                        },
                        children: [
                            {
                                index: true,
                                lazy: () => import('./pages/bot'),
                            },
                            {
                                path: ':bot',
                                lazy: () => import('./pages/bot/provider'),
                                children: [
                                    {
                                        index: true,
                                        element: <Navigate to={'chat'} replace />
                                    },
                                    {
                                        path: 'chat',
                                        lazy: () => import('./pages/bot/chat'),
                                    },
                                    {
                                        lazy: () => import('./pages/bot/layout'),
                                        children: [
                                            {
                                                path: 'configuration',
                                                lazy: () => import('./pages/bot/configuration'),
                                                meta: {
                                                    title: '编排',
                                                    icon: 'sliders'
                                                },
                                            },
                                            {
                                                path: 'integration',
                                                element: <Outlet />,
                                                meta: {
                                                    title: '集成',
                                                    icon: 'cpu',
                                                    hideChildrenInMenu: true
                                                },
                                                children: [
                                                    {
                                                        index: true,
                                                        lazy: () => import('./pages/bot/integration'),
                                                    },
                                                    {
                                                        path: 'key',
                                                        lazy: () => import('./pages/bot/integration/key'),
                                                        meta: {
                                                            title: 'API'
                                                        }
                                                    },
                                                    {
                                                        path: 'share',
                                                        lazy: () => import('./pages/bot/integration/share'),
                                                        meta: {
                                                            title: '分享'
                                                        }
                                                    },
                                                    {
                                                        path: 'embed',
                                                        lazy: () => import('./pages/bot/integration/embed'),
                                                        meta: {
                                                            title: '嵌入'
                                                        }
                                                    },
                                                    {
                                                        path: 'wkf',
                                                        lazy: () => import('./pages/bot/integration/wkf'),
                                                        meta: {
                                                            title: '微信客服'
                                                        }
                                                    },
                                                    {
                                                        path: 'wmp',
                                                        lazy: () => import('./pages/bot/integration/wmp'),
                                                        meta: {
                                                            title: '微信公众号'
                                                        }
                                                    },
                                                    {
                                                        path: 'qq',
                                                        lazy: () => import('./pages/bot/integration/qq'),
                                                        meta: {
                                                            title: 'QQ机器人'
                                                        }
                                                    },
                                                    {
                                                        path: 'ding',
                                                        lazy: () => import('./pages/bot/integration/come-soon'),
                                                        meta: {
                                                            title: '钉钉机器人'
                                                        }
                                                    },
                                                    {
                                                        path: 'lark',
                                                        lazy: () => import('./pages/bot/integration/come-soon'),
                                                        meta: {
                                                            title: '飞书机器人'
                                                        }
                                                    }
                                                ]
                                            },
                                            {
                                                path: 'log',
                                                lazy: () => import('./pages/bot/log'),
                                                meta: {
                                                    title: '日志',
                                                    icon: 'card-list'
                                                },
                                            },
                                            {
                                                path: 'annotation',
                                                lazy: () => import('@/pages/bot/annotation'),
                                                meta: {
                                                    title: '标注',
                                                    icon: 'bookmark-check'
                                                }
                                            }
                                        ]
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        path: 'dataset',
                        element: <Outlet />,
                        meta: {
                            title: '知识库',
                            icon: 'journal-text',
                            access: DEVELOPER
                        },
                        children: [
                            {
                                index: true,
                                lazy: () => import('./pages/dataset')
                            },
                            {
                                path: ':dataset',
                                lazy: () => import('./pages/dataset/provider'),
                                children: [
                                    {
                                        index: true,
                                        element: <Navigate to={'source'} replace />
                                    },
                                    {
                                        path: 'source',
                                        lazy: () => import('./pages/dataset/source'),
                                        meta: {
                                            title: '文档管理',
                                            icon: 'file-text'
                                        },
                                    },
                                    {
                                        path: 'source/:id',
                                        lazy: () => import('./pages/dataset/source/part'),
                                    },
                                    {
                                        path: 'recall',
                                        lazy: () => import('./pages/dataset/recall'),
                                        meta: {
                                            title: '召回测试',
                                            icon: 'search'
                                        },
                                    },
                                ]
                            }
                        ]
                    },
                    {
                        path: 'database',
                        element: <Outlet />,
                        meta: {
                            title: '数据库',
                            icon: 'database',
                            access: DEVELOPER
                        },
                        children: [
                            {
                                index: true,
                                lazy: () => import('./pages/database')
                            },
                            {
                                path: ':database',
                                lazy: () => import('./pages/database/provider'),
                                children: [
                                    {
                                        index: true,
                                        element: <Navigate to={'field'} replace />
                                    },
                                    {
                                        path: 'field',
                                        lazy: () => import('./pages/database/field'),
                                        meta: {
                                            title: '字段管理',
                                            icon: 'list-columns'
                                        },
                                    },
                                    {
                                        path: 'record',
                                        lazy: () => import('./pages/database/record'),
                                        meta: {
                                            title: '数据管理',
                                            icon: 'table'
                                        },
                                    },
                                ]
                            }
                        ]
                    },
                    {
                        path: 'plugin',
                        lazy: () => import('./pages/plugin'),
                        meta: {
                            title: '插件',
                            icon: 'plugin',
                            access: DEVELOPER
                        },
                    },
                    {
                        path: 'member',
                        lazy: () => import('./pages/member'),
                        meta: {
                            title: '团队协作',
                            icon: 'people'
                        }
                    },
                    {
                        path: 'billing',
                        element: <WithRequest request={`/space/:space/billing`}>
                            <TabLayout showBack={false} />
                        </WithRequest>,
                        meta: {
                            title: '订阅充值',
                            hideInMenu: true
                        },
                        children: [
                            {
                                index: true,
                                element: <Navigate to={'subscribe'} replace={true} />
                            },
                            {
                                path: 'subscribe',
                                lazy: () => import('./pages/billing/subscribe'),
                                meta: {
                                    title: '订阅计划',
                                }
                            },
                            {
                                path: 'recharge',
                                lazy: () => import('./pages/billing/recharge'),
                                meta: {
                                    title: '增值服务',
                                }
                            }
                        ]
                    },
                    {
                        path: 'setting',
                        element: <TabLayout showBack={false} />,
                        meta: {
                            title: '空间管理',
                            icon: 'gear',
                            hideChildrenInMenu: true,
                            access: MASTER
                        },
                        children: [
                            {
                                index: true,
                                element: <Navigate to='overview' replace />
                            },
                            {
                                path: 'overview',
                                lazy: () => import('./pages/setting/overview'),
                                meta: {
                                    title: '空间概览',
                                }
                            },
                            {
                                path: 'info',
                                lazy: () => import('./pages/setting/info'),
                                meta: {
                                    title: '基本信息',
                                }
                            },
                            {
                                path: 'token',
                                lazy: () => import('./pages/setting/token'),
                                meta: {
                                    title: '访问令牌',
                                }
                            },
                        ]
                    }
                ]
            },
            {
                path: 'admin',
                lazy: () => import('@/pages/admin/layout'),
                meta: {
                    title: '后台管理',
                    hideInMenu: true,
                },
                children: [
                    {
                        index: true,
                        element: <Navigate to={'dashboard'} replace />
                    },
                    {
                        path: 'dashboard',
                        lazy: () => import('@/pages/admin/dashboard'),
                        meta: {
                            title: '概览',
                            icon: 'speedometer2'
                        }
                    },
                    {
                        path: 'space',
                        lazy: () => import('@/pages/admin/space'),
                        meta: {
                            title: '空间管理',
                            icon: 'building'
                        }
                    },
                    {
                        path: 'user',
                        lazy: () => import('@/pages/admin/user'),
                        meta: {
                            title: '用户管理',
                            icon: 'people'
                        }
                    },
                    {
                        path: 'plugin',
                        lazy: () => import('@/pages/admin/plugin'),
                        meta: {
                            title: '插件管理',
                            icon: 'plugin'
                        }
                    },

                    {
                        path: 'setting',
                        element: <TabLayout />,
                        meta: {
                            title: '系统设置',
                            icon: 'gear',
                            hideChildrenInMenu: true,
                        },
                        children: [
                            {
                                index: true,
                                element: <Navigate to={'website'} replace />
                            },
                            {
                                path: 'website',
                                lazy: () => import('@/pages/admin/setting/website'),
                                meta: {
                                    title: '网站设置',
                                }
                            },
                            {
                                path: 'model',
                                lazy: () => import('@/pages/admin/setting/model'),
                                meta: {
                                    title: '模型设置',
                                }
                            },
                            {
                                path: 'login',
                                lazy: () => import('@/pages/admin/setting/login'),
                                meta: {
                                    title: '登录设置',
                                }
                            },
                        ]
                    }
                ]
            },
            {
                path: 'invite/:code',
                lazy: () => import('./pages/invite')
            },
        ]
    }
];

export default routes;
