apiVersion: apps/v1
kind: Deployment
metadata:
  name: bot
  namespace: topthink
  labels:
    app: bot
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bot
  template:
    metadata:
      labels:
        app: bot
    spec:
      initContainers:
        - name: init
          image: registry-vpc.cn-shanghai.aliyuncs.com/topthink/bot:IMAGE_TAG
          imagePullPolicy: Always
          args:
            - 'app:init'
          env:
            - name: PHP_APP_TOKEN
              value: "1234567890qwertyuiopasdfghjklzxcvbnm"
            - name: PHP_DB_NAME
              value: bot
            - name: PHP_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: db-config
                  key: host
            - name: PHP_DB_USER
              valueFrom:
                secretKeyRef:
                  name: db-config
                  key: user
            - name: PHP_DB_PASS
              valueFrom:
                secretKeyRef:
                  name: db-config
                  key: password
            - name: PHP_CACHE_TYPE
              value: redis
            - name: PHP_REDIS_HOST
              valueFrom:
                configMapKeyRef:
                  name: common-config
                  key: redis-host
            - name: PHP_REDIS_DB
              value: "14"
            - name: PHP_CLOUD_ENABLE
              value: "true"
            - name: PHP_CLOUD_CLIENT_ID
              value: "26e758e05b76176ba42615de648bbcfb"
            - name: PHP_CLOUD_CLIENT_SECRET
              value: "6787c397286ff6892835389a86cbfe39"
            - name: aliyun_logs_bot
              value: /opt/htdocs/runtime/log/*.log
            - name: aliyun_logs_bot_project
              value: topthink
          resources:
            requests:
              cpu: 250m
              memory: 512Mi
          volumeMounts:
            - name: volume-log
              mountPath: /opt/htdocs/runtime/log/
      containers:
        - name: main
          image: registry-vpc.cn-shanghai.aliyuncs.com/topthink/bot:IMAGE_TAG
          imagePullPolicy: Always
          env:
            - name: PHP_APP_TOKEN
              value: "1234567890qwertyuiopasdfghjklzxcvbnm"
            - name: PHP_DB_NAME
              value: bot
            - name: PHP_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: db-config
                  key: host
            - name: PHP_DB_USER
              valueFrom:
                secretKeyRef:
                  name: db-config
                  key: user
            - name: PHP_DB_PASS
              valueFrom:
                secretKeyRef:
                  name: db-config
                  key: password
            - name: PHP_CACHE_TYPE
              value: redis
            - name: PHP_REDIS_HOST
              valueFrom:
                configMapKeyRef:
                  name: common-config
                  key: redis-host
            - name: PHP_REDIS_DB
              value: "14"
            - name: PHP_CLOUD_ENABLE
              value: "true"
            - name: PHP_CLOUD_CLIENT_ID
              value: "26e758e05b76176ba42615de648bbcfb"
            - name: PHP_CLOUD_CLIENT_SECRET
              value: "6787c397286ff6892835389a86cbfe39"
            - name: PHP_QDRANT_HOST
              value: qdrant-svc.thirdparty
            - name: aliyun_logs_bot
              value: /opt/htdocs/runtime/log/*.log
            - name: aliyun_logs_bot_project
              value: topthink
          resources:
            requests:
              cpu: 250m
              memory: 512Mi
          ports:
            - name: main
              containerPort: 80
              protocol: TCP
          volumeMounts:
            - name: volume-log
              mountPath: /opt/htdocs/runtime/log/
            - name: volume-storage
              mountPath: /opt/htdocs/storage/
              subPath: ./bot
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 15
            periodSeconds: 10
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 1
          startupProbe:
            failureThreshold: 3
            initialDelaySeconds: 15
            periodSeconds: 10
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 1
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 15
            periodSeconds: 10
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 1
      volumes:
        - name: volume-log
          emptyDir: { }
        - name: volume-storage
          persistentVolumeClaim:
            claimName: topthink-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: bot-svc
  namespace: topthink
spec:
  ports:
    - name: main
      port: 80
      protocol: TCP
      targetPort: 80
  selector:
    app: bot
  type: ClusterIP
