<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;
use yunwuxin\auth\middleware\Authentication;

Route::group('api', function () {

    Route::get('manifest', 'manifest/index');

    Route::group(function () {
        Route::get('auth/current', 'auth/current');

        Route::resource('space', 'space');

        Route::group('user', function () {
            Route::post('name', 'user/name');
            Route::post('avatar', 'user/avatar');
        });

        Route::group('admin', function () {
            Route::group('statistic', function () {
                Route::get('basic', 'statistic/basic');
                Route::get('user', 'statistic/user');
                Route::get('chat', 'statistic/chat');
            });

            Route::resource('user', 'user');
            Route::post('user/:id/status', 'user/status');

            Route::resource('space', 'space');
            Route::post('space/:id/plan', 'space/plan');

            Route::resource('plugin', 'plugin');
            Route::post('plugin/:id/status', 'plugin/status');
            Route::post('plugin/:id/sort', 'plugin/sort');

            Route::get('model', 'model/index');

            Route::get('setting/:name', 'setting/read');
            Route::post('setting/:name', 'setting/update');
        })->prefix('admin.');
    })->middleware(Authentication::class);

    Route::group(function () {
        Route::get('model/:type', 'model/index');

        Route::group('space/:space_id/assistant', function () {
            Route::get('chat', 'chat/index');
            Route::post('chat', 'chat/save');
        })->prefix('assistant.');

        Route::get('space/:space_id/bot/search', 'bot.index/search');

        Route::resource('space.bot', 'bot.index');
        Route::post('space/:space_id/bot/:id/config', 'bot.index/config');
        Route::get('space/:space_id/bot/:id/chat', 'bot.chat/index');
        Route::post('space/:space_id/bot/:id/chat', 'bot.chat/save');
        Route::post('space/:space_id/bot/:id/chat/upload', 'bot.chat/upload');
        Route::post('space/:space_id/bot/:id/chat/suggestion', 'bot.chat/suggestion');
        Route::get('space/:space_id/bot/:id/chat/conversation', 'bot.chat/conversation');
        Route::post('space/:space_id/bot/:id/chat/speech', 'bot.audio/speech');
        Route::post('space/:space_id/bot/:id/chat/transcriptions', 'bot.audio/transcriptions');

        Route::group('space/:space_id/bot/:bot_id', function () {
            Route::get('conversation', 'bot.conversation/index');
            Route::get('conversation/:id/message', 'bot.conversation/message');

            Route::get('plugin', 'bot.plugin/index');

            Route::get('credentials', 'bot.credentials/index');
            Route::post('credentials', 'bot.credentials/save');
            Route::delete('credentials', 'bot.credentials/delete');

            Route::group('integration', function () {
                Route::resource('share', 'share');

                Route::resource('key', 'key');

                Route::get('embed', 'embed/index');
                Route::post('embed', 'embed/save');

                Route::get('wkf', 'wkf/index');
                Route::post('wkf', 'wkf/save');

                Route::get('wmp', 'wmp/index');
                Route::post('wmp', 'wmp/save');

                Route::get('qq', 'qq/index');
                Route::post('qq', 'qq/save');
            })->prefix('bot.integration.');

            Route::resource('annotation', 'bot.annotation');
        });

        Route::post('space/:space_id/dataset/:id/sync', 'dataset.index/sync');
        Route::post('space/:space_id/dataset/:id/recall', 'dataset.index/recall');
        Route::resource('space.dataset', 'dataset.index');
        Route::resource('space.dataset.source', 'dataset.source');
        Route::post('space/:space_id/dataset/:dataset_id/source/:id/train', 'dataset.source/train');
        Route::resource('space.dataset.source.part', 'dataset.part');

        // 数据库管理
        Route::resource('space.database', 'database.index');
        Route::resource('space.database.field', 'database.field');
        Route::resource('space.database.record', 'database.record');
        Route::post('space/:space_id/database/:database_id/record/import', 'database.record/import');
        Route::post('space/:space_id/database/:database_id/record/export', 'database.record/export');

        Route::post('space/:space_id/plugin/parse', 'plugin/parse');
        Route::post('space/:space_id/plugin/:id/config', 'plugin/config');
        Route::resource('space.plugin', 'plugin');

        Route::post('space/:space_id/member/invite', 'member.invite/save');
        Route::put('space/:space_id/member/invite', 'member.invite/update');

        Route::get('space/:space_id/member', 'member.index/index');
        Route::put('space/:space_id/member/:id', 'member.index/update');
        Route::delete('space/:space_id/member/:id', 'member.index/delete');

        Route::get('space/:space_id/billing', 'billing.index/index');
        Route::post('space/:space_id/billing/check', 'billing.index/check');
        Route::post('space/:space_id/billing/subscribe', 'billing.index/subscribe');
        Route::post('space/:space_id/billing/recharge', 'billing.index/recharge');
        Route::post('space/:space_id/billing/temporary-recharge', 'billing.index/temporaryRecharge');

        Route::get('space/:space_id/token', 'token/index');
        Route::post('space/:space_id/token', 'token/save');
        Route::delete('space/:space_id/token/:id', 'token/delete');

        Route::post('space/:space_id/generate/prompt', 'generate/prompt');

        Route::post('upload/:dir', 'upload/save');
    })->middleware(Authentication::class, 'mix');

    Route::group('chat', function () {
        Route::post('', 'index/save');
        Route::post('upload', 'index/upload');
        Route::get('config', 'index/config');
        Route::post('suggestion', 'index/suggestion');

        Route::post('speech', 'audio/speech');
        Route::post('transcriptions', 'audio/transcriptions');

        Route::get('conversation/recent', 'conversation/recent');
        Route::get('conversation', 'conversation/index');
        Route::delete('conversation/:id', 'conversation/delete');

        Route::get(':id', 'index/read');
    })->prefix('chat.');

    Route::get('auth/login/:channel', 'auth/login');
    Route::post('auth/register', 'auth/register');
    Route::get('auth/qrcode', 'auth/qrcode');
    Route::get('auth/check', 'auth/check');

    Route::any('webhook/:id/wkf', 'webhook/wkf');
    Route::any('webhook/:id/wmp', 'webhook/wmp');
    Route::any('webhook/:id/lark', 'webhook/lark');
    Route::any('webhook/:id/qq', 'webhook/qq');

    Route::any('webhook/order', 'webhook/order');

    Route::get('invite/:code', 'invite/read');
    Route::post('invite/:code', 'invite/save');
});

Route::get('avatar/plugin/:id', 'avatar/plugin');
Route::get('avatar/:hash', 'avatar/index');
